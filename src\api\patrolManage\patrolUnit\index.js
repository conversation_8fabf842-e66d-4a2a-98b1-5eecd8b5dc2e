// 单位列表
import http from '@/utils/apiRequestType';
import apiUrl from '@/utils/apiUrl';

const { defaultUrl } = apiUrl;

/**
 * @description: 单位列表
 * @param {object} params
 * @param {string} [params.deptName]
 * @return {object}
 */
export function getDeptList(params = {}) {
  return http.get(defaultUrl + '/ydxc/dept/getDeptList', params);
}
/**
 * @description: 单位详情
 * @param {object} params
 * @param {number} params.deptCode
 * @return {object}
 */
export function getDeptInfo(params) {
  return http.get(defaultUrl + '/ydxc/dept/getDeptInfo', params);
}

/**
 * @description: 人员信息
 * @param {object} params
 * @param {number} params.deptCode
 * @return {object}
 */
export function getUserList(params) {
  return http.get(defaultUrl + '/ydxc/user/getUserList', params);
}
