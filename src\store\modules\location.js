import Vue from 'vue';
import utils from '@/utils/utils';
import myNJS from '@/utils/myNJS';
import { getStorage, setStorage } from '@/utils/storage';
import { Dialog } from 'vant';
import { getCurrentPosition as getPosInfo } from '@/utils/location';

const _locationParams = {
  enableHighAccuracy: true,
  timeout: 600000,
  maximumAge: 0,
  geocode: false,
  coordsType: 'bd09ll',
  provider: 'baidu'
  // coordsType: "gcj02",  //火星坐标系（国测局）
  // provider: "amap"
  // coordsType: "wgs84", //系统自带坐标系
  // provider: "system"
};
//开启播放背景音乐
const openAudio = (state, type) => {
  let type1 = require('@/assets/images/audio/alice.mp3');
  let type2 = require('@/assets/images/audio/backstage.mp3');
  let url = type1;
  if (type == 'type1') {
    //无声音乐
    //
    url = type1;
  } else if (type == 'type2') {
    //后台提示音乐
    //
    url = type2;
  }
  if (plus.os.name == 'Android') {
    url = plus.io.convertLocalFileSystemURL(url);
  }
  //
  state.audioObj = plus.audio.createPlayer({
    loop: true,
    src: url
    // src: require("@/assets/audio/alice.mp3")
  });
  state.audioObj.play(
    function() {
      // 播放完成
      // stopAudio(state);
    },
    function(e) {}
  );
};
//停止播放背景音乐
const stopAudio = state => {
  // 操作播放对象
  if (state.audioObj) {
    state.audioObj.stop();
    state.audioObj = null;
  }
};
//火星坐标系转百度坐标系
let gcj02tobd09 = function(lng, lat) {
  //定义一些常量
  let x_PI = (3.14159265358979324 * 3000.0) / 180.0;
  let PI = 3.1415926535897932384626;
  let a = 6378245.0;
  let ee = 0.00669342162296594323;
  var lat = +lat;
  var lng = +lng;
  let z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI);
  let theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);
  let bd_lng = z * Math.cos(theta) + 0.0065;
  let bd_lat = z * Math.sin(theta) + 0.006;
  return [bd_lng, bd_lat];
};
const _errorFn = e => {
  // 定位错误提示
  let text = '其它未知错误导致无法获取位置信息';
  switch (e.code) {
    case e.PERMISSION_DENIED:
      text += '系统不允许程序获取定位功能';
      break;
    case e.POSITION_UNAVAILABLE:
      text += '无法获取有效的位置信息';
      break;
    case e.TIMEOUT:
      text += '无法在指定的时间内获取位置信息';
      break;
    case e.UNKNOWN_ERROR:
      text += '其它未知错误导致无法获取位置信息';
      break;
  }
  Vue.$userApp.toast.show({
    text,
    type: 'text'
  });
};

const _calcDistance = (latA, lonA, latB, lonB) => {
  // 根据两点坐标计算距离
  const earthR = 6371000;
  const PI = 3.14159265358979324;
  const x = Math.cos((latA * PI) / 180) * Math.cos((latB * PI) / 180) * Math.cos(((lonA - lonB) * PI) / 180);
  const y = Math.sin((latA * PI) / 180) * Math.sin((latB * PI) / 180);
  let s = x + y;
  if (s > 1) s = 1;
  if (s < -1) s = -1;
  const alpha = Math.acos(s);
  const distance = (alpha * earthR).toFixed(2);
  return Number(distance);
};

const state = {
  g_wakelock: null,
  BMap: null,
  map: null,
  center: { lng: 113.384429, lat: 23.35814 },
  currentPosition: {},
  watchId: null,
  polylinePath: [], // 实时路径坐标点集合
  distance: 0,
  currentTime: '00:00:00',
  curTimer: null,
  isOpenGps: true, // 是否有打开GPS定位
  createdTime: '',
  patrolKey: '',
  interval: 10000, // 记录点位的间隔时间（毫秒）
  isCheckGps: false, // 用于检查巡查开始时是否用了GPS定位，避免还没GPS信号情况下用了网络定位误差大,
  version: '',
  isOtherBackground: false, //判断是拍照上传触发的切后台还是真正切后台
  // fromPath: "", // 判断是什么页面跳转到巡查页面
  audioObj: null, //巡查后台音乐播放对象,
  // 当前位置信息
  currentPosInfo: null
};

const mutations = {
  setMap(state, { BMap, map }) {
    state.BMap = BMap;
    state.map = map;
  },
  setPosition(state, data) {
    state.center = data;
    state.currentPosition = data;
  },
  setCenter(state, data) {
    state.center = data;
  },
  setInterval(state, data) {
    state.interval = data;
  },
  setCheckGps(state, data) {
    state.isCheckGps = data;
  },
  setVersion(state, data) {
    state.version = data;
  },
  setOtherBackgroundState(state, data) {
    state.isOtherBackground = data;
  },
  // setFromPath(state, data) {
  //   state.fromPath = data;
  // },
  resetLocation(state) {
    //停止播放背景音乐
    stopAudio(state);

    // 停止定位
    if (state.watchId) {
      window.plus.geolocation.clearWatch(state.watchId);
      state.watchId = null;
    }
    state.polylinePath = [];
    state.distance = 0;

    if (state.curTimer) {
      clearInterval(state.curTimer);
      state.curTimer = null;
    }
    state.currentTime = '00:00:00';

    if (state.g_wakelock && state.g_wakelock.isHeld()) {
      //
      state.g_wakelock.release();
      state.g_wakelock = null;
    }

    if (window.plus) {
      window.plus.device.setWakelock(false);

      if (plus.os.name == 'Android') {
        // 清除常驻通知
        myNJS.aOSReceive();
      }

      // if (plus.os.name == "iOS") {
      //   // 关掉屏幕常亮
      //   const UIApplication = plus.ios.import("UIApplication");
      //   const app = UIApplication.sharedApplication();
      //   app.setIdleTimerDisabled('NO');
      //   plus.ios.deleteObject(app);
      // }
    }

    if (state.isCheckGps) {
      Vue.$userApp.loading.hide();
    }

    //
  },
  getPosition(state) {
    console.log(230, '已调用');

    // if (state.watchId) {
    //   return;
    // }
    // 通过iframe挂载不能使用plus
    // if (!window.plus.geolocation) {
    //   Vue.$userApp.toast.show({
    //     text: "您的设备不支持获取地理位置",
    //     type: "text"
    //   });
    //   return;
    // } else
    if (!navigator.geolocation) {
      Vue.$userApp.toast.show({
        text: '您的设备不支持获取地理位置',
        type: 'text'
      });
      return;
    }
    const LocalCity = new BMap.LocalCity();
    LocalCity.get(res => {});
    // const successFn = (position) => {

    //   let { longitude: lng, latitude: lat } = position.coords;
    //   const pointBak = new BMap.Point(lng, lat);
    //   const convertor = new BMap.Convertor();
    //   convertor.translate([pointBak], 1, 5, (resPoint) => {
    //     if (resPoint && resPoint.points && resPoint.points.length > 0) {
    //       lng = resPoint.points[0].lng;
    //       lat = resPoint.points[0].lat;
    //     }
    //     const point = new BMap.Point(lng, lat);
    //     const geo = new BMap.Geocoder();
    //     geo.getLocation(point, (res) => {
    //       state.center = { lng: res.point.lng, lat: res.point.lat };
    //       state.currentPosition = { lng: res.point.lng, lat: res.point.lat };
    //     })
    //   });
    // };
    //#region 方案作废
    // if (plus.os.name == "iOS") {
    //IOS采用国测局坐标， 获取到定位后再转为百度坐标系， 兼容IOS手机有时候定位返回的坐标系错乱
    // _locationParams.coordsType = "gcj02"  //火星坐标系（国测局）
    // _locationParams.provider = "amap"
    // }
    // window.plus.geolocation.getCurrentPosition(successFn, _errorFn, _locationParams);
    //#endregion

    //#region 备用方案
    navigator.geolocation.getCurrentPosition(
      function(position) {
        let lat = position.coords.latitude; // 获取纬度
        let lng = position.coords.longitude; // 获取经度
        // 在地图上显示位置信息，或者发送到服务器进行处理
      },
      function(error) {
        // 处理定位错误
      },
      {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0
      }
    );
    const geolocation = new BMap.Geolocation();
    geolocation.getCurrentPosition(
      function(res) {
        if (this.getStatus() == BMAP_STATUS_SUCCESS) {
          // alert(JSON.stringify(r))

          let { longitude: lng, latitude: lat } = res;
          const pointBak = new BMap.Point(lng, lat);
          const convertor = new BMap.Convertor();
          // convertor.translate([pointBak], 1, 5, (resPoint) => {
          //
          //   if (resPoint && resPoint.points && resPoint.points.length > 0) {
          //     lng = resPoint.points[0].lng;
          //     lat = resPoint.points[0].lat;
          //   }
          // });
          const point = new BMap.Point(lng, lat);
          const geo = new BMap.Geocoder();
          geo.getLocation(point, res => {
            console.log('res: ', res);
            state.center = { lng: res.point.lng, lat: res.point.lat };
            state.currentPosition = { lng: res.point.lng, lat: res.point.lat, locationName: res.address };
          });
        } else {
          // alert('failed' + this.getStatus());
        }
      },
      { enableHighAccuracy: true }
    );
    // navigator.geolocation.getCurrentPosition(
    //   successFn
    // );
    //#endregion
  },
  startPatrol(state, { createdTime, patrolKey }) {
    if (!window.plus.geolocation) {
      Vue.$userApp.toast.show({
        text: '您的设备不支持获取地理位置',
        type: 'text'
      });
      return;
    }
    //开启后台无声背景音乐播放
    openAudio(state, 'type1');

    state.createdTime = createdTime;
    state.patrolKey = patrolKey;

    if (patrolKey) {
      let patrolInfoCache = getStorage(patrolKey);
      if (patrolInfoCache) {
        // patrolInfoCache = JSON.parse(patrolInfoCache);
        state.polylinePath = patrolInfoCache.polylinePath;

        let distance = 0;
        patrolInfoCache.polylinePath.reduce((acc, next) => {
          let dis = _calcDistance(acc.lat, acc.lng, next.lat, next.lng);
          distance += dis;

          return next;
        });
        state.distance = distance;
      }
    }

    let aMain = null;
    let aContext = null;
    let aMainSvr = null;
    let aLocationManager = null;
    if (window.plus.os.name == 'Android') {
      aMain = window.plus.android.runtimeMainActivity();
      aContext = window.plus.android.importClass('android.content.Context');
      aMainSvr = aMain.getSystemService(aContext.LOCATION_SERVICE);
      aLocationManager = window.plus.android.importClass('android.location.LocationManager');
    }

    let curTime = 0;
    let preTime = 0;
    let debugTime = +new Date(createdTime);
    const successFn = position => {
      let { longitude: lng, latitude: lat, accuracy } = position.coords;
      //
      if (window.plus.os.name == 'iOS') {
        let trans = gcj02tobd09(lng, lat);
        lng = trans[0];
        lat = trans[1];
      }
      //

      if (state.isCheckGps && window.plus.os.name == 'Android') {
        let pos = aMainSvr.getLastKnownLocation(aLocationManager.GPS_PROVIDER);
        if (!pos) {
          // 监测GPS定位有没有数据，最多检查20秒
          if (+new Date() - debugTime < 20000) {
            Vue.$userApp.loading.show('正在搜索GPS');
            return;
          }
        }

        state.isCheckGps = false;
        state.interval = 5000;
        Vue.$userApp.loading.hide();
      }
      if (accuracy >= 100 || accuracy === 0) {
        return;
      }

      curTime = position.timestamp;
      if (!preTime) {
        preTime = position.timestamp;
      }
      if (curTime - preTime < state.interval) {
        // 每5秒保存一次坐标
        return;
      }
      state.interval = 5000;

      const time = utils.formatDate(position.timestamp, 'HH:mm:ss');
      state.center = { lng, lat };
      state.currentPosition = { lng, lat };
      if (state.polylinePath.length) {
        const prePoint = state.polylinePath[state.polylinePath.length - 1];
        const dis = _calcDistance(prePoint.lat, prePoint.lng, lat, lng);

        const maxDis = ((position.timestamp - new Date(prePoint.time.replace(/-/g, '/')).getTime()) / 1000) * 33;
        if (dis < 5 || dis > maxDis) {
          // 如果定位跟上次定位距离相差小于5米或者时速超过120公里就不记录了
          return;
        }
        state.distance = +state.distance + dis;
      }
      state.polylinePath.push({
        lng,
        lat,
        accuracy: accuracy.toFixed(0),
        time
      });
      preTime = curTime;

      if (patrolKey) {
        const patrolInfoCache = {
          polylinePath: state.polylinePath,
          distance: state.distance
        };
        setStorage(patrolKey, patrolInfoCache);
      }

      // const pointBak = new state.BMap.Point(lng, lat);
      // const convertor = new state.BMap.Convertor();
      // convertor.translate([pointBak], 1, 5, function(resPoint) {
      //   if (resPoint.status === 0) {
      //     lng = resPoint.points[0].lng;
      //     lat = resPoint.points[0].lat;
      //   }

      //   state.center = { lng, lat };
      //   state.currentPosition = { lng, lat };
      //   if (state.polylinePath.length) {
      //     const prePoint = state.polylinePath[state.polylinePath.length - 1];
      //     const dis = _calcDistance(prePoint.lat, prePoint.lng, lat, lng);
      //
      //     if (dis < 0.1) {
      //       // 如果定位跟上次定位距离相差小于0.1米就不记录了
      //       return;
      //     }
      //     state.distance = +state.distance + dis;
      //   }
      //   state.polylinePath.push({ lng, lat, accuracy });
      //   preTime = curTime;

      //   if (patrolKey) {
      //     const patrolInfoCache = {
      //       polylinePath: state.polylinePath,
      //       distance: state.distance
      //     };
      //     setStorage(patrolKey, patrolInfoCache);
      //   }
      // });
    };

    if (createdTime) {
      state.curTimer = setInterval(() => {
        const diffTime = new Date().getTime() - createdTime;
        state.currentTime = utils.formatTime(diffTime, 'H:M:S');
      }, 1000);
    }
    // if (plus.os.name == "iOS") { //IOS采用国测局坐标， 获取到定位后再转为百度坐标系， 兼容IOS手机有时候定位返回的坐标系错乱
    // 	_locationParams.coordsType = "gcj02"  //火星坐标系（国测局）
    // 	_locationParams.provider = "amap"
    // }
    state.watchId = window.plus.geolocation.watchPosition(successFn, _errorFn, _locationParams);

    window.plus.device.setWakelock(true);
    if (window.plus.os.name == 'Android') {
      // myNJS.aOSNotify("巡查进行中", "请保持应用持续运行");
      myNJS.aOSNotify('巡查进行中', '切换到后台将导致巡查轨迹不准确，请保持青山嘴水库程序在前台运行');

      let main = window.plus.android.runtimeMainActivity();
      let Context = window.plus.android.importClass('android.content.Context');
      let PowerManager = window.plus.android.importClass('android.os.PowerManager');
      let pm = main.getSystemService(Context.POWER_SERVICE);
      state.g_wakelock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, 'ANY_NAME');
      state.g_wakelock.acquire();
    }
    if (window.plus.os.name == 'iOS') {
      // // 屏幕常亮
      // const UIApplication = plus.ios.import("UIApplication");
      // const app = UIApplication.sharedApplication();
      // app.setIdleTimerDisabled('YES');
      // plus.ios.deleteObject(app);
      // const Notification = plus.ios.newObject("UILocalNotification");
      // plus.ios.deleteObject(Notification);

      const cllocationManger = window.plus.ios.newObject('CLLocationManager');
      const UIDevice = window.plus.ios.import('UIDevice');
      const dev = UIDevice.currentDevice();
      const version = dev.systemVersion();
      const firstVersion = Number(version.split('.')[0]);
      if (firstVersion >= 8) {
        cllocationManger.requestAlwaysAuthorization();
      }
      if (firstVersion >= 9) {
        // plus.ios.invoke(cllocationManger,'allowsBackgroundLocationUpdates:','YES')
        cllocationManger.setAllowsBackgroundLocationUpdates('YES');
      }
      // plus.ios.invoke(cllocationManger,'pausesLocationUpdatesAutomatically:','NO')
      cllocationManger.setPausesLocationUpdatesAutomatically('NO');

      // cllocationManger.setDelegate('self')
      // cllocationManger.setDesiredAccuracy('kCLLocationAccuracyBest')
      // cllocationManger.setDistanceFilter('kCLDistanceFilterNone')
      // cllocationManger.requestWhenInUseAuthorization()
      // const locDelegate = plus.ios.implements("CLLocationManagerDelegate", {
      //   "cllocationManger:didUpdateLocations:": (manager, locations) => {
      //
      //   },
      //   "cllocationManger:didFailWithError:": (manager, err) => {
      //
      //   },
      // });
      // cllocationManger.setDelegate(locDelegate);
      // cllocationManger.startUpdatingLocation();

      window.plus.ios.deleteObject(cllocationManger);
      window.plus.ios.deleteObject(UIDevice);
    }
  },
  setGpsState(state, data) {
    state.isOpenGps = data;
  },
  changeAudio(state, type) {
    //开启后台无声背景音乐播放
    openAudio(state, type);
  },
  //通过经纬度逆解析坐标信息
  getLocation(state, pt) {
    let PT = state.currentPosition;

    if (pt) {
      PT = pt;
    }
    let myGeo = new state.BMap.Geocoder();
    PT = new BMap.Point(PT.lng, PT.lat);
    myGeo.getLocation(PT, res => {
      return res;
    });
  },
  // 记录当前位置信息
  setPosInfo(state, data) {
    state.currentPosInfo = data;
  }
};

const actions = {
  // 检查是否开了通知
  checkNotifyByAndroid() {
    return new Promise((res, rej) => {
      let main = window.plus.android.runtimeMainActivity();
      let pkName = main.getPackageName();
      let NotificationManagerCompat = window.plus.android.importClass('androidx.core.app.NotificationManagerCompat');
      let packageNames = NotificationManagerCompat.from(main);

      if (packageNames && !packageNames.areNotificationsEnabled()) {
        Dialog.alert({
          title: '提示',
          message: '请允许应用发送通知！'
        }).then(() => {
          try {
            let uid = main.getApplicationInfo().plusGetAttribute('uid');
            let Intent = window.plus.android.importClass('android.content.Intent');
            let Build = window.plus.android.importClass('android.os.Build');
            let intent = '';
            //android 8.0引导
            if (Build.VERSION.SDK_INT >= 26) {
              intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
              intent.putExtra('android.provider.extra.APP_PACKAGE', pkName);
            } else if (Build.VERSION.SDK_INT >= 21) {
              //android 5.0-7.0
              intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
              intent.putExtra('app_package', pkName);
              intent.putExtra('app_uid', uid);
            } else {
              //(<21)其他--跳转到该应用管理的详情页
              let Settings = window.plus.android.importClass('android.provider.Settings');
              let Uri = window.plus.android.importClass('android.net.Uri');
              intent = new Intent();
              intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
              let uri = Uri.fromParts('package', main.getPackageName(), null);
              intent.setData(uri);
            }
            // 跳转到该应用的系统通知设置页
            main.startActivity(intent);
            setTimeout(() => {
              res();
            }, 1000);
          } catch {
            rej();
          }
        });
      } else {
        res();
      }
    });
  },
  // 检查后台定位权限
  checkPermissionByAndroid({ state }) {
    if (state.watchId) {
      return;
    }
    return new Promise((res, rej) => {
      try {
        const Build = window.plus.android.importClass('android.os.Build');
        if (Build.VERSION.SDK_INT >= 28) {
          const main = window.plus.android.runtimeMainActivity();
          if (main.checkSelfPermission('android.permission.FOREGROUND_SERVICE') === -1) {
            window.plus.android.requestPermissions(['android.permission.FOREGROUND_SERVICE']);
          }

          if (Build.VERSION.SDK_INT >= 29) {
            if (main.checkSelfPermission('android.permission.ACCESS_BACKGROUND_LOCATION') === -1) {
              window.plus.android.requestPermissions(['android.permission.ACCESS_BACKGROUND_LOCATION']);
            }
          }
        }

        setTimeout(() => {
          res();
        });
      } catch {
        rej();
      }
    });
  },
  // 检查是否限制后台电量
  checkPowerByAndroid(context, callback) {
    const Build = window.plus.android.importClass('android.os.Build');
    if (Build.VERSION.SDK_INT < 24) {
      return;
    }
    return new Promise((res, rej) => {
      // 电源白名单
      const main = window.plus.android.runtimeMainActivity();
      const packName = main.getPackageName();
      const Context = window.plus.android.importClass('android.content.Context');
      const PowerManager = window.plus.android.importClass('android.os.PowerManager');
      const pm = main.getSystemService(Context.POWER_SERVICE);

      if (!pm.isIgnoringBatteryOptimizations(packName)) {
        if (callback) {
          callback();
          res();
        } else {
          Dialog.alert({
            title: '提示',
            message: '为了更准确记录巡查轨迹，请允许应用始终在后台运行！'
          }).then(() => {
            try {
              const Settings = window.plus.android.importClass('android.provider.Settings');
              // 直接设置白名单，部分手机没法直接设置
              // const Uri = plus.android.importClass("android.net.Uri");
              // const packageURI = Uri.parse("package:" + packName);
              // const intents = plus.android.newObject("android.content.Intent", Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS,packageURI);  // 电池
              // 跳转电源优化名单列表，选对应应用设置
              const Intent = window.plus.android.importClass('android.content.Intent');
              const intents = new Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS);
              if (intents.resolveActivity(main.getPackageManager()) != null) {
                main.startActivity(intents);
              }

              setTimeout(() => {
                res();
              }, 1000);
            } catch {
              rej();
            }
          });
        }
      } else {
        res();
      }
    });
  },
  // 检查是否开了定位
  checkGPSByAndroid({ commit }, message = '请开启位置服务，否则将无法记录巡查轨迹信息！') {
    return new Promise((res, rej) => {
      const context = window.plus.android.importClass('android.content.Context');
      const locationManager = window.plus.android.importClass('android.location.LocationManager');
      const main = window.plus.android.runtimeMainActivity();
      const mainSvr = main.getSystemService(context.LOCATION_SERVICE);
      if (!mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER)) {
        commit('setGpsState', false);
        Dialog.alert({
          title: '提示',
          message
        }).then(() => {
          try {
            const Intent = window.plus.android.importClass('android.content.Intent');
            const Settings = window.plus.android.importClass('android.provider.Settings');
            const intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
            main.startActivity(intent); // 打开系统设置GPS服务页面
            setTimeout(() => {
              res();
            }, 1000);
          } catch {
            rej();
          }
        });
      } else {
        commit('setGpsState', true);
        res();
      }
    });
  },
  // 检查是否开了通知
  checkNotifyByIos({ state }) {
    if (state.watchId) {
      return;
    }
    return new Promise((res, rej) => {
      const UIApplication = window.plus.ios.import('UIApplication');
      const app = UIApplication.sharedApplication();
      let enabledTypes = 0;
      if (app.currentUserNotificationSettings) {
        const settings = app.currentUserNotificationSettings();
        enabledTypes = settings.plusGetAttribute('types');
      } else {
        //针对低版本ios系统
        enabledTypes = app.enabledRemoteNotificationTypes();
      }
      window.plus.ios.deleteObject(app);
      if (0 == enabledTypes) {
        Dialog.alert({
          title: '提示',
          message: '请允许应用发送通知！'
        }).then(() => {
          try {
            const UIApplication = window.plus.ios.import('UIApplication');
            const NSURL = window.plus.ios.import('NSURL');
            const setting = NSURL.URLWithString('app-settings:');
            const application = UIApplication.sharedApplication();
            application.openURL(setting);
            window.plus.ios.deleteObject(setting);
            window.plus.ios.deleteObject(application);
            setTimeout(() => {
              res();
            }, 1000);
          } catch {
            rej();
          }
        });
      } else {
        res();
      }
    });
  },
  // 检查是否开了定位
  checkGPSByIos({ commit }, message = '请开启位置服务，否则将无法记录巡查轨迹信息！') {
    return new Promise((res, rej) => {
      const cllocationManger = window.plus.ios.import('CLLocationManager');
      const enable = cllocationManger.locationServicesEnabled();
      const status = cllocationManger.authorizationStatus();
      window.plus.ios.deleteObject(cllocationManger);
      if (!(enable && status != 2)) {
        commit('setGpsState', false);
        Dialog.alert({
          title: '提示',
          message
        }).then(() => {
          try {
            const UIApplication = plus.ios.import('UIApplication');
            const application2 = UIApplication.sharedApplication();
            const NSURL2 = plus.ios.import('NSURL');
            const setting2 = NSURL2.URLWithString('App-Prefs:root=Privacy&path=LOCATION');
            application2.openURL(setting2);
            window.plus.ios.deleteObject(setting2);
            window.plus.ios.deleteObject(NSURL2);
            window.plus.ios.deleteObject(application2);
            setTimeout(() => {
              res();
            }, 1000);
          } catch {
            rej();
          }
        });
      } else {
        commit('setGpsState', true);
        res();
      }
    });
  },

  // 获取当前位置信息
  getCurPosInfo({ commit, state }, callback = () => {}) {
    if (state.currentPosInfo && state.currentPosInfo.time) {
      const diffTime = new Date().getTime() - state.currentPosInfo.time;
      // 距离上次获取位置信息小于20分钟，则不再获取
      if (diffTime < 1000 * 60 * 20) {
        callback();
        return;
      }
    }

    getPosInfo(position => {
      commit('setPosInfo', { ...position, time: new Date().getTime() });
      callback();
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
