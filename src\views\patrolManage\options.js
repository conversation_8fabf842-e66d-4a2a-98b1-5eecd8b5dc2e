export function formatOptions(code, options) {
  const option = options.find(op => op.value === code);
  if (code) {
    return option.label;
  }
  return code;
}

export const typeOptions = [
  {
    label: '日常',
    value: 1
  },
  {
    label: '定期',
    value: 2
  },
  {
    label: '特别',
    value: 3
  }
];

export const statusOptions = [
  {
    label: '执行中',
    value: 1
  },
  {
    label: '已完成',
    value: 2
  }
];

export const dangerOptions = [
  {
    label: '无隐患',
    value: 1
  },
  {
    label: '有隐患',
    value: 2
  }
];

export const situationOptions = [
  {
    label: '正常',
    value: 1
  },
  {
    label: '异常',
    value: 2
  }
];

export const handingOptions = [
  {
    label: '待处理',
    value: 0
  },
  {
    label: '已处理',
    value: 1
  },
  {
    label: '处理中',
    value: 2
  }
];
