// 天气转换中文，打包成app图片不能带中文
export const weatherConfig = [
  {
    value: 'baoyu',
    label: '暴雨',
    img: require('@/assets/images/weather/baoyu.png')
  },
  {
    value: 'daxue',
    label: '大雪',
    img: require('@/assets/images/weather/daxue.png')
  },
  {
    value: 'dayu',
    label: '大雨',
    img: require('@/assets/images/weather/dayu.png')
  },
  {
    value: 'duoyun',
    label: '多云',
    img: require('@/assets/images/weather/duoyun.png')
  },
  {
    value: 'leizhenyu',
    label: '雷阵雨',
    img: require('@/assets/images/weather/leizhenyu.png')
  },
  {
    value: 'mai',
    label: '霾',
    img: require('@/assets/images/weather/mai.png')
  },
  {
    value: 'qing',
    label: '晴',
    img: require('@/assets/images/weather/qing.png')
  },
  {
    value: 'shachenbao',
    label: '沙尘暴',
    img: require('@/assets/images/weather/shachenbao.png')
  },
  {
    value: 'wu',
    label: '雾',
    img: require('@/assets/images/weather/wu.png')
  },
  {
    value: 'xiaoxue',
    label: '小雪',
    img: require('@/assets/images/weather/xiaoxue.png')
  },
  {
    value: 'xiaoyu',
    label: '小雨',
    img: require('@/assets/images/weather/xiaoyu.png')
  },
  {
    value: 'yin',
    label: '阴',
    img: require('@/assets/images/weather/yin.png')
  },
  {
    value: 'zhenyu',
    label: '阵雨',
    img: require('@/assets/images/weather/zhenyu.png')
  },
  {
    value: 'zhongxue',
    label: '中雪',
    img: require('@/assets/images/weather/zhongxue.png')
  },
  {
    value: 'zhongyu',
    label: '中雨',
    img: require('@/assets/images/weather/zhongyu.png')
  }
];
