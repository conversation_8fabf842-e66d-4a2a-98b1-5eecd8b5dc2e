import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import './router/permission';

//ajax请求
import appAjax from './appPlugins/appApiRequest';
//apiUrl地址
import appApiUrl from './appPlugins/appApiUrl';
//全站工具类包括了正则验证、页面title、json转字符串、字符串转json
import appUtils from './appPlugins/appUtils';
//loading组件
import appLoading from './appPlugins/appLoading';
import appToast from './appPlugins/appToast';
import globalComponents from '@/components/index';
import dayjs from 'dayjs';
import 'default-passive-events';

// import '@/assets/fonts/font.scss';
import '@/style/vant.scss';

import * as echarts from 'echarts';
Vue.prototype.$echarts = echarts;
Vue.prototype.$dayjs = dayjs;

// 引入输入框表情过滤
import emoji from './utils/emoji';
Vue.directive('emoji', emoji);

// 引入全局自定义指令
import directive from './directives';
Vue.use(directive);

// 引入vant组件
import './utils/vant-ui';
import { Toast } from 'vant';
Vue.prototype.$toast = Toast;

// 引入全局样式
// import './style/index.scss';

Vue.config.productionTip = false;

Vue.use(globalComponents);
Vue.use(appAjax);
Vue.use(appApiUrl);
Vue.use(appUtils);
Vue.use(appLoading);
Vue.use(appToast);

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app');
