<template>
  <div class="tab-bar" :class="{ 'tab-bar-padding-bottom': hasTabBar, 'tab-bar-has-nav': isApp }">
    <keep-alive>
      <router-view v-if="$route.meta.isKeepAlive" v-slot="{ Component }">
        <component :is="Component" />
      </router-view>
    </keep-alive>
    <router-view v-if="!$route.meta.isKeepAlive" v-slot="{ Component }">
      <component :is="Component" />
    </router-view>
    <van-tabbar v-if="hasTabBar" v-model="activeIndex" :route="true">
      <van-tabbar-item v-for="(item, index) in tabBarList" :key="item.label" :to="item.to" :replace="true">
        <span>{{ item.label }}</span>
        <template #icon="props">
          <img class="tab-icon" :src="props.active ? item.actIcon : item.icon" alt="bar图标" />
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
export default {
  name: 'CustomTabBar',
  components: {},
  props: {
    active: {
      type: Number,
      default: 0
    },
    tabBarList: {
      type: Array,
      default: () => [
        // {
        //   id: '1',
        //   label: '汛前简报',
        //   icon: require('@/assets/images/tabBar/before-flood.png'),
        //   actIcon: require('@/assets/images/tabBar/before-flood-act.png'),
        //   to: {
        //     name: 'Test1',
        //     query: {
        //       id: '123'
        //     }
        //   }
        // },
        // {
        //   id: '2',
        //   label: '防汛值班',
        //   icon: require('@/assets/images/tabBar/before-flood.png'),
        //   actIcon: require('@/assets/images/tabBar/before-flood-act.png'),
        //   to: {
        //     name: 'Test2'
        //   }
        // }
      ]
    }
  },
  data() {
    return {
      isApp: false
    };
  },
  computed: {
    activeIndex: {
      set(val) {
        return val;
      },
      get() {
        return this.active;
      }
    },
    hasTabBar() {
      return this.$route.meta.hasTabBar !== false;
    }
  },
  watch: {},
  created() {
    this.isApp = process.env.NODE_ENV === 'app';
  },
  mounted() {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.tab-bar {
  box-sizing: border-box;

  // height: calc(100% - 92px); // 有nav-bar
  height: 100%; // 粤政易，无nav-bar

  // overflow-y: auto;
}
.tab-bar-padding-bottom {
  padding-bottom: 100px;
}
.tab-bar-has-nav {
  height: calc(100% - 92px);
}
.tab-icon {
  width: 40px;
  height: 40px;
}
</style>
