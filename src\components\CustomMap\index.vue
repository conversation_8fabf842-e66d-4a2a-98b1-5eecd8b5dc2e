<template>
  <div class="map-container">
    <iframe
      ref="mapRef"
      class="map-iframe"
      title="map"
      width="100%"
      height="100%"
      :src="$appApiUrl.mapUrl + '/#/map'"
      @load="mapOnload"
    ></iframe>

    <MapControl
      v-if="showControl"
      v-model="baseLayer"
      @switchBaseMap="switchBaseMap"
      @flyToCoors="flyToCoors"
      @initView="initView"
      @controlZoom="controlZoom_2d"
    />
  </div>
</template>

<script>
import MapControl from './Control.vue';
export default {
  name: 'CustomMap',
  components: {
    MapControl
  },
  props: {
    showControl: {
      type: Boolean,
      default: true
    }
  },
  data() {
    const mapOriginUrl = this.$appApiUrl.mapUrl.replace('/stzhsl/map', '');
    return {
      mapOriginUrl,
      mapReady: false,
      eventList: [],
      baseLayer: '矢量图'
    };
  },
  watch: {
    mapReady: {
      immediate: true,
      handler(newVal) {
        if (newVal && this.eventList.length) {
          this.eventList.forEach(event => event());
          this.eventList = [];
        }
      }
    }
  },
  created() {
    window.addEventListener('message', this.mapReceiveMessage);
  },
  mounted() {},
  beforeDestroy() {
    window.removeEventListener('message', this.mapReceiveMessage);
  },
  methods: {
    mapOnload() {
      this.mapReady = true;
      this.$emit('mapReady');
    },
    mapReceiveMessage(e) {
      if (e.origin !== this.mapOriginUrl) {
        return;
      }
      if (e.data && typeof e.data === 'string') {
        const { val, method } = JSON.parse(e.data);
        this.$emit('mapClick', val, method);
      }
    },
    mapPostMessage(method, val) {
      const event = () => {
        this.$refs.mapRef.contentWindow.postMessage(
          JSON.stringify({
            method,
            val
          }),
          this.mapOriginUrl
        );
      };

      if (this.mapReady) {
        event();
      } else {
        this.eventList.push(event);
      }
    },
    /**
     * @description 切换底图, 传中文名
     * @param {'影像图' | '矢量图' | '地形图'} baseLayer
     * @return void
     */
    switchBaseMap(baseLayer) {
      this.baseLayer = baseLayer;
      this.mapPostMessage('switchBaseMap', { baseLayer });
    },
    /**
     * @description 缩放到特定范围
     * @param {Object} scope
     * @param {number} scope.east
     * @param {number} scope.west
     * @param {number} scope.north
     * @param {number} scope.south
     * @return void
     */
    zoomByExtent(scope) {
      this.mapPostMessage('zoomByExtent', scope);
    },
    /**
     * @description: 恢复默认范围
     */
    initView() {
      this.mapPostMessage('initView');
    },
    /**
     * @description 二维地图缩放
     * @param {boolean=} [isIncrease = true]
     * @return void
     */
    controlZoom_2d(isIncrease = true) {
      this.mapPostMessage('controlZoom_2d', { isIncrease });
    },
    /**
     * @description 地图加载多个点
     * @param {Object} data 传入的参数对象
     * @param {boolean} data.isVisible 是否可见
     * @param {string} data.layerName 图层名
     * @param {Object[]} data.ptArr 点数据数组
     * @param {number} data.ptArr[].lng 经度
     * @param {number} data.ptArr[].lat 纬度
     * @param {string} data.ptArr[].Name 显示名称
     * @param {string} data.ptArr[].iconUrl 图标url
     * @param {number} data.ptArr[].scale 缩放
     * @param {boolean} data.ptArr[].isAnimate 是否有动画
     * @param {number} data.ptArr[].labelZoom 标签缩放
     * @param {number} data.ptArr[].pointZoom 点缩放
     * @param {string} data.lngFieldName 引用的经度字段
     * @param {string} data.latFieldName 引用的纬度字段
     * @param {string} data.labelFieldName 引用的名称字段
     * @param {Object} data.styleConfig 样式配置
     * @param {number} data.styleConfig.zIndex 样式层级
     * @return void
     */
    setArrPtLayerVisible(data) {
      this.mapPostMessage('setArrPtLayerVisible', data);
    },
    /**
     * @description 缩放到图层
     * @param {string} layerName 图层名
     * @return void
     */
    zoomToLayer(layerName) {
      this.mapPostMessage('zoomToLayer', { layerName });
    },
    /**
     * @description 定位
     * @param {string | number} lng
     * @param {string | number} lat
     * @return void
     */
    flyToCoors(lng, lat) {
      this.mapPostMessage('flyToCoors', { lng, lat });
    },
    /**
     * @description 显示单个弹窗
     * @param {Object} data
     * @param {string} data.domUrl
     * @param {string | number} data.lat
     * @param {string | number} data.lng
     * @param {number} data.isZoom
     * @return void
     */
    showPop(data) {
      this.mapPostMessage('showPop', data);
    },
    /**
     * @description 关闭单个弹窗
     * @return void
     */
    closePop() {
      this.mapPostMessage('closePop');
    },
    /**
     * @description 显示/关闭 多个弹窗
     * @param {Object} data
     * @param {'add' | 'remove'} data.optType
     * @param {{domUrl: string, lng: string | number, lat: string | number, code: string}[]} data.data
     * @return void
     */
    togglePopList(data) {
      this.mapPostMessage('togglePopList', data);
    },
    /**
     * @description 加载geojson
     * @param {Object} data
     * @param {string} data.idf
     * @param {'showLayer'|'flyToLayer'|'destroyLayer'|'flyToCode'|'flashByCode'|'getCenterByCode'} data.optType
     * 若showLayer时，都要传idf, features, labelField, style
     * 若flyToLayer | destroyLayer时，只传idf
     * 若flyToCode | flashByCode | getCenterByCode时，只传idf, code, flyField
     * @param {array}  data.features geo的features数据数组，可选，其中optType = showLayer时，必传
     * @param {string} data.labelField label字段，选填
     * @param {string} data.flyField 飞向区域的字段，选填
     * @param {string} data.code code，选填
     * @param {Object} data.style 样式，选填，不传默认
     * @param {boolean} data.style.outline 是否有轮廓线，选填
     * @param {string} data.style.outlineColor 轮廓线颜色，选填
     * @param {string} data.style.outlineWidth 轮廓线大小，选填
     * @param {boolean} data.style.fill 是否填充，选填
     * @param {string} data.style.fillColor 填充颜色，选填
     * @param {string} data.style.labelColor label颜色，选填
     * @param {number} data.style.labelZoom label缩放，选填
     * @return void
     */
    toggleGeoJson(data) {
      this.mapPostMessage('toggleGeoJson', data);
    },
    /**
     * @description: 按行政区划统计专题
     * @param {Object} data 统计专题数据
     * @param {string} data.type 添加类型，必传，可选值：add | hide | show | initView
     * @param {Object[]} data.dataArr 行政区划数据,可选，其中type = add，必传
     * @param {number} data.dataArr[].lng 经度
     * @param {number} data.dataArr[].lat 纬度
     * @param {number} data.dataArr[].dscd 行政区划编码
     * @param {string} data.areaField 行政区划字段
     * @param {number} data.areaType 行政区划类型，0省级，1市级，2县级
     * @return void
     */
    toggleXzqhTopic(data) {
      this.mapPostMessage('toggleXzqhTopic', data);
    },
    /**
     * @description 巡查
     * @param {Object} data
     * @param {'init'|'play'|'replay'|'stop'|'clear'} data.type
     * @param {[lng: string | number, lat: string | number][]} data.routeArr
     * @return void
     */
    togglePatrol(data) {
      this.mapPostMessage('togglePatrol', data);
    },
    /**
     * @description: 点击地图拾取经纬度
     * @param {boolean} isPick 是否开启拾取
     */
    pickCoorControl(isPick) {
      this.mapPostMessage('pickCoorControl', { isPick });
    }
  }
};
</script>

<style lang="scss" scoped>
.map-container {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  .map-iframe {
    border: none;
  }
}
</style>
