<template>
  <div class="river-warning-box">
    <ul class="river-list">
      <li class="river-item flx" v-for="item in tableData" :key="item.sectionCode">
        <div class="type-info flx-center" :class="['orange-bg']">
          <img src="@/assets/images/warning/type-dm.png" />
          <span>断面</span>
        </div>

        <div class="main-info">
          <div class="flx-align-center" style="margin-bottom: 4px">
            <div class="site-name" :style="{ color: '#ffd060' }">
              {{ item.sectionName }}
            </div>
            <div class="data-time">{{ item.forecastMaxQTime }}</div>
          </div>

          <div class="info-content flx">
            <div class="content-item">
              <div class="label">最高水位/警戒差(m)</div>
              <div class="value" :style="{ color: getDistance('color', item) as string }">
                {{ noDataCheck(item.forecastMaxWl) }}/{{ getDistance('symbol', item)
                }}{{ getDistance('num', item) }}
              </div>
            </div>
            <div class="content-item">
              <div class="label">洪峰(m³/s)</div>
              <div class="value">{{ item.forecastMaxQ }}</div>
            </div>
          </div>
        </div>
      </li>
    </ul>

    <no-warning :is-actual="true" v-if="!tableData.length"></no-warning>
  </div>
</template>

//下游断面情况
<script setup lang="ts">
import NoWarning from '../../Warning/components/NoWarning.vue'
import { GetPlanSummaryVO, SectionForecastVO } from '@/api/interface/plan/VO'

interface Props {
  currentData?: GetPlanSummaryVO
}

const props = withDefaults(defineProps<Props>(), {})

const tableData = ref<Array<SectionForecastVO>>([])

const getDistance = (type, row) => {
  if (
    [null, undefined, ''].includes(row.forecastMaxWl) ||
    [null, undefined, ''].includes(row.limitWl)
  ) {
    return ''
  }
  //获取汛限差
  let waterLevel = row.forecastMaxWl || 0
  let limitLevel = row.limitWl || 0
  if (type === 'symbol') {
    return waterLevel - limitLevel > 0 ? '+' : ''
  } else if (type === 'num') {
    return Number(waterLevel - limitLevel).toFixed(2)
  } else if (type === 'color') {
    return waterLevel - limitLevel > 0 ? '#ff161a' : '#08d075'
  }
}

const noDataCheck = data => {
  if ([null, undefined, ''].includes(data)) {
    return ''
  } else {
    return data
  }
}

watch(
  () => [props.currentData],
  () => {
    tableData.value = props.currentData?.downSectionInfo || []
  },
  {
    deep: true
  }
)

onMounted(() => {
  tableData.value = props.currentData?.downSectionInfo || []
})
</script>

<style lang="scss" scoped>
.river-warning-box {
  padding: 9px 11px;

  .river-list {
    width: 100%;
    max-height: 230px;
    overflow-y: scroll;

    .river-item {
      width: 100%;
      padding: 8px 0;
      border-bottom: 1px dashed #ffffff70;
      border-radius: 2px;

      &:last-child {
        border-bottom: 0;
      }

      .type-info {
        flex-direction: column;
        width: 64px;
        height: 56px;
        border-radius: 2px;

        span {
          margin-top: 4px;
          font-size: 12px;
        }
      }

      .green-bg {
        background: #7ff3;

        img {
          width: 28px;
        }

        span {
          color: #0ff;
        }
      }

      .orange-bg {
        background: #ffd06033;

        img {
          width: 22px;
        }

        span {
          color: #ffce5e;
        }
      }

      .main-info {
        padding-left: 12px;

        .site-name {
          margin-right: 6px;
          font-size: 16px;
          line-height: 16px;
        }

        .data-time {
          font-size: 14px;
          line-height: 14px;
          color: $color-base-white;
        }

        .info-content {
          .content-item {
            padding-right: 22px;
            margin-right: 22px;
            font-size: 14px;
            border-right: 1px solid #ffffff60;

            &:last-child {
              padding-right: 0;
              margin-right: 0;
              border-right: 0;
            }

            .label {
              color: #ffffff60;
            }

            .value {
              color: #fff;
            }
          }

          // .num {
          //   margin-right: 10px;
          //   font-size: 14px;
          //   color: #ffffff60;

          //   span {
          //     color: #ff4b4b;
          //   }
          // }
        }
      }
    }
  }
}
</style>
