// localStorage简单封装，方便修改替换
const _getType = val => {
  return Object.prototype.toString.call(val).slice(8, -1);
};
export default function storageFactory(storage = 'localStorage', prefix = '') {
  if (!['localStorage', 'sessionStorage'].includes(storage)) {
    storage = 'localStorage';
  }

  const _storage = window[storage];

  const _autoAddPrefix = key => {
    return prefix ? `${prefix}_${key}` : key;
  };

  /**
   * 设置 setStorage
   * @param {String} key 键
   * @param {*} value 值
   * @param {object | Number | date} [timeOptions] 时间配置，可选，Number: maxAge，date: expires
   * @param {Number} [timeOptions.maxAge] 内容保留时间毫秒数，优先度比expires高，可选
   * @param {date} [timeOptions.expires] 内容到期时间点，可选
   */
  const setStorage = (key, value, timeOptions = {}) => {
    if (value === '' || value == null) {
      return;
    }

    let expires;

    switch (_getType(timeOptions)) {
      case 'Number':
        expires = +new Date() + timeOptions;
        break;
      case 'Date':
        expires = +timeOptions;
        break;
      case 'Object':
        {
          const { expires: expiresTime, maxAge } = timeOptions;
          if (Number(expiresTime)) {
            expires = +expiresTime;
          }
          if (Number(maxAge)) {
            expires = +new Date() + Number(maxAge);
          }
        }
        break;
    }

    _storage.setItem(
      _autoAddPrefix(key),
      JSON.stringify({
        value, // 存储值
        expires // 到期时间毫秒数
      })
    );
  };

  const getStorage = key => {
    // key 不存在判断
    let data = _storage.getItem(_autoAddPrefix(key));
    if (!data) {
      return null;
    }
    data = JSON.parse(data);
    if (data.expires && +data.expires < +new Date()) {
      // 如果设置了过期时间且已过期
      removeStorage(_autoAddPrefix(key));
      return null;
    }

    return data.value;
  };

  const removeStorage = key => {
    _storage.removeItem(_autoAddPrefix(key));
  };

  const clearStorage = () => {
    _storage.clear();
  };

  return { setStorage, getStorage, removeStorage, clearStorage };
}

const { setStorage, getStorage, removeStorage, clearStorage } = storageFactory('localStorage', process.env.VUE_APP_PROJECT_CODE);

export { setStorage, getStorage, removeStorage, clearStorage };
