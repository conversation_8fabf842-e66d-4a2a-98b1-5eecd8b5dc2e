const path = require('path');
const outputDirName =
  './dist/' + (process.env.NODE_ENV === 'production' ? 'app' : process.env.NODE_ENV === 'staging' ? 'app-h5' : 'app-package');

function resolve(dir) {
  return path.join(__dirname, dir);
}

module.exports = {
  publicPath: process.env.VUE_APP_PUBLIC_PATH,
  assetsDir: 'static',
  lintOnSave: true,
  productionSourceMap: true,
  outputDir: outputDirName,
  devServer: {
    open: false,
    port: 2020,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/qsz': {
        target: process.env.VUE_APP_DEFAULT_URL,
        // target: 'http://10.7.46.213:9065',
        pathRewrite: true,
        pathRewrite: {
          '/qsz': '/qsz'
        }
      }
    }
  },

  css: {
    sourceMap: true,
    loaderOptions: {
      // css: {
      //   // 这里的选项会传递给 css-loader
      //   importLoaders: 1,
      // },
      sass: {
        prependData: `@import "@/style/index.scss";`
        // importLoaders: 1
      },
      less: {
        // 配置vant主题
        // 若 less-loader 版本小于 6.0，请移除 lessOptions 这一级，直接配置选项。
        lessOptions: {
          modifyVars: {
            // 直接覆盖变量
            blue: '#266FE8',
            'text-color': '#000',
            'gray-3': '#CCC',
            'nav-bar-height': '46px',
            'nav-bar-title-font-size': '16px',
            'nav-bar-background-color': '#3177EC', // 粤政易（#3177EC），设计稿（#266FE8）
            'nav-bar-text-color': '#fff',
            'nav-bar-title-text-color': '#fff',
            'nav-bar-icon-color': '#fff',
            'button-default-height': '42px',
            'button-info-color': '#fff',
            'button-info-background-color': '#3E80ED',
            'button-info-font-size': '20px',
            'button-warning-color': '#fff',
            'button-warning-background-color': '#F66673',
            'button-warning-font-size': '20px',
            'button-border-radius': '8px',
            'button-default-font-size': '13px',
            'button-default-color': '#000',
            'switch-size': '12px',
            'switch-width': '60px',
            'switch-height': '24px',
            'switch-node-size': '22px',
            'dialog-width': '300px',
            'dialog-font-size': '14px',
            'dialog-border-radius': '15px',
            'dialog-header-padding-top': '20px',
            'dialog-header-isolated-padding': '20px 0px',
            'dialog-message-padding': '20px',
            'dialog-message-font-size': '14px',
            'dialog-message-line-height': '18px',
            'dialog-has-title-message-text-color': '#000',
            'dialog-has-title-message-padding-top': '20px',
            'dialog-button-height': '42px',
            'dialog-confirm-button-text-color': 'black',
            'field-label-color': '#000000',
            'field-input-text-color': '#000000CC',
            'field-placeholder-text-color': '#00000066',
            'picker-confirm-action-color': '#3E80ED',
            'radio-label-margin': '5px',
            'search-content-background-color': '#F4F5F6',
            // tab
            'tab-text-color': '#000',
            'tab-active-text-color': '#3177EC',
            'tabs-bottom-bar-color': '#3177EC',
            // cell
            'cell-right-icon-color': 'rgba(0, 0, 0, .4)',
            'cell-group-title-color': '#000',
            'cell-value-color': 'rgba(0, 0, 0, .4)',
            // calendar
            'calendar-selected-day-background-color': '#266FE8',
            'calendar-header-title-height': '64px'
          }
        }
      }
    }
  },
  //配置路径
  chainWebpack: config => {
    config.resolve.alias.set('@', resolve('src'));
    // 注入html数据
    config.plugin('html').tap(args => {
      args[0].isEruda = process.env.VUE_APP_IS_ERUDA;
      return args;
    });
  }
};
