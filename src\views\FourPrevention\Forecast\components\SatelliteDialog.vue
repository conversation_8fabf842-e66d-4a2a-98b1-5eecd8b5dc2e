<template>
  <base-dialog
    v-model:showDialog="dialogVisible"
    width="80vw"
    title="气象卫星"
    align-center
    :append-to-body="true"
    :show-footer="false"
  >
    <div class="satellite-content">
      <!-- 左侧图片预览区域 -->
      <div class="image-preview">
        <div class="preview-container">
          <el-image
            v-if="currentImg?.imgUrl"
            :src="currentImg.imgUrl"
            :preview-src-list="previewList"
            :initial-index="currentPreviewIndex"
            fit="contain"
            class="preview-image"
            :preview-teleported="true"
          >
            <template #error>
              <div class="image-error">
                <el-icon><img :src="EMPTY_NO_DATA" alt="" /></el-icon>
                <span>图片加载失败</span>
              </div>
            </template>
          </el-image>
          <div v-else class="no-image">
            <el-icon><img :src="EMPTY_NO_DATA" alt="" /></el-icon>
            <span>未选择图像</span>
          </div>
        </div>
      </div>

      <!-- 右侧控制区域 -->
      <div class="control-panel">
        <!-- 上方：切换组件 -->
        <div class="tab-section">
          <el-segmented
            class="c-el-segmented"
            v-model="satelliteTabIndex"
            :options="[
              { label: '卫星云图', value: 0 },
              { label: '雷达图', value: 1 }
            ]"
            @change="changeSatelliteTab"
          />
        </div>

        <!-- 自动播放控制区域 -->
        <div class="auto-play-section">
          <el-checkbox class="c-checkbox" v-model="autoPlay" :disabled="currentImgList.length <= 1">
            自动播放
          </el-checkbox>
        </div>

        <!-- 下方：时间列表 -->
        <div class="time-list-container">
          <div
            v-for="(img, index) in currentImgList"
            :key="img.id"
            :id="'time-item-' + img.id"
            class="time-item"
            :class="{ active: activeItem === img.id }"
            @click="selectImage(img.id, index)"
          >
            <div class="time-text">{{ img.time }}</div>
            <div class="time-index">{{ index + 1 }}/{{ currentImgList.length }}</div>
          </div>

          <div v-if="currentImgList.length === 0" class="empty-list">
            <el-icon><img :src="EMPTY_NO_DATA" alt="" /></el-icon>
            <span>暂无图片数据</span>
          </div>
        </div>
      </div>
    </div>
  </base-dialog>
</template>
<script lang="ts" setup>
import { getSatelliteImage } from '@/alova_api/methods/forecast'
import { useSatelliteCache } from '../useSatelliteCache'
import { useWatcher } from 'alova/client'
import { satelliteShowUrl } from '@/api/module/attachment'
import { EMPTY_NO_DATA } from '@/utils/constant/icon'

const dialogVisible = defineModel<boolean>({
  default: false
})
const props = defineProps({
  planId: {
    type: Number,
    default: 0
  },
  autoPlayInterval: {
    type: Number,
    default: 3000
  }
})
const { urlCacheMap } = useSatelliteCache()
const { data } = useWatcher(() => getSatelliteImage(props.planId), [() => props.planId], {
  initialData: {}
})
// 气象卫星数据
const satelliteTabIndex = ref(0)
const activeItem = ref('')
const currentPreviewIndex = ref(0)

// 自动播放相关
const autoPlay = ref(false)
let autoPlayTimer: NodeJS.Timeout | null = null

const changeSatelliteTab = () => {
  // 切换标签时重置选中项和自动播放
  autoPlay.value = false
  clearAutoPlayTimer()
  nextTick(() => {
    if (currentImgList.value.length) {
      activeItem.value = currentImgList.value[0].id
      currentPreviewIndex.value = 0
    }
  })
}

// 选择图片
const selectImage = (id: string, index: number) => {
  activeItem.value = id
  currentPreviewIndex.value = index
  // 手动点击时关闭自动播放
  autoPlay.value = false
  clearAutoPlayTimer()
}

// 清除自动播放定时器
const clearAutoPlayTimer = () => {
  if (autoPlayTimer) {
    clearInterval(autoPlayTimer)
    autoPlayTimer = null
  }
}

// 启动自动播放
const startAutoPlay = () => {
  clearAutoPlayTimer()
  if (currentImgList.value.length <= 1) return

  autoPlayTimer = setInterval(() => {
    const currentIndex = currentImgList.value.findIndex(img => img.id === activeItem.value)
    const targetDom = document.querySelector('#time-item-' + activeItem.value)
    if (targetDom) {
      targetDom.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
    const nextIndex = (currentIndex + 1) % currentImgList.value.length
    const nextImg = currentImgList.value[nextIndex]

    if (nextImg) {
      activeItem.value = nextImg.id
      currentPreviewIndex.value = nextIndex
    }
  }, props.autoPlayInterval)
}

// 停止自动播放
const stopAutoPlay = () => {
  clearAutoPlayTimer()
}

const currentImgList = computed(() => {
  if (satelliteTabIndex.value === 0) {
    // 气象卫星
    return (
      data.value.satelliteCloudImages?.map(item => {
        return {
          imgUrl:
            urlCacheMap.value[satelliteShowUrl + item.localUrl] || satelliteShowUrl + item.localUrl,
          time: item.dataTime,
          id: item.sid
        }
      }) || []
    )
  }
  return (
    data.value.radarPuzzleImages?.map(item => {
      return {
        imgUrl:
          urlCacheMap.value[satelliteShowUrl + item.localUrl] || satelliteShowUrl + item.localUrl,
        time: item.dataTime,
        id: item.sid
      }
    }) || []
  )
})

const currentImg = computed(() => currentImgList.value.find(i => i.id === activeItem.value))

// 预览图片列表
const previewList = computed(() => currentImgList.value.map(img => img.imgUrl))

// 监听自动播放状态变化
watch(autoPlay, newVal => {
  if (newVal) {
    startAutoPlay()
  } else {
    stopAutoPlay()
  }
})

// 监听对话框显示状态，关闭时清理定时器
watch(dialogVisible, newVal => {
  if (!newVal) {
    autoPlay.value = false
    clearAutoPlayTimer()
  } else {
    autoPlay.value = true
  }
})

// 监听图片列表变化，自动选择第一张图片
watch(
  currentImgList,
  newList => {
    if (newList.length > 0 && !activeItem.value) {
      activeItem.value = newList[0].id
      currentPreviewIndex.value = 0
    }
  },
  { immediate: true }
)

// 组件卸载时清理定时器
onUnmounted(() => {
  clearAutoPlayTimer()
})
</script>

<style lang="scss" scoped>
.satellite-content {
  display: flex;
  gap: 20px;
  height: 600px;
  margin-top: 16px;
}

// 左侧图片预览区域
.image-preview {
  flex: 1;

  .preview-container {
    width: 100%;
    height: 100%;
    border: 1px solid #0b4eb3;
    border-radius: 8px;
    overflow: hidden;
    background: rgb(16 54 98 / 20%);
    display: flex;
    align-items: center;
    justify-content: center;

    .preview-image {
      width: 100%;
      height: 100%;
      position: relative;
      --el-fill-color-light: transparent;

      :deep(.el-image__inner) {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .image-error,
    .no-image {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      gap: 12px;
      color: #c5e3ff;
      font-size: 16px;

      .el-icon {
        font-size: 64px;
        color: #7ff;
      }
    }
  }
}

// 右侧控制面板
.control-panel {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  // 上方：切换组件区域
  .tab-section {
    .c-el-segmented {
      width: 100%;

      .el-segmented__item {
        flex: 1;
        justify-content: center;
      }
    }
  }

  // 自动播放控制区域
  .auto-play-section {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #0b4eb3;
    border-radius: 6px;
    background: rgb(16 54 98 / 20%);

    .c-checkbox {
      color: #fff;

      &.is-disabled {
        opacity: 0.5;

        .el-checkbox__label {
          color: #c5e3ff;
        }
      }
    }
  }

  // 下方：时间列表区域
  .time-list-container {
    flex: 1;
    border: 1px solid #0b4eb3;
    border-radius: 8px;
    overflow-y: auto;
    background: rgb(16 54 98 / 20%);

    .time-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 4px;
      padding: 12px 16px;
      border-bottom: 1px solid rgb(11 78 179 / 30%);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgb(24 130 251 / 20%);
      }

      &.active {
        background: radial-gradient(106.99% 117.88% at 0% 0%, #09fc 0%, #0099ff52 100%);
        border-color: #2fb3e9;
        box-shadow: 0 0 7px 0 #00b2ff inset;

        .time-text {
          color: #7ff;
        }

        .time-index {
          color: #c5e3ff;
        }
      }

      &:last-child {
        border-bottom: none;
      }

      .time-text {
        font-size: 14px;
        color: #fff;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .time-index {
        font-size: 12px;
        color: #c5e3ff;
        opacity: 0.8;
      }
    }

    .empty-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #c5e3ff;
      font-size: 16px;
      gap: 12px;

      .el-icon {
        font-size: 64px;
        color: #7ff;
      }
    }
  }
}
</style>
