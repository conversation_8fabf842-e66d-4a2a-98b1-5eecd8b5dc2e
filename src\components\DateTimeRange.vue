<template>
  <div class="dateTimeRange">
    <div class="flex flex-wrap j-center" v-if="mode === 1">
      <div class="time flex-vc j-sb bb full-w" @click="handleClickTime('start')">
        {{ (startTime !== '' && startTime.format(formatValue)) || '开始时间' }}
        <div class="time-icon">
          <van-icon name="close" v-if="clearable && !disabled && startTime !== ''" @click.stop="clearDate('start')" />
          <van-icon name="arrow-down" />
        </div>
      </div>
      <span style=" color: #00000066;" class="mtb14">至</span>
      <div class="time flex-vc j-sb bb full-w" @click="handleClickTime('end')">
        {{ (endTime !== '' && endTime.format(formatValue)) || '结束时间' }}
        <div class="time-icon">
          <van-icon name="close" v-if="clearable && !disabled && endTime !== ''" @click.stop="clearDate('end')" />
          <van-icon name="arrow-down" />
        </div>
      </div>
    </div>
    <div class="timeBar flex-hvc" :class="{ disabled }" v-if="mode === 2">
      <span class="flex-1 text-center" @click="handleClickTime('start')">
        {{ (startTime !== '' && startTime.format(formatValue)) || '开始时间' }}
      </span>
      <span style="color:rgb(0 0 0 / 40%)"> - </span>
      <span class="flex-1 text-center" @click="handleClickTime('end')">
        {{ (endTime !== '' && endTime.format(formatValue)) || '结束时间' }}
      </span>
      <van-icon name="close" v-if="clearable && !disabled && (startTime !== '' || endTime !== '')" @click.stop="clearDate" />
      <van-icon name="arrow-down" color="#3d80e0" />
    </div>
    <van-popup v-model="showSelectTime" position="bottom" :style="{ height: '50%' }" get-container="body">
      <van-datetime-picker
        v-model="currentDate"
        :type="timeType"
        :title="pickerTitle"
        :min-date="realMinDate"
        :max-date="realMaxDate"
        @confirm="confirmDate"
        @cancel="showSelectTime = false"
      />
    </van-popup>
  </div>
</template>

<script>
import dayjs from 'dayjs';
const minRangeDate = new Date(+dayjs().subtract(10, 'year'));
const maxRangeDate = new Date(+dayjs().add(10, 'year'));
export default {
  props: {
    startTime: {
      default: () => dayjs().subtract(1, 'month')
    },
    endTime: {
      default: () => dayjs()
    },
    mode: {
      default: 1
    },
    formatValue: {
      default: 'YYYY-MM-DD'
    },
    timeType: {
      default: 'date'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    diffTime: {
      type: Object,
      default: () => ({})
    },
    minDate: {
      type: Date,
      default: () => minRangeDate
    },
    maxDate: {
      type: Date,
      default: () => maxRangeDate
    }
  },
  data() {
    return {
      showSelectTime: false,
      currentDate: new Date(),
      realMinDate: minRangeDate,
      realMaxDate: maxRangeDate,
      type: '',
      pickerTitle: ''
    };
  },
  created() {
    this.realMinDate = this.minDate;
    this.realMaxDate = this.maxDate;
  },
  methods: {
    handleClickTime(type = 'start') {
      if (this.disabled) return;
      this.type = type;
      this.showSelectTime = true;
      if (type === 'start') {
        this.pickerTitle = '开始时间';
        setTimeout(() => {
          if (this.startTime) {
            this.currentDate = new Date(+dayjs(this.startTime));
          }
        }, 0);

        this.realMinDate = this.minDate;
        this.realMaxDate = this.endTime ? new Date(+dayjs(this.endTime)) : this.maxDate;
      }
      if (type === 'end') {
        this.pickerTitle = '结束时间';
        setTimeout(() => {
          if (this.endTime) {
            this.currentDate = new Date(+dayjs(this.endTime));
          }
        }, 0);

        this.realMinDate = this.startTime ? new Date(+dayjs(this.startTime)) : this.minDate;
        this.realMaxDate = this.maxDate;
      }
    },
    confirmDate(date) {
      const { unit, value } = this.diffTime;
      if (this.type === 'start') {
        if (['year', 'month', 'day', 'hour', 'minute'].includes(unit) && value) {
          if (dayjs(this.endTime).diff(date, unit) > value) {
            this.$emit('update:endTime', dayjs(date).add(value, unit));
          }
        }
        this.$emit('update:startTime', dayjs(date));
        if (dayjs(date).valueOf() > this.endTime.valueOf()) {
          this.$emit('update:endTime', dayjs(date));
        }
      }
      if (this.type === 'end') {
        if (['year', 'month', 'day', 'hour', 'minute'].includes(unit) && value) {
          if (dayjs(date).diff(this.startTime, unit) > value) {
            this.$emit('update:startTime', dayjs(date).subtract(value, unit));
          }
        }
        this.$emit('update:endTime', dayjs(date));
      }
      this.showSelectTime = false;
      this.$nextTick(() => {
        if (this.startTime && this.endTime) {
          this.$emit('confirmDate', {
            startTime: this.startTime,
            endTime: this.endTime
          });
        }
      });
    },
    clearDate(type) {
      if (type === 'start') {
        this.$emit('update:startTime', '');
      } else if (type === 'end') {
        this.$emit('update:endTime', '');
      } else {
        this.$emit('update:startTime', '');
        this.$emit('update:endTime', '');
      }
      this.$emit('confirmDate', {
        startTime: '',
        endTime: ''
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.time {
  padding: 14px 27px;
  line-height: 36px;
  background-color: #f5f5f5;
}
.timeBar {
  box-sizing: border-box;
  gap: 3px;
  padding: 20px 10px;
  margin: 20px 32px;
  background-color: #ffffff;
  border: 1px solid rgb(0 0 0 / 12%);
  border-radius: 20px;
}
.disabled {
  background-color: #f5f5f5;
}
.time-icon .van-icon-close {
  margin-right: 5px;
}
</style>
