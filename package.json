{"name": "shan-tou-h5", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "build:app": "vue-cli-service build --mode app", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:fix": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/"}, "dependencies": {"@vue-office/docx": "^1.6.2", "axios": "^0.21.1", "compressorjs": "^1.1.1", "core-js": "^3.6.5", "dayjs": "^1.11.9", "default-passive-events": "^2.0.0", "dommatrix": "^1.0.3", "echarts": "^5.4.1", "js-pinyin": "^0.1.9", "mammoth": "^1.5.1", "pdfh5": "^1.4.9", "pinyin4js": "^1.3.18", "postcss": "^8.3.6", "postcss-px-to-viewport": "^1.1.1", "qs": "^6.10.1", "sm-crypto": "^0.3.13", "vant": "^2.12.47", "vue": "^2.6.11", "vue-baidu-map": "^0.21.22", "vue-demi": "^0.14.6", "vue-router": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "web-streams-polyfill": "^3.2.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.0", "@vue/cli-plugin-eslint": "^4.5.0", "@vue/cli-service": "^4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "autoprefixer": "^8.0.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.5", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "less": "^4.1.1", "less-loader": "^7.3.0", "postcss-html": "^1.5.0", "prettier": "^1.19.1", "sass": "^1.26.5", "sass-loader": "^8.0.2", "sass-resources-loader": "^2.1.1", "stylelint": "^15.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.4.0", "stylelint-config-recommended-scss": "^13.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^11.1.0", "vue-template-compiler": "^2.6.11"}}