<template>
  <!-- 情况上报 -->
  <main class="main flex-column">
    <CustomNavBar />
    <section class="section flex-1 flex-column">
      <div class="section-head flex-vc">
        <img class="head-avatar" src="@/assets/images/patrol/patrolTask/avatar.png" alt="头像" />
        <div>
          <div class="head-name">检查人员：{{ userName }}</div>
          <div class="head-date">
            巡检日期： <span>{{ currentDate }}</span>
          </div>
        </div>
      </div>
      <van-form class="section-content flex-1" validate-first>
        <van-field name="radio" label="检查情况">
          <template #input>
            <van-radio-group v-model="formData.stnType" direction="horizontal">
              <van-radio v-for="item in situationOptions" :key="item.value" :name="item.value">{{ item.label }}</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-if="formData.stnType === 2"
          readonly
          clickable
          name="picker"
          :value="formData.hideDangerTypeName"
          label="隐患类型"
          placeholder="请选择"
          right-icon="arrow"
          input-align="right"
          @click="showPicker = true"
        />
        <van-field
          v-show="formData.stnType === 2"
          v-model="formData.stnPmRefer"
          rows="4"
          autosize
          label="问题描述"
          type="textarea"
          placeholder="请输入"
          show-word-limit
          maxlength="50"
        />
        <van-field
          :value="formData.stnAddress"
          readonly
          clickable
          name="position"
          label="定位"
          placeholder="去获取"
          right-icon="arrow"
          input-align="right"
          @click="getAddress"
        />
        <van-field name="radio" label="照片">
          <template #input>
            <CustomUpload v-model="formData.attachmentList" list-type="picture" :module-id="9" module="xjxc" :capture="capture" />
          </template>
        </van-field>
      </van-form>
    </section>
    <BottomBtn @cancel="onCancel" @confirm="onConfirm" />

    <van-popup v-model="showPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="name"
        :columns="dangerTypeColumns"
        @confirm="onPickerConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
  </main>
</template>

<script>
import BottomBtn from '@/components/BottomBtn.vue';
import CustomUpload from '@/components/CustomUpload/index.vue';
import { situationOptions } from '../../options';
import { addStn } from '@/api/patrolManage/patrolProblem/index.js';
import { getCurrentPosition } from '@/utils/location';
import { getStorage } from '@/utils/storage';
export default {
  name: 'TaskReport',
  components: {
    BottomBtn,
    CustomUpload
  },
  props: {},
  data() {
    const currentDate = this.$dayjs().format('YYYY-MM-DD');
    const isAndroid = /android/i.test(navigator.userAgent);
    return {
      capture: isAndroid ? 'camera' : null,
      situationOptions,
      taskId: null,
      currentDate,
      userName: '',
      formData: {
        stnType: 1,
        stnPmRefer: '',
        stnAddress: '',
        lgtd: null,
        lttd: null,
        attachmentList: [],
        stdHdType: null,
        hideDangerTypeName: ''
      },
      showPicker: false,
      dangerTypeColumns: [
        { name: '设备老化', value: 1 },
        { name: '工程隐患', value: 2 },
        { name: '其他', value: 3 }
      ]
    };
  },
  computed: {},
  watch: {},
  created() {
    const param = this.$route.query;
    this.taskId = +param.taskId;
    const userInfo = getStorage('userInfo');
    if (userInfo) {
      this.userName = userInfo.displayName;
    }
    this.getAddress();
  },
  mounted() {},
  methods: {
    onPickerConfirm(val) {
      this.formData.hideDangerTypeName = val.name;
      this.formData.stdHdType = val.value;
      this.showPicker = false;
    },
    getAddress() {
      const successFn = position => {
        const { longitude, latitude } = position.coords;
        const { city, district, street, streetNum } = position.address || {};
        const address = [city, district, street, streetNum].filter(i => i).join('');
        this.formData.lgtd = longitude;
        this.formData.lttd = latitude;
        this.formData.stnAddress = address;
      };
      getCurrentPosition(successFn);
    },
    onConfirm() {
      addStn({
        taskId: this.taskId,
        ...this.formData
      }).then(res => {
        if (res.status === 200) {
          this.$userApp.toast.show({
            text: '已上报情况',
            type: 'text'
          });
          setTimeout(() => {
            this.$router.back();
          }, 500);
        }
      });
    },
    onCancel() {
      this.$router.back();
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
}
.section {
  padding-bottom: 145px;
  .section-head {
    box-sizing: border-box;
    height: 200px;
    padding-left: 48px;
    font-size: 28px;
    line-height: 36px;
    color: $color-text-white;
    background: url('~@/assets/images/patrol/patrolTask/avatar-bg-mini.png') no-repeat 0 0;
    background-size: 100% 100%;
    .head-avatar {
      width: 90px;
      height: 90px;
      margin-right: 20px;
    }
    .head-name {
      margin-bottom: 16px;
    }
    .head-date {
      font-size: 24px;
      color: rgba($color-text-white, 0.8);
    }
  }
  .section-content {
    overflow: auto;
  }
}
.bottom-btn {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  padding: 24px 46px;
  background: $bg-page;
  border-top: 1px solid $border-color1;
}
</style>
