<template>
  <div class="task-cell">
    <div class="cell-content">
      <van-image class="content-img" :src="imageUrl" @click="showBigImg" alt="巡检图片" />
      <div class="content-detail flex-1">
        <div class="detail-title">{{ data.projectName }}</div>
        <div class="detail-vice">{{ data.stnFdTime }}</div>
        <div class="detail-content flex-vc j-sb">
          <van-tag class="content-tag" :type="data.stnType === 2 ? 'danger' : 'success'" plain>
            {{ data.stnType === 2 ? '异常' : '正常' }}
          </van-tag>
          <van-icon v-if="showBtn" class="content-btn" name="delete-o" color="#00000066" @click="onDelete">
            <span class="btn-text">删除</span>
          </van-icon>
        </div>
      </div>
    </div>
    <div v-if="data.stnType === 2" class="cell-descript flex a-start">
      <van-icon class="descript-icon" name="question-o" />
      <span class="descript-label">隐患类型：</span>
      <span class="descript-value">{{ dangerOptions[data.stdHdType] || '--' }}</span>
    </div>
    <div v-if="data.stnType === 2" class="cell-descript flex a-start">
      <van-icon class="descript-icon" name="question-o" />
      <span class="descript-label">问题描述：</span>
      <span class="descript-value">{{ data.stnPmRefer }}</span>
    </div>
  </div>
</template>

<script>
import { ImagePreview } from 'vant';
export default {
  name: 'RecordCell',
  props: {
    showBtn: {
      type: Boolean,
      default: true
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dangerOptions: {
        1: '设备老化',
        2: '工程隐患',
        3: '其他'
      }
    };
  },
  computed: {
    images() {
      if (this.data.attachmentList && this.data.attachmentList.length) {
        return this.data.attachmentList.map(i => {
          return this.$appApiUrl.imgVideoUrl + i.file_path + `&moduleId=9`;
        });
      }
      return [];
    },
    imageUrl() {
      if (this.images.length) {
        return this.images[0];
      }
      return '';
    }
  },
  methods: {
    showBigImg() {
      if (this.images.length) {
        ImagePreview({ images: this.images });
      }
    },
    onDelete() {
      this.$emit('delete', this.data);
    }
  }
};
</script>

<style lang="scss" scoped>
.task-cell {
  padding: 32px 32px 24px;
  margin-bottom: 24px;
  font-size: 28px;
  line-height: 36px;
  color: rgba($color-text-black, 0.9);
  background: $color-text-white;
  border-radius: 20px;
}
.cell-content {
  display: flex;
  .content-img {
    width: 175px;
    height: 148px;
    margin-right: 32px;
    overflow: hidden;
    border-radius: 18px;
  }
  .detail-title {
    margin-bottom: 8px;
    font-size: 32px;
    font-weight: 500;
    line-height: 40px;
    color: $color-text-black;
  }
  .detail-vice {
    margin-bottom: 16px;
    color: rgba($color-text-black, 0.6);
  }
  .detail-content {
    .content-tag {
      padding: 7px 26px;
      border-radius: 8px;
    }
    .content-btn {
      font-size: 32px;
      cursor: pointer;
      .btn-text {
        margin-left: 12px;
        font-size: 28px;
        color: $color-text-vice;
      }
    }
  }
}
.cell-descript {
  margin-top: 30px;
  .descript-icon {
    margin-right: 8px;
    font-size: 32px;
    line-height: 36px;
    color: $color-error;
  }
  .descript-value {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
  }
}
.text-danger {
  color: $color-error;
}
.text-success {
  color: $color-success;
}
</style>
