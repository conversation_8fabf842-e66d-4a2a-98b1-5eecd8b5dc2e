<template>
  <div class="common-container flex-column">
    <div class="common-slot">
      <slot name="info" v-bind="{ data: slotData }"></slot>
    </div>
    <div class="common-record flex-1  flex-column">
      <div class="common-title">位置上报记录</div>
      <div class="common-table flex-1">
        <CustomTable :options="options" :data-list="list" @onRefresh="onRefresh" @onLoad="onLoad"> </CustomTable>
      </div>
    </div>
    <div class="common-footer">
      <van-button v-if="!status" class="btn" type="info" :block="true" :round="true" @click="reportStart">开始上报</van-button>

      <div v-else class="flex">
        <van-button class="btn" type="info" :block="true" :round="true" @click="reportLocation">上报位置</van-button>
        <van-button class="btn" type="danger" :block="true" :round="true" @click="reportEnd">结束上报</van-button>
      </div>
    </div>
  </div>
</template>
<script>
import CustomTable from '@/components/CustomTable';
import { setStorage, getStorage, removeStorage } from '@/utils/storage';
import { getLocationInfo, addLocationInfo, deleteAddressInfo } from '@/api/patrolManage/locationReport';
import listLoading from '@/mixins/listLoading';
import { getCurrentPosition } from '@/utils/location';

const typeOptions = {
  1: 'RescueTeam',
  2: 'MaterialTransportation',
  3: 'PersonTransfer'
};

export default {
  name: 'Common',
  mixins: [listLoading],
  components: { CustomTable },
  props: {
    type: {
      type: Number,
      default: 1
    },
    rankId: {
      type: Number
    }
  },
  data() {
    return {
      status: false,
      columns: [
        {
          title: '上报时间',
          key: 'time'
        },
        {
          title: '上报地址',
          key: 'address',
          align: 'center',
          formatter: (cellValue, row) => `经度:${row.longitude || '--'},纬度:${row.latitude || '--'}`
        }
      ],
      userId: null,
      slotData: {
        num: null,
        material: null
      }
    };
  },
  created() {
    const userInfo = getStorage('userInfo');
    this.userId = userInfo.userId;
    // 获取上报记录状态缓存
    this.status = getStorage(typeOptions[this.type]) || false;
    this.options.columns = this.columns;
  },
  mounted() {},
  methods: {
    // 开始上报
    reportStart() {
      // 更新上报状态
      this.status = true;
      setStorage(typeOptions[this.type], this.status);
    },

    // 获取上报记录列表
    getList() {
      getLocationInfo({ type: this.type }).then(res => {
        this.list = res.data || [];
        // 倒序
        this.list.reverse();
        this.options.loading = false;
        this.options.finished = true;

        if (this.type === 2) {
          this.slotData.material = this.list[0]?.material || '';
        }
        if (this.type === 3) {
          this.slotData.num = this.list[0]?.num;
        }
      });
    },
    // 上报位置
    reportLocation() {
      if (this.type === 3 && this.slotData.num !== 0 && !this.slotData.num) {
        this.$userApp.toast.show({
          text: `请输入转移人数！`,
          type: 'text'
        });
        return;
      }

      const that = this;

      // 获取当前经纬度;
      getCurrentPosition(position => {
        const data = {
          longitude: position.coords.longitude,
          latitude: position.coords.latitude,
          time: that.$dayjs().format('YYYY-MM-DD HH:mm:ss'),
          type: this.type,
          rankId: that.rankId,
          userId: that.userId,
          num: that.slotData.num || undefined,
          material: that.slotData.material
        };
        addLocationInfo(data).then(res => {
          if (res.status === 200) {
            this.getList();
            this.$userApp.toast.show({
              text: `上报成功`,
              type: 'text'
            });
          }
        });
      });
    },
    // 结束上报
    reportEnd() {
      // 清空上报记录
      deleteAddressInfo({ type: this.type, rankId: this.rankId }).then(res => {
        if (res.status === 200) {
          // 更新上报状态
          removeStorage(typeOptions[this.type]);
          this.status = false;
          this.$userApp.toast.show({
            text: `已结束上报`,
            type: 'text'
          });
          this.onRefresh();
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.common-container {
  box-sizing: border-box;
  height: 100%;
  padding-top: 20px;
  overflow: hidden;
  .common-title {
    padding: 0 20px;
    margin-bottom: 15px;
    font-size: 32px;
    font-weight: 500;
  }
  .common-record,
  .common-table {
    overflow: hidden;
  }
  .common-footer {
    padding: 20px;
    border-top: 1px solid $border-color1;
    > div {
      gap: 20px;
    }
  }
}
</style>
