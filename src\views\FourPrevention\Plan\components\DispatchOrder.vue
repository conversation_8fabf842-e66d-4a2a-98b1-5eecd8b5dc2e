<template>
  <div class="dispatch-order-box">
    <el-form
      ref="formRef"
      :model="orderFormData"
      :rules="orderFormRules"
      label-width="auto"
      class="c-blue-form order-form"
    >
      <el-form-item label="发布单位:" prop="releaseUnitId">
        <el-select
          v-model="orderFormData.releaseUnitId"
          placeholder="请选择发布单位"
          class="order-form-input no-border-form-item"
          @change="getUnitTree"
        >
          <el-option
            :label="item.orgName"
            :value="item.id!"
            v-for="item in totalUnitList"
            :key="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发布时间:" prop="releaseTime">
        <el-date-picker
          v-model="orderFormData.releaseTime"
          type="datetime"
          placeholder="请选择发布时间"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="调令名称:" prop="title">
        <el-input
          v-model="orderFormData.title"
          placeholder="请输入调令名称"
          class="order-form-input no-border-form-item"
        />
      </el-form-item>
      <el-form-item label="调令内容:" label-position="top">
        <!-- <el-input
          v-model="orderFormData.content"
          type="textarea"
          placeholder="请输入调令内容"
          :autosize="{ minRows: 5, maxRows: 15 }"
          class="no-border-form-item"
        /> -->
        <div class="editor-box">
          <wang-editor
            :custom-prop="orderFormData.content"
            :trans-bg="true"
            @content-update="contentUpdate"
          ></wang-editor>
        </div>
      </el-form-item>

      <el-form-item label="调令下达单位:" prop="commandingUnitIds" label-position="top">
        <div class="unit-sel-box flx">
          <div class="unit-left">
            <el-tree
              ref="unitTreeRef"
              :data="unitData"
              node-key="id"
              :props="defaultProps"
              :show-checkbox="true"
              :default-expand-all="true"
              class="c-black-el-tree"
              @check-change="selUnit"
            />
          </div>
          <div class="unit-right flx-column">
            <div class="selected-title flx-align-center">
              <img src="@/assets/images/plan/selected-icon.png" alt="" />
              <span>已选单位</span>
            </div>

            <ul class="sel-list">
              <li class="sel-item flx-justify-between" v-for="item in selList">
                <span class="item-label">{{ item.dictName }}</span>
                <el-icon :size="14" :color="'#7ff'" @click="cancelSelUnit(item.id)">
                  <Close />
                </el-icon>
              </li>
            </ul>
          </div>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="flx-align-center w-full" style="justify-content: flex-end">
          <el-button :icon="Promotion" class="sel-btn" @click="submitOrder(1)">发送</el-button>
          <el-button :icon="CircleCheckFilled" class="sel-btn" @click="submitOrder(0)"
            >保存草稿</el-button
          >
          <el-button :icon="Document" class="sel-btn" @click="showOrderRecord">查看记录</el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>

  <order-record-dialog v-if="showRecord" @dialog-close="closeOrderRecord"></order-record-dialog>
</template>

<script setup lang="ts">
import { Close, CircleCheckFilled, Promotion, Document } from '@element-plus/icons-vue'
import OrderRecordDialog from './OrderRecordDialog.vue'
import { GetForecastSchemeVO, GetUnitTreeVO } from '@/api/interface/plan/VO'
import { ReleaseDispatchDTO } from '@/api/interface/plan/DTO'
import {
  getUnitListApi,
  getUnitTreeApi,
  releaseDispatchApi,
  getInitGuidanceApi,
  getDictDataApi
} from '@/api/module/fourPrevention/plan'
import dayjs from 'dayjs'

interface Props {
  currentPlan?: GetForecastSchemeVO
  dispatchRule: string
  dispatchName: string
  resCode?: string
}

const props = withDefaults(defineProps<Props>(), {
  resCode: 'A532301S2007'
})

watch(
  () => [props.currentPlan],
  () => {
    if (props.currentPlan) {
      orderFormData.value.planId = props.currentPlan.id || 0
      orderFormData.value.plan = props.currentPlan.forecastName || ''
      getInitContent()
    }
  },
  {
    deep: true
  }
)

watch(
  () => [props.dispatchName],
  () => {
    orderFormData.value.title = props.dispatchName
  }
)
watch(
  () => [props.dispatchRule],
  () => {
    orderFormData.value.dispatchRule = props.dispatchRule
  }
)

const orderFormData = ref<ReleaseDispatchDTO>({
  planId: 0,
  plan: '',
  dispatchRule: '',
  releaseUnitId: 0,
  releaseUnit: '',
  releaseTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  title: '',
  content: '',
  commandingUnitIds: [],
  status: 0
})

const orderFormRules = ref({
  releaseUnitId: [{ required: true, message: '请选择发布单位', trigger: 'change' }],
  releaseTime: [{ required: true, message: '请选择发布时间', trigger: 'change' }],
  title: [{ required: true, message: '请输入调令名称', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  commandingUnitIds: [{ required: false, message: '请选择调令下达单位', trigger: 'change' }]
})

const getInitContent = async () => {
  const res = await getInitGuidanceApi({
    resCode: props.resCode,
    schemeId: props.currentPlan!.id
  })
  if (res.data) {
    orderFormData.value.content = `${res.data.briefIntroduction ? res.data.briefIntroduction + '\n\n' : ''}${res.data.skDispatchContent ? res.data.skDispatchContent + '\n\n' : ''}${res.data.disasterMaterialsDispatchContent}`
  }
}

const totalUnitList = ref<Array<any>>([])

const getUnitList = async () => {
  const res = await getUnitListApi({})
  if (res.data && res.data.list) {
    totalUnitList.value = res.data.list
    if (totalUnitList.value.length) {
      orderFormData.value.releaseUnitId = totalUnitList.value[0].id
      getUnitTree()
    }
  }
}

const unitTreeRef = ref()

const unitData = ref<Array<GetUnitTreeVO>>([
  // {
  //   id: '1',
  //   label: '汕头市下埔桥闸水利管理处',
  //   children: [
  //     {
  //       id: '1-1',
  //       label: '人事部'
  //     }
  //   ]
  // }
])

const defaultProps = {
  children: 'children',
  label: 'dictName'
}

const getUnitTree = async () => {
  // const res = await getUnitTreeApi({ rootId: orderFormData.value.releaseUnitId })
  const res = await getDictDataApi({ code: 'TRANSFER_ORDER_ISSUING_UNIT' })
  if (res.data) {
    let total = res.data[0]
    total.dictName = '调令下达单位'
    unitData.value = [total]
  }
}

const selList = ref<Array<any>>([])

const selUnit = () => {
  let selNodes = unitTreeRef.value.getCheckedNodes().filter(item => !item.children)
  selList.value = JSON.parse(JSON.stringify(selNodes))
}

const cancelSelUnit = id => {
  unitTreeRef.value.setChecked(id, false)
}

const formRef = ref()

const contentUpdate = val => {
  orderFormData.value.content = val
}

const submitOrder = status => {
  if (!orderFormData.value.planId) {
    ElMessage.warning('请选择预报方案')
    return
  }
  if (!orderFormData.value.dispatchRule) {
    ElMessage.warning('请选择调度规则')
    return
  }
  formRef.value.validate(valid => {
    if (valid) {
      ElMessageBox.confirm(`确定要${status === 1 ? '发布' : '保存草稿'}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          orderFormData.value.commandingUnitIds = selList.value.map(item => item.id)
          if (!orderFormData.value.commandingUnitIds.length) {
            ElMessage.warning('请选择调令下达单位')
            return
          }
          orderFormData.value.releaseUnit = totalUnitList.value.find(
            item => item.id === orderFormData.value.releaseUnitId
          ).orgName
          orderFormData.value.status = status

          const res = await releaseDispatchApi(orderFormData.value)
          if (res.status === 200) {
            ElMessage.success(`${status === 1 ? '发布' : '保存草稿箱'}成功`)
          }
        })
        .catch(() => {})
    }
  })
}

const showRecord = ref(false)

const showOrderRecord = () => {
  showRecord.value = true
}

const closeOrderRecord = () => {
  showRecord.value = false
}

onMounted(() => {
  getUnitList()
})
</script>

<style lang="scss" scoped>
.dispatch-order-box {
  padding: 9px 11px;

  .order-form {
    .el-form-item {
      &:last-child {
        margin-bottom: 0;
      }

      .no-border-form-item {
        --el-input-border: none !important;
        --el-input-hover-border: none !important;
        --el-input-focus-border: none !important;
        --el-input-border-color: #fff0 !important;
        --el-input-hover-border-color: #fff0 !important;
        --el-input-clear-hover-color: #fff0 !important;
        --el-input-focus-border-color: #fff0 !important;
      }

      .editor-box {
        width: 100%;
        height: 330px;
        padding: 8px;
        background: url('@/assets/images/plan/textarea-bg.png') no-repeat !important;
        background-position: center center !important;
        background-size: 100% 100% !important;
      }

      .order-form-input {
        background: url('@/assets/images/plan/form-item-bg.png') no-repeat !important;
        background-position: center center !important;
        background-size: 100% 100% !important;

        --el-border-color: transparent !important;
        --el-select-border-color-hover: transparent !important;

        :deep(.el-input__wrapper),
        :deep(.el-textarea__inner),
        :deep(.el-select__wrapper) {
          color: #fff !important;
          background-color: transparent !important;
          border: 1px solid transparent !important;
        }

        :deep(.is-hovering) {
          border: 1px solid transparent !important;
        }
      }

      // :deep(.el-select) {
      //   --el-border-color: transparent !important;
      //   --el-select-border-color-hover: transparent !important;

      //   .el-select__wrapper {
      //     border: 1px solid transparent !important;
      //   }
      // }

      :deep(.el-date-editor) {
        --el-input-border: none !important;
        --el-input-hover-border: none !important;
        --el-input-focus-border: none !important;
        --el-input-border-color: #fff0 !important;
        --el-input-hover-border-color: #fff0 !important;
        --el-input-clear-hover-color: #fff0 !important;
        --el-input-focus-border-color: #fff0 !important;

        background: url('@/assets/images/plan/form-item-bg.png') no-repeat !important;
        background-position: center center !important;
        background-size: 100% 100% !important;

        .el-input__wrapper {
          color: #fff !important;
          background-color: transparent !important;
          border: 1px solid transparent !important;
        }
      }

      :deep(.el-textarea) {
        background: url('@/assets/images/plan/textarea-bg.png') no-repeat !important;
        background-position: center center !important;
        background-size: 100% 100% !important;

        .el-input__wrapper {
          color: #fff !important;
          background-color: transparent !important;
          border: 1px solid transparent !important;
        }
      }

      .unit-sel-box {
        width: 100%;
        height: 200px;
        border: 1px solid #0b94b0;

        .unit-left,
        .unit-right {
          width: 50%;
          height: 100%;
          padding: 6px;
          overflow-y: scroll;
        }

        .unit-left {
          background-color: rgb(0 123 148 / 30%);
          border-right: 1px solid #0b94b0;
        }

        .unit-right {
          background-color: rgb(9 62 114 / 30%);

          .selected-title {
            padding: 2px 0 8px;
            margin-bottom: 4px;
            border-bottom: 1px solid #fff3;

            img {
              width: 16px;
              height: 16px;
              margin-right: 6px;
            }

            span {
              font-size: 14px;
              line-height: 14px;
              color: #7ff;
            }
          }

          .sel-list {
            flex: 1;

            .sel-item {
              padding: 6px;
              margin-bottom: 4px;
              background-color: #1882fb30;

              .item-label {
                font-size: 14px;
                line-height: 14px;
                color: #fff;
              }

              :deep(.el-icon) {
                cursor: pointer;
              }

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }

      :deep(.sel-btn) {
        margin-left: 8px;
        background: url('@/assets/images/strengthenMaintenance/active-tab.png') no-repeat;
        background-position: center center;
        background-size: 100% 100%;
        border: none;

        .el-icon {
          color: #7ff !important;
        }

        span {
          color: #7ff !important;
        }
      }
    }
  }
}
</style>
