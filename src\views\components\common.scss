// 地图右上方菜单
.map-menu {
  position: fixed;
  top: 106px;
  right: 16px;
  padding: 0 12px;
  background: $bg-page;
  border-radius: 16px;
  .map-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 14px 0;
    border-bottom: 1px solid $border-color1;
    &:last-child {
      border-bottom: none;
    }
    .map-item-icon {
      width: 40px;
      height: 40px;
      margin-bottom: 7px;
    }
    .map-item-label {
      font-size: 24px;
      line-height: 32px;
      color: $color-text-main;
    }
  }
}
.map-tip {
  position: fixed;
  top: 106px;
  left: 16px;
  display: flex;
  align-items: center;

  // width: 400px;
  padding: 8px 12px;
  font-size: 24px;
  line-height: 30px;
  background: $bg-page;
  border-radius: 16px;
  box-shadow: 0 0 10px 2px rgb(0 0 0 / 30%);
}

// 地图下方面板
.map-sheet {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 0 15px;

  // background: url('~@/assets/patrol/bg.png') no-repeat 0 0;
  background-size: 100% 100%;
  .map-sheet-content {
    border-radius: 30px 30px 0 0;
    .sheet-content_detail {
      display: flex;
      align-items: center;
      padding: 60px 32px 8px;
      background: $bg-page;
      border-radius: 30px 30px 0 0;
      .detail-item {
        flex: 1;
        text-align: center;
        .item-value {
          font-size: 30px;
          font-weight: 500;
          color: $color-text-black;
        }
        .item-label {
          margin-top: 8px;
          font-size: 24px;
          font-weight: 400;
          color: $color-text-vice;
        }
      }
    }
    .sheet-content_option {
      display: flex;
      align-items: center;
      padding: 26px 32px;
      background: $bg-page;
      border-radius: 0 0 30px 30px;
      .option-btn {
        flex: 1 1 40%;
        margin-right: 22px;
        color: $color-text-main;
        background-image: linear-gradient(180deg, #ffffff 0%, #edf1fb 100%);
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .sheet-content_action {
      padding: 24px 20px;
    }
  }
}

// 页面下方按钮
.bottom-btn {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  padding: 24px 35px;
  background: $bg-page;
  border-top: 1px solid $border-color1;
  .btn-item {
    flex: 1;
    margin-right: 30px;
    &:last-child {
      margin-right: 0;
    }
  }
}

// 自定义switch
.my-switch {
  position: relative;
  border: none;
  &::v-deep {
    &.van-switch {
      &::after {
        position: absolute;
        top: 50%;
        right: 20px;
        font-size: 24px;
        color: $color-success;
        content: '正常';
        transform: translateY(-50%);
      }
      .van-switch__node {
        box-sizing: border-box;
        border: 3px solid $color-success;
      }
    }
    &.van-switch--on {
      &::after {
        position: absolute;
        top: 50%;
        left: 20px;
        font-size: 24px;
        color: $color-error;
        content: '异常';
        transform: translateY(-50%);
      }
      .van-switch__node {
        border: 3px solid $color-error;
      }
    }
    &.van-switch--disabled {
      opacity: 1;
      &::after {
        position: absolute;
        top: 50%;
        right: auto;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .van-switch__node {
        display: none;
      }
    }
  }
}
