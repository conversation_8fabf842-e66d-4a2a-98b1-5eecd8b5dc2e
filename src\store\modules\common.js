import { $appApiUrl } from '@/appPlugins/appApiUrl.js';
import { $appAjax } from '@/appPlugins/appApiRequest.js';
import { getAllOptionMap } from '@/api/common.js';

const state = {
  areaList: [], // 区列表
  allOptionMap: {}, // 数据字典
  basinList: [] // 流域列表
};

const getters = {
  getOptions: state => type => {
    if (state.allOptionMap[type]) {
      return state.allOptionMap[type].map(option => ({
        label: option.name,
        value: option.code
      }));
    }
    return [];
  }
};

const mutations = {
  setAreaList(state, val) {
    state.areaList = val;
  },
  setAllOptionMap(state, val) {
    state.allOptionMap = val;
  },
  setBasinList(state, val) {
    state.basinList = val;
  }
};

const actions = {
  // 获取所有数据
  async getAreaList({ commit, state }) {
    if (state.areaList.length) return;
    const url = $appApiUrl.defaultUrl + '/area/getAreaList';
    const res = await $appAjax.get(url);
    commit('setAreaList', res.data || []);
    // return res.data;
  },
  // 获取数据字典
  getAllOption({ commit }) {
    return getAllOptionMap().then(res => {
      if (res.status === 200 && res.data) {
        commit('setAllOptionMap', res.data);
      }
      return res;
    });
  }
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
};
