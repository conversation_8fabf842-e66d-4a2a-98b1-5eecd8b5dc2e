<template>
  <div class="location-report flex-column">
    <CustomNavBar> </CustomNavBar>
    <van-tabs v-model="currentComponent" v-if="rankId">
      <van-tab title="抢险队伍" :name="1"> </van-tab>
      <van-tab title="物资运送" :name="2"></van-tab>
      <van-tab title="人员转移" :name="3"></van-tab>
    </van-tabs>

    <div class="com flex-1">
      <Common v-if="rankId" :key="currentComponent" :type="currentComponent" :rank-id="rankId">
        <template #info="{ data }">
          <div class="extra-box" v-if="currentComponent === 2">
            <div class="flex j-sb">
              <span class="extra-title">物资信息</span>
              <span @click="chooseMaterial(data)">编辑</span>
            </div>
            <div class="extra-content">{{ data.material }}</div>
          </div>
          <div class="extra-box" v-if="currentComponent === 3">
            <div class="extra-title">转移人数</div>
            <van-field
              v-model.trim="data.num"
              type="number"
              clearable
              placeholder="请输入转移人数"
              name="data.num"
              :rules="[{ required: true, message: '请输入转移人数' }]"
            ></van-field>
          </div>
        </template>
      </Common>
      <!-- <component :is="currentComponent" :rank-id="rankId"></component> -->
      <CustomEmpty v-else description="暂无队伍信息" />
    </div>
    <MaterialInfo ref="materialInfo" @confirm="confirm" />
  </div>
</template>

<script>
import Common from './components/common.vue';
import MaterialInfo from './components/materialInfo.vue';
import { getRankInfo } from '@/api/patrolManage/locationReport';
export default {
  components: {
    Common,
    MaterialInfo
  },
  data() {
    return {
      currentComponent: 1,
      rankId: null,
      materialInfo: {
        material: ''
      }
    };
  },
  computed: {},
  methods: {
    chooseMaterial(data) {
      this.materialInfo = data;
      this.$refs.materialInfo.show(data.material);
    },
    confirm(material) {
      this.materialInfo.material = material;
    }
  },
  created() {
    getRankInfo().then(res => {
      if (res.status === 200) {
        this.rankId = res.data?.rankId;
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.location-report {
  position: relative;
  height: 100%;
  .com {
    overflow: hidden;
  }
  .extra-box {
    padding: 0 20px;
    margin-bottom: 20px;
    .extra-title {
      margin-bottom: 15px;
      font-size: 32px;
      font-weight: 500;
    }
    .extra-content {
      min-height: 60px;
      padding: 20px;
      border: 1px solid $border-color1;
    }
    .van-cell {
      background: #f4f5f6;
    }
  }
}
</style>
