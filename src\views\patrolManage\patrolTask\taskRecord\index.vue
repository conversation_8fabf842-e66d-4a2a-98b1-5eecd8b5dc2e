<template>
  <!-- 已提交情况 -->
  <main class="main flex-column">
    <CustomNavBar />
    <!-- <CustomSearch v-model="searchForm.keyWord" show-action @search="onSearch" @cancel="showFilterPopup = true" /> -->
    <van-pull-refresh ref="content" class="section flex-1" v-model="refresh" @refresh="onSearch">
      <van-list class="content-list" v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
        <RecordCell v-for="data in dataList" :key="data.id" :data="data" @delete="deleteEvent" />
      </van-list>
    </van-pull-refresh>
    <!-- <van-popup v-model="showFilterPopup" position="right" :style="{ height: '100%', width: '60%' }">
      <RecordFilter :data="searchForm" @on-choose-time="chooseTime" @on-confirm="confirmFilterEvent" @on-cancel="showFilterPopup = false" />
    </van-popup> -->

    <!-- <TimeSelect v-model="showTimePopup" @confirm="confirmTimeEvent" :time="searchForm[timeKey]" /> -->
  </main>
</template>

<script>
// import CustomSearch from '@/components/CustomSearch';
// import TimeSelect from '@/views/earlyWarning/components/TimeSelect.vue';
// import RecordFilter from './recordFilter.vue';
import RecordCell from './recordCell.vue';
import { getStnList, delStnById } from '@/api/patrolManage/patrolProblem/index.js';
export default {
  name: 'TaskRecord',
  components: {
    RecordCell
    // CustomSearch,
    // RecordFilter,
    // TimeSelect
  },
  props: {},
  data() {
    return {
      taskId: null,
      dataList: [],
      refresh: false,
      loading: false, // 列表是否处于加载状态
      finished: true // 列表是否加载完成
    };
  },
  computed: {},
  watch: {},
  created() {
    const param = this.$route.query;
    this.taskId = +param.taskId;
    this.loadData();
  },
  mounted() {},
  methods: {
    loadData() {
      getStnList({ taskId: this.taskId }).then(res => {
        if (res.status === 200) {
          this.dataList = res.data || [];
        }
      });
    },
    deleteEvent(data) {
      this.$dialog
        .confirm({
          title: '提示',
          message: `是否确认删除？`
        })
        .then(() => {
          delStnById([data.id]).then(res => {
            if (res.status === 200) {
              this.$userApp.toast.show({
                text: '删除成功！',
                type: 'text'
              });
            }
          });
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  height: 100%;
}
.section {
  padding: 32px 32px 0;
  overflow: auto;
  background: $bg-page-gray;
}
</style>
