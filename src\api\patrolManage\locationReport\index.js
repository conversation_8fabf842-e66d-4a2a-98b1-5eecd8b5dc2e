// 位置上报
import http from '@/utils/apiRequestType';
import apiUrl from '@/utils/apiUrl';

const { defaultUrl } = apiUrl;

/**
 * @description: 队伍信息
 * @param {object} data
 * @return {object}
 */
export function getRankInfo(data) {
  return http.post(defaultUrl + '/jkyj/AppRescue/rankInfo', data);
}

/**
 * @description: 位置信息
 * @param {object} params
 * @return {object}
 */
export function getLocationInfo(params) {
  return http.get(defaultUrl + '/jkyj/AppRescue/getAddr', params);
}

/**
 * @description: 添加位置信息
 * @param {object} data
 * @return {object}
 */
export function addLocationInfo(data) {
  return http.post(defaultUrl + '/jkyj/AppRescue/addAddr', data);
}

/**
 * @description: 删除位置信息
 * @param {object} params
 * @return {object}
 */
export function deleteAddressInfo(params) {
  return http.get(defaultUrl + '/jkyj/AppRescue/delAddr', params);
}
