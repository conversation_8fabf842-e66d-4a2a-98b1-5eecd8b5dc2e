/**
 * 用法: 默认absolute定位
 * <div v-draggable></div>
 * <div v-draggable.fixed></div>
 */
export default {
  inserted: function(el, binding) {
    console.log('binding', binding);
    el.style.cursor = 'move';
    el.style.position = binding.modifiers.fixed ? 'fixed' : 'absolute'; //
    el.style.zIndex = 9;
    el.ontouchstart = function(e) {
      let disX = e.touches[0].pageX - el.offsetLeft;
      let disY = e.touches[0].pageY - el.offsetTop;
      document.ontouchmove = function(e) {
        let x = e.touches[0].pageX - disX;
        let y = e.touches[0].pageY - disY;
        let maxX = el.parentNode.offsetWidth - el.offsetWidth;
        let maxY =
          (el.parentNode.offsetHeight ? el.parentNode.offsetHeight : el.parentNode.parentNode.offsetHeight) - el.offsetHeight;
        if (x < 0) {
          x = 0;
        } else if (x > maxX) {
          x = maxX;
        }
        if (y < 0) {
          y = 0;
        } else if (y > maxY) {
          y = maxY;
        }
        el.style.left = x + 'px';
        el.style.top = y + 'px';
      };
      document.ontouchend = function() {
        document.ontouchmove = document.ontouchend = null;
      };
    };
  }
};
