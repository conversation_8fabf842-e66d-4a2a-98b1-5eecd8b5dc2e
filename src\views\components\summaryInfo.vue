<template>
  <div class="summary">
    <div class="summary-item" :style="item.bgStyle" v-for="(item, index) in list" :key="index" @click="clickHandle">
      <div class="item-label">
        <img class="label-icon" :src="require(`@/assets/images/${item.icon}`)" alt="" />
        <span class="label-text">{{ item.label }}</span>
      </div>
      <div class="item-value">{{ item.value || 0 }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'summaryInfo',
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    clickHandle() {
      this.$emit('onClickHandle');
    }
  }
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 15px;
  color: #ffffff;
  .summary-item {
    flex: 1;
    height: 125px;
    padding: 24px 20px;
    margin-right: 16px;
    &:last-child {
      margin-right: 0;
    }
    .item-label {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      .label-icon {
        width: 32px;
        height: 32px;
        margin-right: 3px;
      }
      .label-text {
        // font-weight: 500;
        font-size: 24px;
      }
    }
    .item-value {
      // color: $color-text-main;
      padding: 0 8px;
      font-size: 30px;
      font-weight: 500;
    }
  }
}
</style>
