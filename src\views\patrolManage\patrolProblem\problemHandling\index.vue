<template>
  <!-- 隐患情况 -->
  <main class="main flex-column">
    <CustomNavBar />
    <div class="header" :class="{ 'header--done': stnHdWay === 1 }">
      <h4 class="header-title">{{ formData.xjName }}</h4>
      <div v-if="stnHdWay === 1" class="header-vice">处理操作人：{{ formData.stnHdUserName }}</div>
      <div v-if="stnHdWay === 1" class="header-vice">处理时间：{{ formData.stnHdTime }}</div>
    </div>
    <section class="section flex-1" :class="{ 'section-bottom': stnHdWay !== 1 }">
      <HandingCondition class="section-card" :data="formData" />

      <HandingDetail v-if="stnHdWay === 1" class="section-card" :data="formData" />

      <van-form v-else ref="ruleForm" class="section-content flex-1" validate-first>
        <van-field name="radio" label="处理方式">
          <template #input>
            <van-radio-group v-if="stnHdWay === 0" v-model="formData.stnHdWay" direction="horizontal">
              <van-radio v-for="item in typeOptions" :key="item.value" :name="item.value">{{ item.label }}</van-radio>
            </van-radio-group>
            <span v-else>{{ formData.stnHdWay | filterWay(typeOptions) }}</span>
          </template>
        </van-field>
        <van-field
          v-if="formData.stnHdWay === 1 || stnHdWay === 2"
          v-model="formData.stnHdRefer"
          rows="4"
          autosize
          label="处理描述"
          type="textarea"
          placeholder="请输入"
          show-word-limit
          maxlength="50"
          required
          :rules="[{ required: true, message: '请输入处理描述' }]"
        />
        <van-field v-if="formData.stnHdWay === 1 || stnHdWay === 2" name="radio" label="处理照片">
          <template #input>
            <CustomUpload list-type="picture" v-model="formData.clAttachList" :module-id="9" module="xjxc" />
          </template>
        </van-field>
      </van-form>
    </section>
    <BottomBtn v-if="stnHdWay !== 1" @confirm="submit" @cancel="onCancel" />
  </main>
</template>

<script>
import BottomBtn from '@/components/BottomBtn.vue';
import CustomUpload from '@/components/CustomUpload/index.vue';
import HandingCondition from './handingCondition.vue';
import HandingDetail from './handingDetail.vue';
import { getStnInfo, editStn } from '@/api/patrolManage/patrolProblem/index.js';
import { formatOptions } from '../../options';
export default {
  name: 'ProblemHandling',
  components: {
    BottomBtn,
    CustomUpload,
    HandingCondition,
    HandingDetail
  },
  props: {},
  data() {
    return {
      id: null,
      stnHdWay: null, // 0：待处理，1：已完成，2：处理中
      title: '这是一个水闸的名称',
      typeOptions: [
        {
          label: '已处理',
          value: 1
        },
        {
          label: '问题需上报',
          value: 2
        }
      ],
      formData: {
        wtAttachList: [],
        clAttachList: []
      }
    };
  },
  computed: {},
  watch: {},
  filters: {
    filterWay(val, typeOptions) {
      return formatOptions(val, typeOptions);
    }
  },
  created() {
    const param = this.$route.query;
    this.id = +param.id;
    this.stnHdWay = +param.stnHdWay;
    this.loadData();
  },
  mounted() {},
  methods: {
    loadData() {
      getStnInfo({ id: this.id }).then(res => {
        if (res.status === 200 && res.data) {
          this.formData = res.data;
          if (this.stnHdWay === 0) {
            this.formData.stnHdWay = 1;
          }
        }
      });
    },
    submit() {
      this.$refs.ruleForm.validate().then(() => {
        if (this.stnHdWay === 0 && this.formData.stnHdWay === 2) {
          this.formData.stnHdRefer = null;
          this.formData.clAttachList = [];
        } else if (this.stnHdWay === 2) {
          this.formData.stnHdWay = 1;
        }
        editStn(this.formData).then(res => {
          if (res.status === 200) {
            this.$userApp.toast.show({
              text: '已上报情况',
              type: 'text'
            });
            setTimeout(() => {
              this.$router.back();
            }, 500);
          }
        });
      });
    },
    onCancel() {
      this.$router.back();
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, $color-primary 0%, $color-primary 70%, transparent 100%) no-repeat 0 0;
  background-size: 100% 626px;
}
.header {
  padding: 32px;
  .header-title {
    font-size: 36px;
    font-weight: 500;
    line-height: 44px;
    color: $color-text-white;
  }
  .header-vice {
    margin-top: 45px;
    font-size: 28px;
    line-height: 36px;
    color: rgba($color-text-white, 0.8);
    & ~ .header-vice {
      margin-top: 8px;
    }
  }
}
.header--done {
  background: url('~@/assets/images/patrol/patrolProblem/done.png') no-repeat right 50px center;
  background-size: 149px 152px;
}
.section {
  overflow: auto;

  // overflow: overlay;
  .section-card {
    box-sizing: border-box;
    margin: 0 32px 32px;
  }
}
.section-bottom {
  padding-bottom: 145px;
}
</style>
