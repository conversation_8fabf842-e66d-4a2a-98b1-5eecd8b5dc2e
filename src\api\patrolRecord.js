import apiUrl from '@/utils/apiUrl';
import appAjax from '@/utils/apiRequestType';
const defaultUrl = apiUrl.defaultUrl;

/**
 * 获取巡查记录列表 - 分页
 * @param data
 * "projectId": 工程id
 */
export function getGcxcList(data) {
  return appAjax.post(
    `${defaultUrl}/operaEngineeringInspection/getGcxcList?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  );
}

/**
 * 获取巡查7日统计
 */
export function getPatrolStatistics(data) {
  return appAjax.post(`${defaultUrl}/operaEngineeringInspection/sevenDayChart`, data);
}

// 获取巡检统计数据
export function getPatrolStatisticsData(data) {
  return appAjax.post(`${defaultUrl}/operaEngineeringInspection/currentDayChart`, data);
}
