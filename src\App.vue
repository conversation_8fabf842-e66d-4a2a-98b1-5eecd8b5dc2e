<template>
  <div id="app">
    <keep-alive :include="includesRoute">
      <router-view v-if="$route.meta.isKeepAlive" />
    </keep-alive>
    <router-view v-if="!$route.meta.isKeepAlive" />
  </div>
</template>
<script>
import myNJS from '@/utils/myNJS';
import { getStorage } from '@/utils/storage';
// import updateApp from '@/mixins/updateApp.js';

export default {
  name: 'app',
  components: {},
  // mixins: [updateApp],
  data() {
    return {};
  },
  computed: {
    includesRoute() {
      let routes = [];
      if (this.$route.name !== 'Index') {
        this.$router.options.routes.forEach(route => {
          if (route.meta.isKeepAlive) {
            routes.push(route.name);
          }
        });
      }
      return routes;
    }
  },
  created() {},
  mounted() {
    if (window.plus) {
      this.plusReady();
    } else {
      document.addEventListener('plusready', this.plusReady, false);
    }
  },
  methods: {
    plusReady() {
      document.addEventListener(
        'pause',
        () => {
          if (this.$store.state.patrol.watchId) {
            if (plus.os.name == 'Android') {
              myNJS.aOSReceive();
              setTimeout(() => {
                myNJS.aOSNotify('后台巡查中', '切换到后台将导致巡查轨迹不准确，请保持青山嘴水库程序在前台运行');
              }, 300);
            } else {
              plus.push.createMessage('切换到后台将导致巡查轨迹不准确，请保持青山嘴水库程序在前台运行。', '', {
                cover: true,
                delay: 0,
                title: '巡查提醒'
              });

              const allMsg = plus.push.getAllMessage();
              setTimeout(() => {
                plus.push.remove(allMsg[0]);
              }, 5000);
            }
          }
        },
        false
      );
      document.addEventListener(
        'resume',
        () => {
          if (this.$store.state.patrol.watchId) {
            if (plus.os.name == 'Android') {
              setTimeout(() => {
                myNJS.aOSNotify('巡查进行中', '切换到后台将导致巡查轨迹不准确，请保持应用在前台运行');
              }, 300);
            }
            // 定位时应用切后台，一段时间再切回来时有可能定位到刚开始切后台的位置，所以延迟20s再获取定位
            this.$store.commit('patrol/setInterval', 20000);
            setTimeout(() => {
              this.$store.commit('patrol/setInterval', 10000);
            }, 20000);
          }
        },
        false
      );
    }
  },
  destroyed() {}
};
</script>
<style lang="scss">
@import './style/index.scss';
</style>
