export function formatOptions(code, options) {
  const option = options.find(op => op.value === code);
  if (code) {
    return option.label;
  }
  return code;
}

export const typeOptions = [
  {
    label: '日常',
    value: 1
  },
  {
    label: '定期',
    value: 2
  },
  {
    label: '特殊',
    value: 3
  }
];

export const statusOptions = [
  {
    label: '执行中',
    value: 1
  },
  {
    label: '已完成',
    value: 2
  }
];

export const dangerOptions = [
  {
    label: '无隐患',
    value: 1
  },
  {
    label: '有隐患',
    value: 2
  }
];

export const situationOptions = [
  {
    label: '正常',
    value: 1
  },
  {
    label: '异常',
    value: 0
  },
  {
    label: '已整改',
    value: 2
  }
];

export const handingOptions = [
  {
    label: '待处理',
    value: 0
  },
  {
    label: '已处理',
    value: 1
  },
  {
    label: '处理中',
    value: 2
  }
];

// 巡查路线
/*
水库管理所 码头 输水隧洞闸室 泄洪隧洞闸室 主坝上游坝坡 主坝坝顶 主坝下游坝坡一级戗台 主坝堆石棱体 主坝排水沟 主坝渗流观测堰 枢纽扰动区 泄洪隧洞、输水隧洞出口 坝后电站 输水隧洞蝶阀房 溢洪道消力池 枢纽区后门 防汛物资仓库 副坝 溢洪道闸室 水库配电房 水库长廊亭子 水库气象观测站 水库观景台
*/
export const routeLine = [
  {
    pointName: '水库管理所（巡查起点）'
  },
  {
    pointName: '码头'
  },
  {
    pointName: '输水隧洞闸室'
  },
  {
    pointName: '泄洪隧洞闸室'
  },
  {
    pointName: '主坝上游坝坡'
  },
  {
    pointName: '主坝坝顶'
  },
  {
    pointName: '主坝下游坝坡一级戗台'
  },
  {
    pointName: '主坝堆石棱体'
  },
  {
    pointName: '主坝排水沟'
  },
  {
    pointName: '主坝渗流观测堰'
  },
  {
    pointName: '枢纽扰动区'
  },
  {
    pointName: '泄洪隧洞、输水隧洞出口'
  },
  {
    pointName: '坝后电站'
  },
  {
    pointName: '输水隧洞蝶阀房'
  },
  {
    pointName: '溢洪道消力池'
  },
  {
    pointName: '枢纽区后门'
  },
  {
    pointName: '防汛物资仓库'
  },
  {
    pointName: '副坝'
  },
  {
    pointName: '溢洪道闸室'
  },
  {
    pointName: '水库配电房'
  },
  {
    pointName: '水库长廊亭子'
  },
  {
    pointName: '水库气象观测站'
  },
  {
    pointName: '水库观景台（巡查终点）'
  }
];
