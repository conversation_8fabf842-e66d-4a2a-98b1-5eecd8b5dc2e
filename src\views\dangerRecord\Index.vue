<template>
  <div class="page-index danger-record flex-column">
    <van-nav-bar title="隐患记录" fixed right-text="返回" @click-right="onClickLeft" />
    <div class="main flex-column">
      <div class="nav">
        <div class="nav-item" @click="toPage({ name: item.pathName, type: 'add' })" v-for="(item, index) in navList" :key="index">
          <img v-if="item.icon" class="item-icon" :src="require(`@/assets/images/${item.icon}`)" alt="" />
          <div class="item-text">{{ item.title }}</div>
        </div>
      </div>

      <summary-info :list="summaryInfo"></summary-info>

      <div class="content">
        <van-tabs
          v-model="activeTab"
          color="#3E80ED"
          title-active-color="#3E80ED"
          title-inactive-color="#455166"
          @click="onRefresh"
        >
          <van-tab
            v-for="item in tabList"
            :key="item.value"
            title-class="tab-title"
            :title="item.name"
            :name="item.value"
          ></van-tab>
        </van-tabs>

        <van-pull-refresh class="content-detail" v-model="options.isLoading" @refresh="onRefresh">
          <van-list
            v-model="options.loading"
            :finished="options.finished"
            :finished-text="list.length ? '没有更多了' : ''"
            @load="onLoad"
          >
            <div class="detail-item" v-for="item in list" :key="item.id" @click="toDetail(item)">
              <div class="item-left">
                <div>
                  <div>{{ item.fssj | formatTime }}</div>
                  <div class="text-mini text-bottom">发生时间</div>
                </div>
                <div class="text-large">
                  {{ item.szwz || '--' }}
                </div>
                <div class="text-mini">位置</div>
              </div>
              <div class="item-right">
                <div class="right-row">
                  <div class="text-with-icon text-mini icon-time">状态</div>
                  <div class="text-with-icon text-mini icon-person">排查时间</div>
                </div>
                <div class="right-row text-bottom">
                  <div class="text-with-icon">{{ item.status | situation }}</div>
                  <div class="text-with-icon">{{ item.pcsj | formatTime }}</div>
                </div>
                <div>
                  <div class="text-with-icon text-mini icon-problem">隐患情况</div>
                  <div class=" text-with-icon text-with-icon">{{ item.yhqk || '--' }}</div>
                </div>
              </div>
            </div>
          </van-list>
          <CustomEmpty v-if="!list.length" description="暂无隐患记录" />
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import SummaryInfo from '@/views/components/summaryInfo.vue';
import listLoading from '@/mixins/listLoading';
import dayjs from 'dayjs';
import { getDangerRecordList, getDangerRecordCount } from '@/api/dangerRecord';

export default {
  name: 'DangerRecord',
  mixins: [listLoading],
  components: {
    SummaryInfo
  },
  data() {
    return {
      navList: [
        {
          title: '隐患上报',
          icon: 'dangerRecord/upload.png',
          pathName: 'DangerUpload'
        },
        {
          title: '巡查记录',
          icon: 'dangerRecord/record.png',
          pathName: 'PatrolRecord'
        },
        {}
      ],
      summaryInfo: [
        {
          value: '0',
          label: '本日隐患数',
          color: '#3E80ED',
          icon: 'dangerRecord/danger.png',
          bgStyle: {
            background: `url(${require('@/assets/images/dangerRecord/danger-bg.png')}) no-repeat 0 0`,
            backgroundSize: '100% 100%'
          }
        },
        {
          value: '0',
          label: '隐患完成率',
          color: '#02CA74',
          icon: 'dangerRecord/success.png',
          bgStyle: {
            background: `url(${require('@/assets/images/dangerRecord/success-bg.png')}) no-repeat 0 0`,
            backgroundSize: '100% 100%'
          }
        },
        {
          value: '0',
          label: '未整改隐患',
          color: '#FF0000',
          icon: 'dangerRecord/error.png',
          bgStyle: {
            background: `url(${require('@/assets/images/dangerRecord/error-bg.png')}) no-repeat 0 0`,
            backgroundSize: '100% 100%'
          }
        }
      ],
      activeTab: 'all',
      tabList: [
        { name: '全部', value: 'all' },
        { name: '已整改', value: 1 },
        { name: '未整改', value: 0 }
      ],
      list: [] // 列表数据
    };
  },
  filters: {
    formatTime(val, fmt = 'YYYY-MM-DD') {
      if (!val) return '--';
      return dayjs(val).format(fmt);
    },
    situation(val) {
      return val || val === 0 ? (val === 0 ? '未整改' : '已整改') : '--';
    }
  },
  computed: {
    ...mapState('user', ['projectInfo']),
    ...mapState({
      userInfo: state => state.user.userInfo || getStorage('userInfo')
    })
  },
  methods: {
    onClickLeft() {
      this.$router.back();
    },
    toPage(item) {
      let { name, ...rest } = item;
      if (name) {
        this.$router.push({
          name,
          params: { ...rest }
        });
      }
    },
    toDetail(row) {
      this.toPage({ name: 'DangerUpload', type: 'edit', ...row });
    },
    // 获取统计数据
    gcCounts() {
      getDangerRecordCount({
        areaCode: this.projectInfo.areaCode,
        projectType: this.projectInfo.projectType,
        wrpcd: this.userInfo.assignWrpcdList?.[0] || this.projectInfo.wrpcd
      }).then(res => {
        this.$set(this.summaryInfo[0], 'value', res.data['本日隐患数']);
        this.$set(this.summaryInfo[1], 'value', res.data['隐患完成率']);
        this.$set(this.summaryInfo[2], 'value', res.data['未整改隐患数']);
      });
    },
    // 获取列表数据
    getList() {
      getDangerRecordList({
        wrpcd: this.userInfo.assignWrpcdList?.[0] || this.projectInfo.wrpcd,
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        status: this.activeTab === 'all' ? undefined : this.activeTab
      }).then(res => {
        if (res.status === 200) {
          this.list = this.list.concat(res.data.list || []);
          this.total = res.data.total;
          this.options.finished = this.list.length >= this.total;
          this.options.isLoading = false;
          this.options.loading = false;
          this.pageNum += 1;
        }
      });
    }
  },
  created() {
    this.gcCounts();
  },
  mounted() {}
};
</script>

<style lang="scss" scoped>
@import '@/views/components/common';
@import './index';
</style>
