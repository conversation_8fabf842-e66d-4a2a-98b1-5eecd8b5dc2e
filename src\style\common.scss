/* flex */
.flx {
  display: flex;
}
.flx-hc {
  display: flex;
  justify-content: center;
}
.flx-shrink {
  flex-shrink: 0;
}
.flx-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flx-justify-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flx-align-center {
  display: flex;
  align-items: center;
}
.flx-column {
  display: flex;
  flex-direction: column;
}
.flx-evenly {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}
.flx-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.flx-1 {
  flex: 1;
}
.flx-end {
  display: flex;
  justify-content: flex-end;
}
.overflow-hide {
  overflow: hidden;
}
.scroll-container {
  position: relative;
  box-sizing: border-box;
  flex: 1;
  padding-right: 5px;
  overflow: hidden auto;
}

/* clearfix */
.clearfix::after {
  @extend #{'.overflow-hide'};

  display: block;
  height: 0;
  clear: both;
  content: '';
}
.full {
  width: 100%;
  height: 100%;
}
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
.h-0 {
  height: 0;
}

/* 文字单行省略号 */
.sle {
  @extend #{'.overflow-hide'};

  line-height: 1;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 文字多行省略号 */
.mle {
  @extend #{'.overflow-hide'};

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* 文字多了自动換行 */
.break-word {
  word-break: break-all;
  word-wrap: break-word;
}
.danger-color {
  color: #fc5a5a;
}
.fullscreen-box {
  position: fixed;
  top: 0;
  left: 100%;
  width: 100%;
  height: 100%;

  // background-color: #ffffff;
  transform: rotate(90deg) !important;
  transform-origin: 0 0;
}
.page-index {
  height: 100%;
  padding-top: 90px;
  background: $bg-page;
  &::v-deep {
    .van-nav-bar.van-hairline--bottom::after {
      border-bottom-width: 0;
    }
  }
}
