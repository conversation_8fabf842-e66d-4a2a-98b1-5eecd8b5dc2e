<template>
  <!-- <van-popup v-bind="$attrs" position="right" :style="{ height: '100%', width: '60%' }"> -->
  <div class="popup-content">
    <div class="card">
      <div class="card-title">任务状态</div>
      <div class="card-option">
        <div
          @click="selectKind(item)"
          class="option-item"
          :class="{ 'option-item_checked': item.value === formData.kindChecked }"
          v-for="item in kindOptions"
          :key="item.value"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <div class="card">
      <div class="card-title">检查类型</div>
      <div class="card-option">
        <div
          @click="selectType(item)"
          class="option-item"
          :class="{ 'option-item_checked': item.value === formData.typeChecked }"
          v-for="item in typeOptions"
          :key="item.value"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <div class="card">
      <div class="card-title">巡查时间</div>
      <div class="card-date">
        <van-field
          class="date-input"
          v-model="formData.startTime"
          right-icon="arrow-down"
          readonly
          placeholder="开始时间"
          @click="chooseTime('startTime')"
        />
        <van-field
          class="date-input"
          v-model="formData.endTime"
          right-icon="arrow-down"
          readonly
          placeholder="结束时间"
          @click="chooseTime('endTime')"
        />
      </div>
    </div>
    <div class="card" style="flex: 1">
      <div class="card-title">隐患情况</div>
      <div class="card-option">
        <div
          @click="selectStatus(item)"
          class="option-item"
          :class="{ 'option-item_checked': item.value === formData.statusChecked }"
          v-for="item in statusOptions"
          :key="item.value"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="popup-btn">
      <van-button @click="resetEvent" class="btn" size="large" :square="true">取消</van-button>
      <van-button @click="confirmEvent" class="btn" size="large" :square="true" type="info">确定</van-button>
    </div>
  </div>
  <!-- </van-popup> -->
</template>

<script>
export default {
  name: 'RecordFilter',
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      typeChecked: '', // 选择的类型
      typeOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '日常',
          value: '3'
        },
        {
          label: '定期',
          value: '2'
        },
        {
          label: '特别',
          value: '1'
        }
      ],
      statusChecked: '', // 选择的问题
      statusOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '执行中',
          value: '0'
        },
        {
          label: '已完成',
          value: '1'
        }
      ],
      kindChecked: '',
      kindOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '有隐患',
          value: 0
        },
        {
          label: '无隐患',
          value: 1
        }
      ]
    };
  },
  computed: {
    formData: {
      get() {
        return this.data;
      },
      set(val) {
        this.$emit('update:data', val);
      }
    }
  },
  methods: {
    // 选择类型
    selectType(item) {
      this.formData.typeChecked = item.value;
    },
    // 选择问题
    selectStatus(item) {
      this.formData.statusChecked = item.value;
    },
    // 选择状态
    selectKind(item) {
      this.formData.kindChecked = item.value;
    },
    // 打开选择时间弹窗
    chooseTime(time) {
      this.$emit('on-choose-time', time);
    },
    // 重置筛选条件
    resetEvent() {
      for (const key of Object.keys(this.formData)) {
        this.formData[key] = null;
      }
      this.$emit('on-cancel');
    },
    // 筛选确认事件
    confirmEvent() {
      this.$emit('on-confirm');
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../common.scss';
</style>
