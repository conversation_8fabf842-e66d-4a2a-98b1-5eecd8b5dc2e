.popup-content {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  font-size: 28px;
  line-height: 36px;
  color: $color-text-black;
  background: $bg-page-gray;
  .card {
    box-sizing: border-box;
    padding: 32px 32px 16px;
    margin-bottom: 16px;
    background: $bg-page;
    .card-title {
      margin-bottom: 16px;
      font-size: 32px;
      font-weight: 500;
      line-height: 40px;
    }
    .card-option {
      display: flex;
      flex-flow: row wrap;
      .option-item {
        box-sizing: border-box;
        width: calc(50% - 10px);
        padding: 14px 4px;
        margin-bottom: 16px;
        text-align: center;
        background: $bg-page-gray;
        border-radius: 32px;
        &:nth-child(odd) {
          margin-right: 20px;
        }
      }
      .option-item_checked {
        color: $color-text-white;
        background: $color-primary;
      }
    }
    .card-date {
      .date-input {
        box-sizing: border-box;
        padding: 14px 27px;
        margin-bottom: 20px;
        background: $bg-page-gray;
        border-radius: 32px;
      }
      &::v-deep {
        .van-cell::after {
          border-bottom: none;
        }
      }
    }
  }
  .popup-btn {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    .btn {
      width: 50%;
      font-size: 34px;
      font-weight: 500;
    }
  }
}
