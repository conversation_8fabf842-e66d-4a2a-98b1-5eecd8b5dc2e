<template>
  <div class="filterBox">
    <van-popup :value="value" @input="$emit('input', false)" position="right" class="popup">
      <div class="flex-column full-h">
        <main class="flex-1">
          <div class="address" v-for="item in options" :key="item.field">
            <div class="font32 mb16" style="font-weight:500">{{ item.title }}</div>
            <div class="addressSelect" v-if="!item.slot">
              <van-button
                round
                :type="getType(item, radioItem.name)"
                v-for="(radioItem, index) in item.radioList"
                :key="index"
                @click="handleSelected(item, radioItem.name)"
              >
                {{ radioItem.label }}
              </van-button>
            </div>
            <div class="" v-else>
              <slot :name="item.slot" :item="item"></slot>
            </div>
          </div>
        </main>

        <div class="bg-white flex">
          <span @click="$emit('input', false)" class="flex-1 flex-hvc bottom-btn bb">取消</span>
          <span @click="confirm" class="flex-1 flex-hvc bottom-btn bb bottom-btn--active">确定</span>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      required: true
    },
    options: {
      default: [
        {
          title: '行政区划',
          field: 'address',
          radioList: [
            { name: 'all', label: '全部' },
            { name: '1', label: '金平区' },
            { name: '2', label: '龙湖区' },
            { name: '3', label: '濠江区' },
            { name: '4', label: '潮阳区' },
            { name: '5', label: '潮南区' },
            { name: '6', label: '澄海区' },
            { name: '7', label: '南澳县' }
          ]
        },
        {
          title: '行政区划',
          field: 'address',
          slot: 'address'
        }
      ]
    }
  },
  data() {
    return {
      returnValue: {}
    };
  },
  methods: {
    confirm() {
      this.$emit('confirm', this.returnValue);
      this.$emit('input', false);
    },
    getType(option, name) {
      const selectedVal = this.returnValue[option.field];
      if (option.multiple) {
        if (Array.isArray(selectedVal)) {
          return selectedVal.includes(name) ? 'info' : 'default';
        }
        return 'default';
      }
      return selectedVal === name ? 'info' : 'default';
    },
    handleSelected(option, name) {
      let selectedVal = this.returnValue[option.field];
      if (option.multiple) {
        if (!Array.isArray(selectedVal)) {
          selectedVal = [];
        }
        if (selectedVal.includes(name)) {
          selectedVal.splice(selectedVal.indexOf(name), 1);
        } else {
          selectedVal.push(name);
        }

        this.$set(this.returnValue, option.field, selectedVal);
      } else {
        this.$set(this.returnValue, option.field, name);
      }
    }
  },
  created() {
    this.options.forEach(item => {
      this.$set(this.returnValue, item.field, item.value || undefined);
    });
  }
};
</script>

<style lang="scss" scoped>
.popup {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 80%;
  height: 100%;
}
main {
  height: 90%;
  overflow: auto;
  .address {
    padding: 32px;
    .addressSelect {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
  }
}
.bottom-btn {
  height: 98px;
  font-size: 34px;
  font-weight: 500;
  color: #000000e6;
  background: #f5f5f5;
  border-top: 1px solid #0000001f;
}
.bottom-btn--active {
  color: #ffffff;
  background: #266fe8;
}
</style>
