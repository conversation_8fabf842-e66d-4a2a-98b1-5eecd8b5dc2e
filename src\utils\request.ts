// 并发控制器
export class ConcurrencyController {
  private maxConcurrency: number
  private currentRunning: number = 0
  private queue: Array<() => Promise<void>> = []

  constructor(maxConcurrency: number = 4) {
    this.maxConcurrency = maxConcurrency
  }

  async execute<T>(task: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const wrappedTask = async () => {
        try {
          const result = await task()
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          this.currentRunning--
          this.processQueue()
        }
      }

      if (this.currentRunning < this.maxConcurrency) {
        this.currentRunning++
        // 使用 Promise.resolve().then() 确保任务异步执行
        Promise.resolve().then(wrappedTask)
      } else {
        this.queue.push(wrappedTask)
      }
    })
  }

  private processQueue() {
    if (this.queue.length > 0 && this.currentRunning < this.maxConcurrency) {
      const nextTask = this.queue.shift()
      if (nextTask) {
        this.currentRunning++
        // 使用 Promise.resolve().then() 确保任务异步执行
        Promise.resolve().then(nextTask)
      }
    }
  }
}
