青山嘴水库AI平台开放接口
1.	开放平台API用户调用令牌
 

API用户	token
青山嘴水库	2fe0dd6c1dd217982d50f33ffea43fbe
2.	智能接口模块说明
 
青山嘴水库的行政区划编码（addvcd）：	532301101 , 即“楚雄市鹿城镇”

2.1	获取水库智能识别列表信息接口
2.1.1	接口定义

接口方式	REST接口方式获取 POST
接口描述	获取水库智能识别列表信息
接口地址	https://web.digitwater.com:9043/dwaiapi/open/third/wrp/singlereal?
token=2fe0dd6c1dd217982d50f33ffea43fbe
2.1.2	请求参数

参数	描述
start	开始时间
end	结束时间
page	页码
size	每页大小
addvcd	532301101
2.1.3	返回参数

字段	类型	说明
code	int	500 失败 200成功
data	Array	单个水库智能分析信息
+ total	int	检测分析点数
+ more_analy_total	int	今周分析总次数
+ warn_total	int	发现问题数
+ page	object	
 
字段	类型	说明
++ count	int	记录数
++ total	Int	页数
++ page	Int	页码
++ size	Int	每页大小
+ list_data	Array	
++ stcd	String	测站编码
++ stnm	String	测站名称
++ detection_id	Int	功能ID
++ detection_name	String	功能名称
++ work_id	Int	业务ID
++ tm	String	最新时间
++ score	Decimal	置信度
++ value	Int	最新异常数
++ today_warn_total	Int	今日异常累计
++ mask_ratio	Decimal	占比
++ preset_id	String	预制点ID
++ preset_name	String	预置点名称
++ before_url	String	识别前（图像）
++ after_url	String	识别后（图像）
++ poster	String	封面（短视频）
++ short_video	String	短视频
++ index	Int	视频预置点编号
++ channel_no	Int	视频通道号
++ terminal_type	Int	终端短视频	0 不存在 1 存在
msg	string	返回说明
2.1.4	示例
URL: https://web.digitwater.com:9043/dwaiapi/open/third/wrp/singlereal? token=2fe0dd6c1dd217982d50f33ffea43fbe

POST:
 {
"start": "2025-05-01",
"end": "2025-06-30",
"page": 1,
"size": 100,
"addvcd": "532301101"
}

 
Response:
{
"data": {
"total": 10,
"more_analy_total": 1358,
"today_analy_total": 27,
"warn_total": 0, "page": {
"count": 10,
"total": 1,
"page": 1,
"size": 100
},
"list_data": [
{
"stcd": "HK_CXQSZ202505191512159450",
"stnm": "泄洪洞下游",
"wrpcd": "HK_CXQSZ202505191512159450",
"detection_id": 100024, "detection_name": "行人检测", "work_id": 100005,
"tm": "2025-06-09 14:51:10",
"score": 0.0,
"value": 0,
"today_warn_total": 0,
"today_total": 2,
"warn_total": 0,
"analy_total": 147,
"mask_ratio": 0.0,
"preset_id": "e4a5cf47-0bfd-4e8f-9683-1a977ba2c6d5", "preset_name": "行人检测",
"before_url": "https://web.digitwater.com:9043/aiplatform/test/work/100005/2025/0609/HK_CXQSZ20250 5191512159450/images/20250609145110.jpg",
"after_url": "https://web.digitwater.com:9043/aiplatform/test/work/100005/2025/0609/HK_CXQSZ20250 5191512159450/results/20250609145110.jpg",
"poster": null,
"short_video": "", "index": 2,
"channel_no": 1,
"terminal_type": 0
}
]
},
"code": 200,
"msg": ""
}

 
 

2.2	获取水库智能分析“异常”统计信息接口
2.2.1	接口定义

接口方式	REST接口方式获取 GET
接口描述	获取水库智能分析“异常”统计信息
接口地址	https://web.digitwater.com:9043/dwaiapi/openv2/warn/result/sk?addvcd=532301101&start=2025-05-20&end=2025-06-
30&token=2fe0dd6c1dd217982d50f33ffea43fbe
2.2.2	请求参数

参数	描述
addvcd	行政区划编码（532301101）
start	开始时间
end	结束时间
token	调用令牌（2fe0dd6c1dd217982d50f33ffea43fbe）
2.2.3	返回参数

字段	类型	说明
code	int	500 失败 200成功
data	Array	异常统计信息
+ model_code	int	模型代码
+ model_name	String	模型名称
+ total	int	数量
+ totalSk	int	水库数量
msg	string	返回说明
 
2.2.4	示例：
URL: https://web.digitwater.com:9043/dwaiapi/openv2/warn/result/sk? addvcd=532301101&start=2025-05-20&end=2025-06-30&token=2fe0dd6c1dd217982d50f33ffea43fbe

Response:
{
"data": [
{
"total": 3,
"totalSk": 3,
"model_code": 1008, "model_name": "坝体入侵"
}
],
"code": 200,
"msg": ""
}


2.3	获取水库智能分析“结果”统计信息接口
2.3.1	接口定义

接口方式	REST接口方式获取 GET
接口描述	获取智能分析“结果”统计信息
接口地址	https://web.digitwater.com:9043/dwaiapi/openv2/warn/class/sk?addvcd=532301101&start=2025-05-01&end=2025-
06-30&token=2fe0dd6c1dd217982d50f33ffea43fbe
2.3.2	请求参数

参数	描述
addvcd	行政区划编码（532301101）
token	调用令牌（2fe0dd6c1dd217982d50f33ffea43fbe）
start	开始时间
end	结束时间
2.3.3	返回参数

字段	类型	说明
code	int	500 失败 200成功
data	Array	结果统计信息
+ model_code	int	模型代码
 
字段	类型	说明
+ model_name	String	模型名称
+ analy_count	int	分析总次数
+ warn_num	int	预警宗数
+ warn_total	int	异常次数
msg	string	返回说明
2.3.4	示例
URL: https://web.digitwater.com:9043/dwaiapi/openv2/warn/class/sk? addvcd=532301101&start=2025-05-01&end=2025-06-30&token=2fe0dd6c1dd217982d50f33ffea43fbe

Response:
{
"data": [
{
"analy_count": 1115,
"warn_total": 3,
"warn_num": 6,
"model_code": 1008, "model_name": "坝体入侵"
},
{
"analy_count": 0,
"warn_total": 0,
"warn_num": 0,
"model_code": 1001, "model_name": "大坝杂草"
}
],
"code": 200,
"msg": ""
}


2.4	单个水库-获取智能分析“结果”信息接口
2.4.1	接口定义

接口方式	REST接口方式获取 POST
接口描述	单个水库-获取智能分析“结果”信息，包含统计信息
接口地址	https://web.digitwater.com:9043/dwaiapi/open/third/wrp/singlereal?token=2fe0dd6c1dd217982d50f33ffea43fbe
 
2.4.2	请求参数

参数	描述
wrpcd	工程编码
stcd	测站编码
start	开始时间
end	结束时间
page	页码
size	每页大小
2.4.3	返回参数

字段	类型	说明
code	int	500 失败 200成功
data	Array	单个水库智能分析信息
+ total	int	检测分析点数
+ more_analy_total	int	今周分析总次数
+ warn_total	int	发现问题数
+ page	object	
++ count	int	记录数
++ total	Int	页数
++ page	Int	页码
++ size	Int	每页大小
+ list_data	Array	
++ stcd	String	测站编码
++ stnm	String	测站名称
++ detection_id	Int	功能ID
++ detection_name	String	功能名称
++ work_id	Int	业务ID
++ tm	String	最新时间
++ score	Decimal	置信度
++ value	Int	最新异常数
 
字段	类型	说明
++ today_warn_total	Int	今日异常累计
++ mask_ratio	Decimal	占比
++ preset_id	String	预制点ID
++ preset_name	String	预置点名称
++ before_url	String	识别前（图像）
++ after_url	String	识别后（图像）
++ poster	String	封面（短视频）
++ short_video	String	短视频
++ index	Int	视频预置点编号
++ channel_no	Int	视频通道号
++ terminal_type	Int	终端短视频	0 不存在 1 存在
msg	string	返回说明
2.4.4	示例
URL: https://web.digitwater.com:9043/dwaiapi/open/third/wrp/singlereal? token=2fe0dd6c1dd217982d50f33ffea43fbe

Post:
{
"stcd": "HK_CXQSZ202505191512140542", "start": "2025-05-01",
"end": "2025-06-30",
"page": 1,
"size": 10
}

Response:
{
"data": {
"total": 1,
"more_analy_total": 127,
"today_analy_total": 6,
"warn_total": 0,
"page": {
"count": 1,
"total": 1,
"page": 1,
"size": 100
},
"list_data": [
{
"stcd": "HK_CXQSZ202505191512140542",
"stnm": "溢洪道下游",
"wrpcd": "HK_CXQSZ202505191512140542",
"detection_id": 100024, "detection_name": "行人检测", "work_id": 100005,
"tm": "2025-06-09 14:30:38",
"score": 0.0,
"value": 0,
"today_warn_total": 0,
"today_total": 6,
"warn_total": 1,
"analy_total": 127,
"mask_ratio": 0.0,
"preset_id": "1914451c-8890-46a3-93c7-7db1146381a4",
"preset_name": "行人检测", "before_url":
"https://web.digitwater.com:9043/aiplatform/test/work/100005/2025/0609/HK_CXQSZ20250 5191512140542/images/20250609143038.jpg",
"after_url":
"https://web.digitwater.com:9043/aiplatform/test/work/100005/2025/0609/HK_CXQSZ20250 5191512140542/results/20250609143038.jpg",
"poster": null, "short_video": "", "index": 1,
"channel_no": 1,
"terminal_type": 0
}
]
},
"code": 200,
"msg": ""
}

 
 

2.5	单个水库-获取智能分析“详细”信息接口
2.5.1	接口定义
 
接口方式	REST接口方式获取 GET
接口描述	单个水库-获取智能分析“详细”信息
接口地址	https://web.digitwater.com:9043/dwaiapi/openv2/warn/stationsingle/sk?
stcd=HK_CXQSZ202505191512140542&start=2025-05-01&end=2025-06-30&token=2fe0dd6c1dd217982d50f33ffea43fbe
2.5.2	请求参数

参数	描述
stcd	测站编码
token	调用令牌（2fe0dd6c1dd217982d50f33ffea43fbe）
start	开始时间
end	结束时间
model_code	模型代码
iden_types	识别类型 1：云端视频 2：终端视频 3：云端图像
2.5.3	返回参数

字段	类型	说明
code	int	500 失败 200成功
data	Array	单个水库-智能分析详细信息
+ model_data	Array	模型列表
++ model_code	int	模型代码
++ model_name	String	模型名称
+ detail_data	Array	分析详情
++ stcd	String	站点编码
++ stnm	String	站点名称
++ lng	Decimal	经度
++ lat	Decimal	纬度
++ addvcd	String	区域编码
++ addvnm	String	区域名称（上一级+本级）
++ faddvnm	String	区域全称
++ iden_types	Int	1：云端视频 2：终端视频 3：云端图像
++ tm	String	识别时间
++ labels	Array	识别的内容列表
+++ label_id	String	识别类型ID
 
字段	类型	说明
+++ label_name	String	识别类型名称
++ warn_num	Int	预警数
++ score	Decimal	置信度
++ value	Decimal	占比
++ before_url	string	识别前
++ after_url	String	识别后
++ poster	String	短视频首页
++ short_video	String	短视频
msg	string	返回说明
2.5.4	示例
URL: https://web.digitwater.com:9043/dwaiapi/openv2/warn/stationsingle/sk? stcd=HK_CXQSZ202505191512140542&start=2025-05-01&end=2025-06-
30&token=2fe0dd6c1dd217982d50f33ffea43fbe

Response:
{
"data": { "model_data": [
{
"model_code": 1008, "model_name": "坝体入侵"
}
],
"detail_data": [
{
"stcd": "HK_CXQSZ202505191512140542",
"stnm": "溢洪道下游", "lng": null,
"lat": 0.0,
"addvcd": "532301101",
"addvnm": "楚雄市鹿城镇",
"faddvnm": "云南省楚雄彝族自治州楚雄市鹿城镇",
"iden_types": 1,
"tm": "2025-06-04 15:59:33",
"labels": [
{
"label_id": "person", "label_name": "行人"
}
],
"warn_num": 1,
"score": 73.0,
"value": 0.0011, "before_url":
"https://web.digitwater.com:9043/aiplatform/test/work/100005/2025/0604/HK_CXQSZ20250 5191512140542/images/20250604155933.jpg",
"after_url": "https://web.digitwater.com:9043/aiplatform/test/work/100005/2025/0604/HK_CXQSZ20250 5191512140542/results/20250604155933.jpg",
"poster": "",
"short_video": ""
}
]
},
"code": 200,
"msg": ""
}

 
 

2.6	单个水库-获取智能分析“最新”信息接口
2.6.1	接口定义

接口方式	REST接口方式获取 POST
接口描述	单个水库-获取智能分析“最新”结果信息
接口地址	https://web.digitwater.com:9043/dwaiapi/open/third/wrp/singlelast?token=2fe0dd6c1dd217982d50f33ffea43fbe
2.6.2	请求参数

参数	描述
wrpcd	工程编码
stcd	测站编码
preset_id	预制点ID
2.6.3	返回参数

字段	类型	说明
code	int	500 失败 200成功
msg	string	返回说明
data	Array	单个水库-智能分析详细信息
+ stcd	String	测站编码
 
字段	类型	说明
+ stnm	String	测站名称
+ detection_id	Int	功能ID
+ detection_name	String	功能名称
+ work_id	Int	业务ID
+ tm	String	最新时间
+ score	Decimal	置信度
+ value	Int	最新异常数
+ mask_ratio	Decimal	占比
+ preset_id	String	预制点ID
+ preset_name	String	预置点名称
+ before_url	String	识别前（图像）
+ after_url	String	识别后（图像）
+ poster	String	封面（短视频）
+ short_video	String	短视频
+ index	Int	视频预置点编号
+ channel_no	Int	视频通道号
+ terminal_type	Int	终端短视频	0 不存在 1 存在
2.6.4	示例
URL: https://web.digitwater.com:9043/dwaiapi/open/third/wrp/singlelast? token=2fe0dd6c1dd217982d50f33ffea43fbe

POST:
{
"stcd": "HK_CXQSZ202505191512140542",
"preset_id": "1914451c-8890-46a3-93c7-7db1146381a4"
}

Response:
{
"data": [
{
"stcd": "HK_CXQSZ202505191512140542",
"stnm": "溢洪道下游",
"wrpcd": "HK_CXQSZ202505191512140542",
"detection_id": 100024, "detection_name": "行人检测", "work_id": 100005,
"tm": "2025-06-09 14:30:38",
"score": 0.0,
"value": 0, "today_warn_total": null, "today_total": null, "warn_total": null, "analy_total": null, "mask_ratio": 0.0,
"preset_id": "1914451c-8890-46a3-93c7-7db1146381a4",
"preset_name": "行人检测", "before_url":
"https://web.digitwater.com:9043/aiplatform/test/work/100005/2025/0609/HK_CXQSZ20250 5191512140542/images/20250609143038.jpg",
"after_url": "https://web.digitwater.com:9043/aiplatform/test/work/100005/2025/0609/HK_CXQSZ20250 5191512140542/results/20250609143038.jpg",
"poster": null,
"short_video": "", "index": 1,
"channel_no": 1,
"terminal_type": 0
}
],
"code": 200,
"msg": ""
}

 
 

2.7	单个水库-获取预置点智能分析“详细”信息接口
2.7.1	接口定义

接口方式	REST接口方式获取 POST
接口描述	单个水库-获取预置点智能分析“详细”信息接口
接口地址	https://web.digitwater.com:9043/dwaiapi/open/third/wrp/singlerang?token=2fe0dd6c1dd217982d50f33ffea43fbe
2.7.2	请求参数

参数	描述
preset_id	预制点ID
 
参数	描述
start	开始时间
end	结束时间
2.7.3	返回参数

字段	类型	说明
code	int	500 失败 200成功
msg	string	返回说明
data	Array	单个水库-智能分析详细信息
+ stcd	String	测站编码
+ stnm	String	测站名称
+ detection_id	Int	功能ID
+ detection_name	String	功能名称
+ work_id	Int	业务ID
+ tm	String	最新时间
+ score	Decimal	置信度
+ value	Int	最新异常数
+ mask_ratio	Decimal	占比
+ preset_id	String	预制点ID
+ preset_name	String	预置点名称
+ before_url	String	识别前（图像）
+ after_url	String	识别后（图像）
+ poster	String	封面（短视频）
+ short_video	String	短视频
+ index	Int	视频预置点编号
+ channel_no	Int	视频通道号
+ terminal_type	Int	终端短视频	0 不存在 1 存在
2.7.4	示例
URL: https://web.digitwater.com:9043/dwaiapi/open/third/wrp/singlerang? token=2fe0dd6c1dd217982d50f33ffea43fbe

POST:
 {
"preset_id": "1914451c-8890-46a3-93c7-7db1146381a4", "start": "2025-06-08",
"end": "2025-06-30"
}

 
Response:
{
"data": [
{
"stcd": "HK_CXQSZ202505191512140542",
"stnm": "溢洪道下游",
"wrpcd": "HK_CXQSZ202505191512140542",
"detection_id": 100024, "detection_name": "行人检测", "work_id": 100005,
"tm": "2025-06-09 14:30:38",
"score": 0.0,
"value": 0, "today_warn_total": null, "today_total": null, "warn_total": null, "analy_total": null, "mask_ratio": 0.0,
"preset_id": "1914451c-8890-46a3-93c7-7db1146381a4",
"preset_name": "行人检测", "before_url":
"https://web.digitwater.com:9043/aiplatform/test/work/100005/2025/0609/HK_CXQSZ20250 5191512140542/images/20250609143038.jpg",
"after_url":
"https://web.digitwater.com:9043/aiplatform/test/work/100005/2025/0609/HK_CXQSZ20250 5191512140542/results/20250609143038.jpg",
"poster": null, "short_video": "", "index": 1,
"channel_no": 1,
"terminal_type": 0
}
],
"code": 200,
"msg": ""
}

 
 
