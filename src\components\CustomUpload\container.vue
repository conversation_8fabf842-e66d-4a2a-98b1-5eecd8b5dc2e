<template>
  <!-- 上传组件UI部分 -->
  <div class="upload-container">
    <div class="file-list" v-if="listType === 'text' && previewImage">
      <div class="file-item" v-for="(item, index) in fileList" :key="item.id">
        <van-icon class="file-item-icon" name="description" />
        <div class="file-item-text van-ellipsis">{{ item.name }}</div>
        <div v-if="deletable" class="file-item-delete" @click="deleteFile(item, index)">
          <van-icon class="delete-icon" name="cross" />
        </div>
      </div>
    </div>
    <van-uploader
      v-bind="$attrs"
      v-on="$listeners"
      v-model="fileList"
      :preview-image="listType === 'picture' && previewImage"
      :deletable="deletable"
      upload-icon="plus"
    >
      <div v-if="listType === 'text'" class="upload-btn-box">
        <span>添加附件</span>
        <van-icon class="btn-icon" name="plus" />
      </div>
    </van-uploader>
  </div>
</template>

<script>
export default {
  name: 'UploadContainer',
  components: {},
  props: {
    value: {
      type: Array,
      required: true
    },
    listType: {
      type: String,
      default: 'picture',
      validator: value => {
        return ['picture', 'text'].includes(value);
      }
    },
    previewImage: {
      type: Boolean,
      default: true
    },
    deletable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  computed: {
    fileList: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {},
  methods: {
    isPromise(obj) {
      return obj && Object.prototype.toString.call(obj) === `[object Promise]`;
    },
    deleteFile(file, index) {
      const params = {
        name: this.$attrs.name,
        index
      };
      const callback = () => {
        this.fileList.splice(index, 1);
        this.$emit('delete', file, params);
      };

      if (this.$attrs.beforeDelete) {
        let response = this.$attrs.beforeDelete(file, params);

        if (!response) {
          return;
        }

        if (this.isPromise(response)) {
          response.then(() => callback());
          return;
        }
      }

      callback();
    }
  },
  created() {},
  mounted() {}
};
</script>

<style lang="scss" scoped>
.upload-container {
  .file-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 6px 30px 6px 8px;
    margin-right: 8px;
    margin-bottom: 8px;
    font-size: 24px;
    background-color: $bg-page-gray;
    border-radius: 4px;
    .file-item-icon {
      margin-right: 12px;
      font-size: 28px;
    }
    .file-item-text {
      line-height: 32px;
    }
    .file-item-delete {
      position: absolute;
      top: 0;
      right: 0;
      width: 28px;
      height: 28px;
      background-color: rgb(0 0 0 / 70%);
      border-radius: 0 4px 0 24px;
      .delete-icon {
        position: absolute;
        top: 4px;
        right: 4px;
        font-size: 16px;
        color: $color-text-white;
      }
    }
  }
  .upload-btn-box {
    display: flex;
    align-items: center;
    font-size: 28px;
    font-weight: 400;
    color: $color-primary;
    .btn-icon {
      margin-left: 16px;
    }
  }
  ::v-deep {
    .van-uploader__preview-image {
      border-radius: 12px;
    }
    .van-uploader__preview-delete {
      border-radius: 0 12px;
    }
    .van-uploader__upload {
      border-radius: 12px;
    }
  }
}
</style>
