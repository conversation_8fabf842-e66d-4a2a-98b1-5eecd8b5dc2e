<template>
  <div class="flex-vc custom-picker" @click="popupShow = true">
    {{ name }}
    <img class="picker-icon" src="@/assets/images/earlyWarning/caret-down-small.png" alt="" />
    <van-popup v-model="popupShow" get-container="body" position="bottom">
      <van-picker
        show-toolbar
        :value-key="labelKey"
        :columns="columns"
        :default-index="defaultIndex"
        @confirm="onConfirm"
        @cancel="popupShow = false"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'CustomPicker',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    columns: {
      type: Array,
      default: () => []
    },
    labelKey: {
      type: String,
      default: 'label'
    },
    valueKey: {
      type: String,
      default: 'value'
    }
  },
  data() {
    return {
      popupShow: false
    };
  },
  computed: {
    selectedValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    },
    name() {
      return this.columns.find(item => item[this.valueKey] === this.selectedValue)?.[this.labelKey] || '';
    },
    defaultIndex() {
      const index = this.columns.findIndex(item => item[this.valueKey] === this.selectedValue);
      return index === -1 ? 0 : index;
    }
  },
  methods: {
    onConfirm(row) {
      this.selectedValue = row[this.valueKey];
      this.$emit('confirm', row[this.valueKey], row);
      this.popupShow = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.custom-picker {
  padding: 12px 24px;
  .picker-icon {
    width: 40px;
    height: 40px;
  }
}
</style>
