<template>
  <!-- 上传组件业务部分，公共接口统一封装 -->
  <upload-container
    v-bind="$attrs"
    v-model="fileList"
    :after-read="afterRead"
    :before-delete="beforeDelete"
    @click-upload="beforeUpload"
  ></upload-container>
</template>

<script>
import UploadContainer from './container.vue';
import Compressor from 'compressorjs';
import { previewUrl, uploadFile, deleteFile } from '@/api/common.js';

export default {
  name: 'CustomUpload',
  components: {
    UploadContainer
  },
  props: {
    value: {
      type: Array,
      required: true
    },
    module: {
      type: String,
      default: 'common'
    }
  },
  data() {
    return {};
  },
  computed: {
    fileList: {
      get() {
        return this.value.map(i => {
          return {
            name: i.mc,
            url: previewUrl + i.file_path,
            ...i
          };
        });
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  watch: {},
  methods: {
    beforeUpload() {},
    async afterRead(result, { index }) {
      result = Array.isArray(result) ? result : [result];
      for (let r of result) {
        // 照片大于500k压缩一下
        if (r.file.size > 512000 && r.file.type.startsWith('image/')) {
          await this.compressorImg(r.file, index);
        }
        await this.uploadFile(r.file, index);
      }
    },
    // 压缩图片
    async compressorImg(file, index) {
      let _this = this;
      return new Promise((res, rej) => {
        new Compressor(file, {
          quality: 0.4,
          success: async result => {
            await _this.uploadFile(result, index);
            res();
          },
          error: async err => {
            await _this.uploadFile(file, index);
            rej(err);
          }
        });
      });
    },
    async uploadFile(file, index) {
      let formData = new FormData();
      formData.append('module', this.module);
      formData.append('file', file);
      await uploadFile(formData).then(res => {
        if (res.status === 200 && res.data) {
          this.fileList[index] = {
            name: res.data.mc,
            url: previewUrl + res.data.file_path,
            ...res.data
          };

          this.$emit('input', this.fileList);
        }
      });
    },
    beforeDelete(file) {
      if (file.id) {
        return new Promise((resolve, reject) => {
          deleteFile({ id: file.id }).then(res => {
            if (res.status == 200) {
              resolve(true);
            }
            reject(false);
          });
        });
      }

      return true;
    }
  },
  created() {},
  mounted() {}
};
</script>

<style lang="scss" scoped></style>
