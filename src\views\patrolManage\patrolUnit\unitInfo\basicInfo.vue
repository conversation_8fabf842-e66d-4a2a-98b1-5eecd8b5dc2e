<template>
  <div class="basic-info">
    <h4 class="info-title">{{ data.orgName }}</h4>
    <p class="info-text">编号：{{ data.unitType }}</p>
    <van-cell-group inset>
      <van-cell title="负责人" :value="data.principal">
        <template #title>
          <div class="cell-title flex-vc">
            <img class="title-icon" src="@/assets/images/patrol/patrolUnit/director.png" alt="负责人" />
            <span>负责人</span>
          </div>
        </template>
      </van-cell>
      <van-cell title="联系电话" :value="data.contactWay">
        <template #title>
          <div class="cell-title flex-vc">
            <img class="title-icon" src="@/assets/images/patrol/patrolUnit/phone.png" alt="电话" />
            <span>联系电话</span>
          </div>
        </template>
      </van-cell>
      <van-cell title="人员数量" :value="data.count">
        <template #title>
          <div class="cell-title flex-vc">
            <img class="title-icon" src="@/assets/images/patrol/patrolUnit/person.png" alt="数量" />
            <span>人员数量</span>
          </div>
        </template>
      </van-cell>
      <van-cell title="地址" :label="data.address">
        <template #title>
          <div class="cell-title flex-vc">
            <img class="title-icon" src="@/assets/images/patrol/patrolProblem/position.png" alt="地址" />
            <span>地址</span>
          </div>
        </template>
      </van-cell>
    </van-cell-group>
  </div>
</template>

<script>
export default {
  name: 'BasicInfo',
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    userCount: {
      type: Number,
      default: 0
    }
  },
  components: {},
  data() {
    return {};
  },
  created() {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.basic-info {
  padding-top: 40px;
  .info-title {
    padding: 0 51px;
    margin-bottom: 16px;
    font-size: 36px;
    font-weight: 500;
    line-height: 44px;
    color: $color-text-white;

    @include ellipsisText;
  }
  .info-text {
    padding: 0 51px;
    margin-bottom: 40px;
    font-size: 28px;
    font-weight: 400;
    line-height: 36px;
    color: rgba($color-text-white, 0.8);
  }
  .cell-title {
    height: 48px;
    font-size: 32px;
    font-weight: 500;
    color: $color-text-black;
    .title-icon {
      width: 36px;
      height: 36px;
      margin-right: 20px;
    }
  }
  &::v-deep {
    .van-cell__value,
    .van-cell__label {
      font-size: 32px;
      line-height: 40px;
      color: rgba($color-text-black, 0.9);
    }
  }
}
</style>
