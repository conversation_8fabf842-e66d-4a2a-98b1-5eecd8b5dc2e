import { getAxisMax, getAxisMin } from '@/utils/arithmetic';
export default {
  data() {
    return {
      charts: new Map(), // 统一使用Map管理所有图表实例，以DOM元素为key
      LABEL_COLOR: '#00000099',
      LINE_COLOR: '#2e4c68',
      BG_COLOR: '#7dd4ff29'
    };
  },
  methods: {
    initChart(dom, options) {
      if (!dom || !dom.nodeType) {
        console.error('initChart: dom参数是必传的，请传入有效的DOM元素');
        return;
      }

      let chartInstance = this.charts.get(dom);

      if (!chartInstance) {
        chartInstance = this.$echarts.init(dom, null, { renderer: 'svg' });
        this.charts.set(dom, chartInstance);

        // 第一次创建图表时添加resize监听
        if (this.charts.size === 1) {
          window.addEventListener('resize', this.resizeChart);
        }
      } else {
        chartInstance.clear();
      }

      chartInstance.setOption(options);
      return chartInstance;
    },
    resizeChart() {
      // 调整所有图表大小
      this.charts.forEach(chart => {
        if (chart) {
          chart.resize();
        }
      });
    },
    // 双y轴刻度对齐处理
    maintainScale(rangeData) {
      if (Object.keys(rangeData).length == 2) {
        const max1 = getAxisMax({ max: rangeData[0].max, min: rangeData[0].min }) || 1;
        const min1 = getAxisMin({ max: rangeData[0].max, min: rangeData[0].min }) || 0;
        const max2 = getAxisMax({ max: rangeData[1].max, min: rangeData[1].min }) || 1;
        const min2 = getAxisMin({ max: rangeData[1].max, min: rangeData[1].min }) || 0;

        //不小于零直接不需要处理
        if (min1 >= 0 && min2 >= 0) {
          return { y1Max: max1, y2Max: max2, y1Min: Math.floor(min1), y2Min: Math.floor(min2) };
        }
        const ratio = (max1 - min1) / (max2 - min2);
        let minMax = {};
        if (max1 < max2 * ratio) {
          minMax.y1Max = max2 * ratio;
          minMax.y2Max = max2;
        } else {
          minMax.y1Max = max1;
          minMax.y2Max = max1 / ratio;
        }
        if (min1 < min2 * ratio) {
          minMax.y1Min = min1;
          minMax.y2Min = min1 / ratio;
        } else {
          minMax.y1Min = min2 * ratio;
          minMax.y2Min = min2;
        }
        minMax.y1Min = Number(minMax.y1Min.toFixed(2));
        minMax.y2Min = Number(minMax.y2Min.toFixed(2));
        minMax.y1Max = Number(minMax.y1Max.toFixed(2));
        minMax.y2Max = Number(minMax.y2Max.toFixed(2));
        return minMax;
      } else {
        return false;
      }
    }
  },
  beforeDestroy() {
    // 销毁所有图表实例
    this.charts.forEach(chart => {
      if (chart) {
        chart.dispose();
      }
    });
    this.charts.clear();

    window.removeEventListener('resize', this.resizeChart);
  },
  deactivated() {
    window.removeEventListener('resize', this.resizeChart);
  }
};
