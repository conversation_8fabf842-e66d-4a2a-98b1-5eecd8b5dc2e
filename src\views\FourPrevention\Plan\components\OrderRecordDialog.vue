<template>
  <base-dialog
    :showDialog="true"
    :append-to-body="true"
    width="60vw"
    title="调令发布记录"
    :has-padding="false"
    :show-footer="false"
    @close="dialogClose"
  >
    <div class="dialog-content">
      <el-tabs v-model="activeName" class="way-tabs c-black-el-tabs" @tab-change="tabChange">
        <el-tab-pane label="调令发送记录" name="1">
          <div class="flx search-box">
            <!-- <el-button type="primary" @click="toAdd">发布</el-button> -->
            <el-form :inline="true" :model="searchForm" class="c-blue-form search-form">
              <el-form-item label="发布时间">
                <el-date-picker
                  v-model="recordSearchTime"
                  type="datetimerange"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  class="c-black-el-date-picker"
                  :clearable="false"
                  :editable="false"
                />
              </el-form-item>
              <el-form-item label="调令名称">
                <el-input v-model="searchForm.title" clearable class="c-black-input" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchData">查询</el-button>
                <el-button class="c-black-normal-button" @click="resetData">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <el-table
            :data="tableDataRecord"
            style="width: 100%"
            class="c-black-el-table warning-table"
          >
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="title" label="调令名称" align="center"></el-table-column>
            <el-table-column prop="releaseUnit" label="发布单位" align="center"></el-table-column>
            <el-table-column prop="releaseTime" label="发布时间" align="center"></el-table-column>
            <el-table-column label="调令下达单位" align="center">
              <template #default="scope">
                <span>{{
                  scope.row.orgList ? scope.row.orgList.map(item => item.dictName).join('/') : ''
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-button type="primary" @click="toCheck(scope.row)">查看</el-button>
                <el-button type="danger" @click="toDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="flx" style="justify-content: flex-end">
            <el-pagination
              class="c-black-el-pagination"
              background
              :total="recordTotal"
              v-model:current-page="searchForm.pageNum"
              v-model:page-size="searchForm.pageSize"
              :page-sizes="[10, 15, 20, 30]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="searchData"
              @current-change="searchData"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="草稿箱" name="0">
          <div class="flx search-box">
            <!-- <el-button type="primary" @click="toAdd">发布</el-button> -->
            <el-form :inline="true" :model="draftSearchForm" class="c-blue-form search-form">
              <el-form-item label="发布时间">
                <el-date-picker
                  v-model="draftSearchTime"
                  type="datetimerange"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  class="c-black-el-date-picker"
                  :clearable="false"
                  :editable="false"
                />
              </el-form-item>
              <el-form-item label="调令名称">
                <el-input v-model="draftSearchForm.title" clearable class="c-black-input" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="searchData">查询</el-button>
                <el-button class="c-black-normal-button" @click="resetData">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <el-table
            :data="tableDataDraft"
            style="width: 100%"
            class="c-black-el-table warning-table"
          >
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="title" label="调令名称" align="center"></el-table-column>
            <el-table-column prop="releaseUnit" label="发布单位" align="center"></el-table-column>
            <el-table-column prop="releaseTime" label="发布时间" align="center"></el-table-column>
            <el-table-column label="调令下达单位" align="center">
              <template #default="scope">
                <span>{{
                  scope.row.orgList ? scope.row.orgList.map(item => item.dictName).join('/') : ''
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-button type="primary" @click="toCheck(scope.row)">查看</el-button>
                <el-button type="danger" @click="toDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="flx" style="justify-content: flex-end">
            <el-pagination
              class="c-black-el-pagination"
              background
              :total="draftTotal"
              v-model:current-page="draftSearchForm.pageNum"
              v-model:page-size="draftSearchForm.pageSize"
              :page-sizes="[10, 15, 20, 30]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="searchData"
              @current-change="searchData"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <base-dialog
      :showDialog="addShow"
      :append-to-body="true"
      width="800px"
      title="调令详情"
      :has-padding="false"
      :show-footer="false"
      @close="closeAddDialog"
    >
      <el-form ref="formRef" :model="formData" label-width="auto" class="c-blue-form">
        <el-form-item label="调令名称">
          <el-input v-model="formData.title" class="c-black-input" readonly />
        </el-form-item>
        <el-form-item label="发布单位">
          <el-input v-model="formData.releaseUnit" class="c-black-input" readonly />
        </el-form-item>
        <el-form-item label="发布时间">
          <el-input v-model="formData.releaseTime" class="c-black-input" readonly />
        </el-form-item>
        <el-form-item label="调令下达单位">
          <el-input v-model="formData.commandingUnits" class="c-black-input" readonly />
        </el-form-item>

        <el-form-item label="调令内容">
          <!-- <el-input v-model="formData.content" type="textarea" class="c-black-input" readonly /> -->
          <div class="editor-box">
            <wang-editor
              :custom-prop="formData.content"
              :disable="true"
              :trans-bg="true"
            ></wang-editor>
          </div>
        </el-form-item>
      </el-form>
    </base-dialog>
  </base-dialog>
</template>

<script setup lang="ts">
import { QueryReleaseDispatchVO } from '@/api/interface/plan/VO'
import { QueryReleaseDispatchDTO } from '@/api/interface/plan/DTO'
import { queryReleaseDispatchApi, delReleaseDispatchApi } from '@/api/module/fourPrevention/plan'
import $dayjs from 'dayjs'

const activeName = ref('1')

const formRef = ref()

const recordSearchTime = ref<Array<any>>([$dayjs().subtract(1, 'month'), $dayjs()])
const draftSearchTime = ref<Array<any>>([$dayjs().subtract(1, 'month'), $dayjs()])

const searchForm = ref<QueryReleaseDispatchDTO>({
  status: 1,
  title: '',
  beginTm: '',
  endTm: '',
  pageNum: 1,
  pageSize: 10
})

const draftSearchForm = ref<QueryReleaseDispatchDTO>({
  status: 0,
  title: '',
  beginTm: '',
  endTm: '',
  pageNum: 1,
  pageSize: 10
})

const recordTotal = ref(0)
const draftTotal = ref(0)

const searchData = async () => {
  if (activeName.value === '1') {
    searchForm.value.beginTm = $dayjs(recordSearchTime.value[0]).format('YYYY-MM-DD HH:mm:ss')
    searchForm.value.endTm = $dayjs(recordSearchTime.value[1]).format('YYYY-MM-DD HH:mm:ss')
  } else {
    draftSearchForm.value.beginTm = $dayjs(draftSearchTime.value[0]).format('YYYY-MM-DD HH:mm:ss')
    draftSearchForm.value.endTm = $dayjs(draftSearchTime.value[1]).format('YYYY-MM-DD HH:mm:ss')
  }
  const res = await queryReleaseDispatchApi(
    activeName.value === '1' ? searchForm.value : draftSearchForm.value
  )
  if (res.data && res.data.list) {
    if (activeName.value === '1') {
      tableDataRecord.value = res.data.list
      recordTotal.value = res.data.total
    } else {
      tableDataDraft.value = res.data.list
      draftTotal.value = res.data.total
    }
  }
}

const tabChange = () => {
  if (activeName.value === '1' && !tableDataRecord.value.length) {
    searchData()
  } else if (activeName.value === '0' && !tableDataDraft.value.length) {
    searchData()
  }
}

const resetData = () => {
  if (activeName.value === '1') {
    searchForm.value.pageNum = 1
    searchForm.value.title = ''
    recordSearchTime.value = [$dayjs().subtract(1, 'month'), $dayjs()]
  } else {
    draftSearchForm.value.pageNum = 1
    draftSearchForm.value.title = ''
    draftSearchTime.value = [$dayjs().subtract(1, 'month'), $dayjs()]
  }

  searchData()
}

const tableDataRecord = ref<Array<QueryReleaseDispatchVO>>([])

const tableDataDraft = ref<Array<QueryReleaseDispatchVO>>([])

const addShow = ref(false)

const toCheck = row => {
  formData.value = row
  if (row.orgList) {
    formData.value.commandingUnits = row.orgList.map(item => item.dictName).join('，')
  }
  addShow.value = true
}

const closeAddDialog = () => {
  addShow.value = false
  formData.value = {}
}

const formData = ref<any>({})

const toDelete = row => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const res = await delReleaseDispatchApi({ id: row.id })
      if (res.status === 200) {
        ElMessage.success('删除成功')
        resetData()
      }
    })
    .catch(() => {})
}

const emits = defineEmits(['dialogClose'])

const dialogClose = () => {
  emits('dialogClose')
}

onMounted(() => {
  searchData()
})
</script>

<style lang="scss" scoped>
.dialog-content {
  width: 100%;
  padding: 12px;
  background-color: #00152980;

  .way-tabs {
    :deep(.el-tabs__header) {
      padding-left: 10px;

      // .el-tabs__active-bar {
      //   background-color: #7ff;
      // }

      // .is-active {
      //   color: #7ff;
      // }

      .el-tabs__item,
      .is-active {
        font-family: YouSheBiaoTiHei, sans-serif;
        font-size: 26px;
      }
    }
  }

  .search-box {
    margin-bottom: 10px;

    .search-form {
      margin-top: 10px;

      .el-form-item {
        padding-right: 10px;
        margin: 0 !important;
      }
    }
  }

  .warning-table {
    margin-bottom: 10px;
  }
}

.editor-box {
  width: 100%;
  height: 200px;
  padding: 8px;
  border: 1px solid #0b4eb3;
  border-radius: 4px;
}
</style>
