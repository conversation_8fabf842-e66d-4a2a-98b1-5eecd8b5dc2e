import apiUrl from '@/utils/apiUrl';
import appAjax from '@/utils/apiRequestType';

const defaultUrl = apiUrl.defaultUrl;

export const downloadUrl = apiUrl.defaultUrl + '/common/attachment/downLoad';
export const uploadUrl = apiUrl.defaultUrl + '/common/attachment/upload';
export const previewUrl = defaultUrl + '/common/attachment/show?path=';

/**
 * @description: 上传文件
 * @param {FormData} data
 */
export const uploadFile = data => appAjax.postUpLoadFile(uploadUrl, data);

/**
 * @description: 删除文件
 * @param {number[]} params
 */
export const deleteFile = params => appAjax.get(`${defaultUrl}/common/attachment/del`, params);

/**
 * @description: 下载文件
 * @param {number[]} file
 */
export const downLoadFile = (params, type) => {
  return appAjax.getDownLoad(downloadUrl, params, undefined, type);
};

/**
 * @description: 数据字典
 * @return {object}
 */
export const getAllOptionMap = data => appAjax.get(`${defaultUrl}/common/option/getAllOptionMap`, data);
