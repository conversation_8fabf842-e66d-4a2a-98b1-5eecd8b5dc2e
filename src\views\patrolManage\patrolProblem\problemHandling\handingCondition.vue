<template>
  <div class="handing-card">
    <div class="flex-vc j-sb">
      <span>检查人员：{{ data.xjUserName }}</span>
      <span class="text-vice">{{ data.date }} 开始巡查</span>
    </div>
    <div class="card-item van-hairline--bottom">
      <div class="item-title flex-vc">
        <img class="title-icon" src="@/assets/images/patrol/patrolProblem/problem.png" alt="描述" />
        <span>问题描述</span>
      </div>
      <div class="item-descript">{{ data.stnPmRefer }}</div>
    </div>
    <div class="card-item van-hairline--bottom">
      <div class="item-title flex-vc">
        <img class="title-icon" src="@/assets/images/patrol/patrolProblem/position.png" alt="定位" />
        <span>问题定位</span>
      </div>
      <div class="flex-vc j-sb">
        <span>{{ data.stnAddress }}</span>
        <span class="text-primary" @click="toPositionPage(data)">定位</span>
      </div>
    </div>
    <div class="card-item">
      <div class="item-title flex-vc">
        <img class="title-icon" src="@/assets/images/patrol/patrolProblem/problem.png" alt="照片" />
        <span>问题照片</span>
      </div>
      <CustomUpload v-model="data.wtAttachList" :module-id="9" module="xjxc" :deletable="false" :show-upload="false" disabled />
    </div>
  </div>
</template>

<script>
// import { ImagePreview } from 'vant';
import CustomUpload from '@/components/CustomUpload/index.vue';
export default {
  name: 'HandingCondition',
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    CustomUpload
  },
  data() {
    return {};
  },
  methods: {
    toPositionPage(row) {
      if (row.lng && row.lat) {
        this.$router.push({
          name: 'ProblemMap',
          query: { lng: row.lng, lat: row.lat }
        });
      } else {
        this.$userApp.toast.show({
          text: `当前问题没有位置信息`,
          type: 'text'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
