import { Dialog } from 'vant';

let appVersion = null;
document.addEventListener('plusready', function() {
  appVersion = window.plus.runtime.getProperty(window.plus.runtime.appid, function(appInfo) {
    appVersion = appInfo.version;
  });
});

function isBrowser(type) {
  return new RegExp(type, 'i').test(navigator.userAgent);
}

// 设置应用是否保持唤醒（屏幕常亮）状态
function setWakelock(lock = false) {
  window.plus.device.setWakelock(lock);
}

// 安卓将activity推到后台
function an_moveTaskToBack(nonRoot = true) {
  const Main = window.plus.android.runtimeMainActivity();
  Main.moveTaskToBack(nonRoot);
}

// 唤醒锁，不让手机进入休眠状态，加大耗电量，结束后必须释放唤醒锁
function an_setWakelock(tagName = 'LOCATION_LOCK') {
  const Main = window.plus.android.runtimeMainActivity();
  const Context = window.plus.android.importClass('android.content.Context');
  const PowerManager = window.plus.android.importClass('android.os.PowerManager');

  const pm = Main.getSystemService(Context.POWER_SERVICE);
  const an_wakelock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, tagName);
  an_wakelock.acquire();

  return an_wakelock;
}

// 释放唤醒锁
function an_releaseWakelock(an_wakelock) {
  if (an_wakelock && an_wakelock.isHeld()) {
    an_wakelock.release();
    an_wakelock = null;
  }
}

// 安卓检查后台定位权限并申请权限
function an_checkBackgroundPermission() {
  const Build = window.plus.android.importClass('android.os.Build');
  if (Build.VERSION.SDK_INT >= 28) {
    const Main = window.plus.android.runtimeMainActivity();
    if (Main.checkSelfPermission('android.permission.FOREGROUND_SERVICE') === -1) {
      // 将服务提升为前台服务
      window.plus.android.requestPermissions(['android.permission.FOREGROUND_SERVICE']);
    }

    if (Build.VERSION.SDK_INT >= 29) {
      if (Main.checkSelfPermission('android.permission.ACCESS_BACKGROUND_LOCATION') === -1) {
        // 后台定位权限
        window.plus.android.requestPermissions(['android.permission.ACCESS_BACKGROUND_LOCATION']);
      }
    }
  }
}

// 安卓检查是否开启GPS
function an_checkGPS() {
  const Main = window.plus.android.runtimeMainActivity();
  const Context = window.plus.android.importClass('android.content.Context');
  const LocationManager = window.plus.android.importClass('android.location.LocationManager');

  const mainSvr = Main.getSystemService(Context.LOCATION_SERVICE);
  return mainSvr.isProviderEnabled(LocationManager.GPS_PROVIDER);
}

// 安卓检查是否能获取到坐标
function an_getLastKnownLocation() {
  const Main = window.plus.android.runtimeMainActivity();
  const Context = window.plus.android.importClass('android.content.Context');
  const LocationManager = window.plus.android.importClass('android.location.LocationManager');

  const mainSvr = Main.getSystemService(Context.LOCATION_SERVICE);
  return mainSvr.getLastKnownLocation(LocationManager.GPS_PROVIDER);
}

// 安卓打开系统设置GPS服务页面
function an_toLocationSettings() {
  const Main = window.plus.android.runtimeMainActivity();
  const Intent = window.plus.android.importClass('android.content.Intent');
  const Settings = window.plus.android.importClass('android.provider.Settings');

  const intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
  Main.startActivity(intent);
}

// 安卓检查有没有通知权限
function an_checkNotification() {
  const Main = window.plus.android.runtimeMainActivity();
  const NotificationManagerCompat = window.plus.android.importClass('androidx.core.app.NotificationManagerCompat');

  const packageNames = NotificationManagerCompat.from(Main);
  if (packageNames) {
    return packageNames.areNotificationsEnabled();
  }
  return true;
}

// 安卓打开系统设置通知服务页面
function an_toNotificationSettings() {
  const Main = window.plus.android.runtimeMainActivity();
  const Build = window.plus.android.importClass('android.os.Build');
  const Intent = window.plus.android.importClass('android.content.Intent');

  const pkName = Main.getPackageName();
  const uid = Main.getApplicationInfo().plusGetAttribute('uid');
  let intent = '';
  //android 8.0引导
  if (Build.VERSION.SDK_INT >= 26) {
    intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
    intent.putExtra('android.provider.extra.APP_PACKAGE', pkName);
  } else if (Build.VERSION.SDK_INT >= 21) {
    //android 5.0-7.0
    intent = new Intent('android.settings.APP_NOTIFICATION_SETTINGS');
    intent.putExtra('app_package', pkName);
    intent.putExtra('app_uid', uid);
  } else {
    //(<21)其他--跳转到该应用管理的详情页
    const Settings = window.plus.android.importClass('android.provider.Settings');
    const Uri = window.plus.android.importClass('android.net.Uri');

    const uri = Uri.fromParts('package', Main.getPackageName(), null);
    intent = new Intent();
    intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
    intent.setData(uri);
  }
  // 跳转到该应用的系统通知设置页
  Main.startActivity(intent);
}

// 安卓检查电源白名单
function an_checkPower() {
  const Build = window.plus.android.importClass('android.os.Build');
  if (Build.VERSION.SDK_INT < 24) {
    return true;
  }
  // 电源白名单
  const Main = window.plus.android.runtimeMainActivity();
  const Context = window.plus.android.importClass('android.content.Context');
  const PowerManager = window.plus.android.importClass('android.os.PowerManager');
  const packName = Main.getPackageName();
  const pm = Main.getSystemService(Context.POWER_SERVICE);
  if (pm) {
    return pm.isIgnoringBatteryOptimizations(packName);
  }
  return true;
}

// 安卓跳转电源优化名单列表
function an_toPowerSettings() {
  const Main = window.plus.android.runtimeMainActivity();
  const Settings = window.plus.android.importClass('android.provider.Settings');
  const Intent = window.plus.android.importClass('android.content.Intent');
  // 跳转电源优化名单列表，选对应应用设置
  const intents = new Intent(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS);
  if (intents.resolveActivity(Main.getPackageManager()) != null) {
    Main.startActivity(intents);
  }
}

// ios设置后台定位权限
function ios_setBackgroundPermission() {
  const CLLocationManager = window.plus.ios.newObject('CLLocationManager');
  const UIDevice = window.plus.ios.import('UIDevice');

  const device = UIDevice.currentDevice();
  const version = device.systemVersion();
  const firstVersion = Number(version.split('.')[0]);
  if (firstVersion >= 8) {
    // 申请定位权限
    CLLocationManager.requestAlwaysAuthorization();
  }
  if (firstVersion >= 9) {
    // 允许后台定位
    // window.plus.ios.invoke(CLLocationManager,'allowsBackgroundLocationUpdates:','YES')
    CLLocationManager.setAllowsBackgroundLocationUpdates('YES');
  }
  // 不允许系统暂停定位
  // window.plus.ios.invoke(CLLocationManager,'pausesLocationUpdatesAutomatically:','NO')
  CLLocationManager.setPausesLocationUpdatesAutomatically('NO');

  window.plus.ios.deleteObject(CLLocationManager);
  window.plus.ios.deleteObject(UIDevice);
}

// ios 检查是否开启GPS
function ios_checkGPS() {
  const CLLocationManager = window.plus.ios.import('CLLocationManager');

  const enable = CLLocationManager.locationServicesEnabled();
  const status = CLLocationManager.authorizationStatus();

  window.plus.ios.deleteObject(CLLocationManager);
  return enable && status != 2;
}

// ios 打开系统设置GPS服务页面
function ios_toLocationSettings() {
  const UIApplication = window.plus.ios.import('UIApplication');
  const NSURL = window.plus.ios.import('NSURL');

  const application = UIApplication.sharedApplication();
  const setting = NSURL.URLWithString('App-Prefs:root=Privacy&path=LOCATION');
  application.openURL(setting);

  window.plus.ios.deleteObject(setting);
  window.plus.ios.deleteObject(NSURL);
  window.plus.ios.deleteObject(application);
  window.plus.ios.deleteObject(UIApplication);
}

// ios 检查有没有通知权限
function ios_checkNotification() {
  const UIApplication = window.plus.ios.import('UIApplication');

  const application = UIApplication.sharedApplication();
  let enabledTypes = 0;
  if (application.currentUserNotificationSettings) {
    const settings = application.currentUserNotificationSettings();
    enabledTypes = settings.plusGetAttribute('types');
  } else {
    //针对低版本ios系统
    enabledTypes = application.enabledRemoteNotificationTypes();
  }

  window.plus.ios.deleteObject(application);
  window.plus.ios.deleteObject(UIApplication);
  return enabledTypes !== 0;
}

// ios 打开系统设置通知服务页面
function ios_toNotificationSettings() {
  const UIApplication = window.plus.ios.import('UIApplication');
  const NSURL = window.plus.ios.import('NSURL');

  const setting = NSURL.URLWithString('app-settings:');
  const application = UIApplication.sharedApplication();
  application.openURL(setting);

  window.plus.ios.deleteObject(setting);
  window.plus.ios.deleteObject(NSURL);
  window.plus.ios.deleteObject(application);
  window.plus.ios.deleteObject(UIApplication);
}

function callPhone(phone, confirm = true) {
  window.plus.device.dial(phone, confirm);

  // 安卓拨打电话
  // if (isBrowser('Android')) {
  //   const Intent = window.plus.android.importClass('android.content.Intent');
  //   const Uri = window.plus.android.importClass('android.net.Uri');
  //   // 获取主Activity对象的实例
  //   const Main = window.plus.android.runtimeMainActivity();
  //   // 创建Intent
  //   const uri = Uri.parse(`tel:${phone}`); // 这里可修改电话号码
  //   const call = new Intent('android.intent.action.CALL', uri);
  //   // 调用startActivity方法拨打电话
  //   Main.startActivity(call);
  // }
}

function checkBackgroundPermission() {
  return new Promise(resolve => {
    if (window.plus) {
      try {
        if (isBrowser('Android')) {
          an_checkBackgroundPermission();
        } else {
          ios_setBackgroundPermission();
        }

        setTimeout(() => resolve(true));
      } catch {
        resolve(false);
      }
    } else {
      resolve(true);
    }
  });
}

function checkGPS() {
  return new Promise(resolve => {
    if (window.plus) {
      const isOpenGps = isBrowser('Android') ? an_checkGPS() : ios_checkGPS();
      if (!isOpenGps) {
        Dialog.alert({
          title: '提示',
          message: '请开启位置服务，否则将无法记录巡查轨迹信息！'
        }).then(() => {
          try {
            if (isBrowser('Android')) {
              an_toLocationSettings();
            } else {
              ios_toLocationSettings();
            }
            setTimeout(() => {
              resolve(true);
            }, 1000);
          } catch {
            resolve(false);
          }
        });
      } else {
        resolve(true);
      }
    } else {
      // if (process.env.NODE_ENV !== 'development') {
      window.navigator.geolocation.getCurrentPosition(
        () => resolve(true),
        () => resolve(false)
      );
      // } else {
      //   resolve(true);
      // }
    }
  });
}

function checkNotification() {
  return new Promise(resolve => {
    if (window.plus) {
      const isOpenGps = isBrowser('Android') ? an_checkNotification() : ios_checkNotification();
      if (!isOpenGps) {
        Dialog.confirm({
          title: '提示',
          message: '请允许应用发送通知！'
        }).then(() => {
          try {
            if (isBrowser('Android')) {
              an_toNotificationSettings();
            } else {
              ios_toNotificationSettings();
            }
            setTimeout(() => {
              resolve(true);
            }, 1000);
          } catch {
            resolve(false);
          }
        });
      } else {
        resolve(true);
      }
    } else {
      resolve(true);
    }
  });
}

function checkPower() {
  return new Promise(resolve => {
    if (window.plus) {
      if (isBrowser('Android') && !an_checkPower()) {
        // todo 后面补充跳转手机设置教程
        Dialog.confirm({
          title: '提示',
          message: '为了更准确记录巡查轨迹，请允许应用始终在后台运行！'
        }).then(() => {
          try {
            an_toPowerSettings();

            setTimeout(() => {
              resolve(true);
            }, 1000);
          } catch {
            resolve(false);
          }
        });
      } else {
        resolve(true);
      }
    } else {
      resolve(true);
    }
  });
}

function getPhoneInfo() {
  if (window.plus) {
    const system = isBrowser('Android') ? 'a' : 'i';
    const osVersion = window.plus.os.version; // 手机系统版本
    const model = window.plus.device.model; //设备型号
    const vendor = window.plus.device.vendor; //设备的生产厂商
    const version = appVersion || window.plus.runtime.version; //应用版本
    let permission = [];
    if (isBrowser('Android')) {
      const Build = window.plus.android.importClass('android.os.Build');
      const Main = window.plus.android.runtimeMainActivity();
      const packName = Main.getPackageName();
      if (Build.VERSION.SDK_INT >= 24) {
        const Context = window.plus.android.importClass('android.content.Context');
        const pm = Main.getSystemService(Context.POWER_SERVICE);
        const battery = pm.isIgnoringBatteryOptimizations(packName);
        permission.push(`bat:${+battery}`); //电池白名单

        if (Build.VERSION.SDK_INT >= 29) {
          const background = Main.checkSelfPermission('android.permission.ACCESS_BACKGROUND_LOCATION') !== -1;
          permission.push(`back:${+background}`); // 后台定位
        }
      }
    }

    return `${system}-${osVersion}-${model}-${vendor}-${version}-${permission.join('-')}`;
  }

  return null;
}

// 判断第三方程序是否已经存在
function checkApplication(packageName) {
  if (!window.plus) return;
  if (plus.runtime.isApplicationExist({ pname: packageName })) {
    return true;
  } else {
    return false;
  }
}

export {
  isBrowser,
  setWakelock,
  an_moveTaskToBack,
  an_setWakelock,
  an_releaseWakelock,
  an_checkBackgroundPermission,
  an_checkGPS,
  an_getLastKnownLocation,
  an_toLocationSettings,
  an_checkNotification,
  an_toNotificationSettings,
  an_checkPower,
  an_toPowerSettings,
  ios_setBackgroundPermission,
  ios_checkGPS,
  ios_toLocationSettings,
  ios_checkNotification,
  ios_toNotificationSettings,
  callPhone,
  checkBackgroundPermission,
  checkGPS,
  checkNotification,
  checkPower,
  getPhoneInfo,
  checkApplication
};
