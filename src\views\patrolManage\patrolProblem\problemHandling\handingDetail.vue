<template>
  <div class="handing-card">
    <div class="card-item van-hairline--bottom">
      <div class="item-title flex-vc">
        整改资料
      </div>
      <CustomUpload v-model="data.clAttachList" :module-id="9" module="xjxc" :deletable="false" :show-upload="false" disabled />
    </div>
    <div class="card-item">
      <div class="item-title flex-vc">
        整改说明
      </div>
      <div class="item-descript">{{ data.stnHdRefer }}</div>
    </div>
  </div>
</template>

<script>
// import { ImagePreview } from 'vant';
import CustomUpload from '@/components/CustomUpload/index.vue';
export default {
  name: 'HandingDetail',
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    CustomUpload
  },
  data() {
    return {};
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
