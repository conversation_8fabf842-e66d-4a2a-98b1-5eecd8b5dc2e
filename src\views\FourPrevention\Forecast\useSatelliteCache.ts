import { ConcurrencyController } from '@/utils/request'

//planId : imgList
const satelliteCacheMap = ref<
  Record<
    string,
    {
      preloadedSatelliteImages: string[]
      preloadedRadarImages: string[]
    }
  >
>({})

// url : blob url
const urlCacheMap = ref<Record<string, string>>({})

// 创建并发控制器实例，限制同时下载4个图片
const concurrencyController = new ConcurrencyController(4)

// 撤销blob url以防止内存泄漏
const revokeImageUrls = (urls: string[]) => {
  for (const url of urls) {
    if (url.startsWith('blob:')) {
      URL.revokeObjectURL(url)
      Object.entries(urlCacheMap.value).forEach(([key, value]) => {
        if (value === url) {
          delete urlCacheMap.value[key]
        }
      })
    }
  }
}

/**
 * 删除所有缓存
 * @param excludePlanId 保留的planId，一般保留最新可用预报的缓存
 */
const removeAllCache = (excludePlanId?: string | number) => {
  if (!excludePlanId) {
    for (let pid in satelliteCacheMap.value) {
      const cache = satelliteCacheMap.value[pid]
      revokeImageUrls(cache.preloadedSatelliteImages)
      revokeImageUrls(cache.preloadedRadarImages)
    }
    satelliteCacheMap.value = {}
    urlCacheMap.value = {}
  } else {
    for (let pid in satelliteCacheMap.value) {
      if (pid !== excludePlanId + '') {
        const cache = satelliteCacheMap.value[pid]
        revokeImageUrls(cache.preloadedSatelliteImages)
        revokeImageUrls(cache.preloadedRadarImages)
        delete satelliteCacheMap.value[pid]
      }
    }
  }
}

const initCache = (planId?: string | number) => {
  if (!planId) {
    return {
      preloadedSatelliteImages: [],
      preloadedRadarImages: []
    }
  }
  if (satelliteCacheMap.value[planId]) {
    return satelliteCacheMap.value[planId]
  }
  satelliteCacheMap.value[planId] = {
    preloadedSatelliteImages: [],
    preloadedRadarImages: []
  }
  return satelliteCacheMap.value[planId]
}

const processUrl = async (
  url: string,
  pid: number,
  target: keyof (typeof satelliteCacheMap.value)[string]
) => {
  // 使用并发控制器来限制同时下载的图片数量
  return concurrencyController.execute(async () => {
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${url}, status: ${response.status}`)
      }
      const blob = await response.blob()
      const blobUrl = URL.createObjectURL(blob)
      const targetArray = satelliteCacheMap.value[pid][target]
      targetArray.push(blobUrl)
      urlCacheMap.value[url] = blobUrl
    } catch (error) {
      console.warn(`Failed to download or process image: ${url}`, error)
      // 重新抛出错误，让调用方能够处理
      throw error
    }
  })
}

export const useSatelliteCache = () => {
  return {
    satelliteCacheMap,
    revokeImageUrls,
    removeAllCache,
    initCache,
    urlCacheMap,
    processUrl
  }
}
