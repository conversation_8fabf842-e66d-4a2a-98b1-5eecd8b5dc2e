# Project Structure

## Root Directory
```
├── .env*                    # Environment configuration files
├── .kiro/                   # Kiro IDE configuration
├── build/                   # Build utilities and helpers
├── dist/                    # Production build output
├── public/                  # Static assets (3D models, images)
├── src/                     # Source code
├── node_modules/            # Dependencies
├── package.json             # Project configuration
├── vite.config.ts          # Vite configuration
├── tsconfig.json           # TypeScript configuration
├── eslint.config.js        # ESLint configuration
└── prettier.config.mjs     # Prettier configuration
```

## Source Directory Structure (`src/`)

### Core Application Files
- `main.ts` - Application entry point with global configurations
- `App.vue` - Root Vue component with Element Plus provider
- `vite-env.d.ts` - Vite type definitions
- `auto-imports.d.ts` - Auto-generated import types
- `components.d.ts` - Auto-generated component types

### Feature Directories

#### `api/` - API Layer
- `config/` - API configuration and interceptors
- `interface/` - TypeScript interfaces for API responses
- `module/` - API endpoint modules organized by feature
- `index.ts` - API exports and initialization

#### `alova_api/` - Alova HTTP Client
- `methods/` - API method definitions
- `docs/` - API documentation
- Generated API client with type safety

#### `components/` - Reusable Components
- `Base*` - Generic base components (Button, Dialog, Title, etc.)
- `Map3d/` - 3D map visualization component
- `FilePreview/` - Document preview components
- `WangEditor/` - Rich text editor integration
- Each component in its own directory with index files

#### `views/` - Page Components
- `Login/` - Authentication pages
- `ComprehensiveSituation/` - Dashboard and overview
- `Four*/` - Main feature modules (Full, Institutional, Management, Prevention)
- `ErrorPages/` - Error handling pages
- `testMap/` - Development/testing components

#### `stores/` - State Management
- `modules/` - Pinia store modules by feature
- `interface/` - Store type definitions
- `index.ts` - Store configuration
- `piniaPersist.ts` - Persistence configuration

#### `routers/` - Routing
- `modules/` - Route definitions by feature
- `index.ts` - Router configuration with guards

#### `utils/` - Utilities
- `constant/` - Application constants
- `is/` - Type checking utilities
- `map/` - Map-related utilities
- Individual utility files (dayjs, echarts, tree, etc.)

#### `hooks/` - Composables
- Custom Vue composition functions
- Reusable logic for maps, downloads, charts

#### `styles/` - Global Styles
- `element/` - Element Plus theme customization
- `reset.scss` - CSS reset
- `common.scss` - Common styles
- `var.scss` - SCSS variables

#### `types/` - Type Definitions
- `global.d.ts` - Global TypeScript definitions

#### `config/` - Application Configuration
- Service loading, progress bar, and other configs

#### `directives/` - Vue Directives
- Custom directives (auth, debounce, throttle)

#### `enums/` - Enumerations
- Application-wide enums and constants

#### `layouts/` - Layout Components
- Page layout components and constants

## Naming Conventions

### Files & Directories
- **PascalCase** for Vue components (`BaseButton.vue`)
- **camelCase** for utilities and composables (`useEcharts.ts`)
- **kebab-case** for views and feature directories
- **lowercase** for configuration files

### Vue Components
- **Base** prefix for generic reusable components
- **Feature-specific** names for domain components
- Single File Components (`.vue`) with `<script setup>`

### API & Types
- **Interface** suffix for TypeScript interfaces
- **Type** suffix for type aliases
- **Enum** suffix for enumerations

## Import Patterns

### Path Aliases
- `@/` - Points to `src/` directory
- Use absolute imports with `@/` prefix

### Auto-imports
- Vue APIs (ref, reactive, computed, etc.) auto-imported
- Element Plus components auto-imported
- Pinia stores auto-imported
- Router composables auto-imported

### Component Registration
- Global components auto-registered from `src/components/`
- Layout components from `src/layouts/components/`
- Element Plus icons globally registered

## Environment Structure
- `.env` - Base configuration
- `.env.development` - Development overrides
- `.env.production` - Production overrides  
- `.env.pretreatment` - Staging/pretreatment overrides
- `.env.hlq.*` - HLQ environment variants

## Build Output Structure
```
dist/
├── assets/
│   ├── js/           # JavaScript bundles
│   ├── css/          # Stylesheets
│   └── images/       # Optimized images
├── index.html        # Entry HTML
└── ...               # Other static assets
```