<template>
  <van-dialog
    v-model="showDialog"
    title="物资信息"
    width="95%"
    show-cancel-button
    :lazy-render="false"
    :before-close="beforeClose"
  >
    <div class="material-info">
      <div class="flex-1 material-list">
        <div v-for="(item, index) in materialList" :key="index" class="material-list">
          <div class="material-item-tool" @click="handleDelete(index)"><van-icon name="delete-o" />删除</div>
          <div class="material-item">
            <van-form :ref="'materialRef' + index">
              <van-field
                readonly
                is-link
                v-model="item.categoryName"
                label="物资类别"
                name="categoryName"
                clearable
                placeholder="请选择物资类别"
                :rules="[{ required: true }]"
                @click="chooseCategory(index)"
              />

              <van-field
                readonly
                is-link
                v-model.trim="item.goodsName"
                label="物资名称"
                name="goodsName"
                clearable
                placeholder="请选择物资名称"
                :rules="[{ required: true }]"
                @click="chooseGoodsName(index)"
              >
              </van-field>
              <van-field
                v-model.trim="item.num"
                label="数量"
                name="num"
                type="number"
                clearable
                placeholder="请输入数量"
                :rules="[{ required: true }]"
              >
                <template #right-icon>{{ item.unit || '' }}</template>
              </van-field>
            </van-form>
          </div>
        </div>

        <van-button class="btn" type="info" :block="true" icon="plus" @click="handleAdd">新增</van-button>

        <van-action-sheet v-model="nameShow" :actions="materialFilterOptions" @select="onSelectGoodsName" />

        <van-action-sheet v-model="categoryShow" :actions="categoryOptions" @select="onSelectCategory" />
      </div>
    </div>
  </van-dialog>
</template>
<script>
export default {
  name: 'MaterialInfo',
  props: {},
  data() {
    return {
      showDialog: false,
      materialList: [],
      activeMaterial: null,
      nameShow: false,
      categoryShow: false,
      categoryOptions: [
        // { text: '发电机设备及电缆', value: 0 },
        { name: '救生器材', value: 1 },
        // { text: '救生衣具', value: 2 },
        // { text: '抢险设备', value: 3 },
        { name: '抢险物料', value: 4 },
        // { text: '供排水设备', value: 5 },
        // { text: '照明灯具', value: 6 },
        // { text: '易耗品', value: 7 },
        { name: '小型抢险机具', value: 8 },
        { name: '其他', value: 9 }
      ],
      materialOptions: [
        {
          category: 4,
          field: 'dl',
          name: '袋类',
          unit: '条'
        },
        {
          category: 4,
          field: 'tgb',
          name: '土工布',
          unit: 'm²'
        },
        {
          category: 4,
          field: 'ssl',
          name: '砂石料',
          unit: 'm³'
        },
        {
          category: 4,
          field: 'ks',
          name: '块石',
          unit: 'm³'
        },
        {
          category: 4,
          field: 'qs',
          name: '铅丝',
          unit: 'kg'
        },
        {
          category: 4,
          field: 'zm',
          name: '桩木',
          unit: '根'
        },
        {
          category: 4,
          field: 'gg',
          name: '钢管(材)',
          unit: 'kg'
        },
        {
          category: 1,
          field: 'jsy',
          name: '救生衣',
          unit: '件'
        },
        {
          category: 1,
          field: 'qxjsz',
          name: '抢险救生舟',
          unit: '艘'
        },
        {
          category: 8,
          field: 'bxsgzd',
          name: '便携式工作灯',
          unit: '只'
        },
        {
          category: 8,
          field: 'tgd',
          name: '投光灯',
          unit: '只'
        },
        {
          category: 8,
          field: 'dzj',
          name: '打桩机',
          unit: '台'
        },
        {
          category: 8,
          field: 'dl2',
          name: '电缆',
          unit: 'm'
        }
      ]
    };
  },
  computed: {
    materialFilterOptions() {
      return this.materialOptions.filter(
        item =>
          !this.materialList[this.activeMaterial] ||
          !this.materialList[this.activeMaterial].category ||
          item.category === this.materialList[this.activeMaterial].category
      );
    }
  },
  methods: {
    show(material) {
      // 物资字符串处理
      this.materialList = (material.split('、') || [])
        .filter(item => item)
        .map(item => {
          const arr = item.split(/(\d+)/);
          const category = this.materialOptions.find(option => option.name === arr[0]).category;
          return {
            goodsName: arr[0],
            num: arr[1] || '',
            unit: arr[2] || '',
            category,
            categoryName: this.categoryOptions.find(option => option.value === category).name
          };
        });
      this.showDialog = true;
    },
    // 选择物资名称
    chooseGoodsName(i) {
      this.nameShow = true;
      this.activeMaterial = i;
    },
    onSelectGoodsName(item) {
      this.materialList[this.activeMaterial].goodsName = item.name;
      this.materialList[this.activeMaterial].category = item.category;
      this.materialList[this.activeMaterial].unit = item.unit;
      this.nameShow = false;
    },
    // 选择物资类别
    chooseCategory(i) {
      this.categoryShow = true;
      this.activeMaterial = i;
    },
    onSelectCategory(item) {
      this.materialList[this.activeMaterial].category = item.value;
      this.materialList[this.activeMaterial].categoryName = item.name;
      this.categoryShow = false;
    },
    // 新增物资
    handleAdd() {
      this.materialList.push({
        category: '',
        goodsName: '',
        num: '',
        unit: '',
        categoryName: ''
      });
    },

    async beforeClose(action, done) {
      if (action === 'cancel') {
        done();
        return;
      }
      try {
        for (let i = 0; i < this.materialList.length; i++) {
          await this.$refs[`materialRef${i}`][0].validate();
        }

        let material = '';
        this.materialList &&
          this.materialList.forEach(item => {
            material += `${item.goodsName}${item.num}${item.unit || ''}、`;
          });

        this.$emit('confirm', material.slice(0, -1));
        done(true);
      } catch (err) {
        done(false);
      }
    },
    handleDelete(index) {
      this.materialList.splice(index, 1);
    }
  }
};
</script>
<style lang="scss" scoped>
.material-info {
  min-height: 30vh;
  max-height: 70vh;
  padding: 20px;
  overflow: auto;
  .material-list {
    .material-item {
      margin-bottom: 20px;
      overflow: hidden;
      border: 1px solid $border-color1;
      border-radius: 20px;
      ::v-deep .van-dropdown-menu {
        width: 100%;
        height: 100%;
        .van-dropdown-menu__bar {
          height: 100%;
          box-shadow: none;
        }
        .van-dropdown-menu__title {
          width: 100%;
        }
      }
    }
    .material-item-tool {
      margin-bottom: 8px;
      color: #dc0202;
      text-align: right;
    }
  }
}
</style>
