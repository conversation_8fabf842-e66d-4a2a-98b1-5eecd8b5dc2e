<template>
  <div class="index page-index">
    <van-nav-bar class="nav" left-text="青山嘴水库矩阵管理系统" :left-arrow="false" fixed>
      <template #right>
        <div class="nav-right">
          <img class="nav-right-icon" :src="weatherImg(weather)" alt="" />
          <span class="nav-right-text">{{ temperature }}<span class="nav-right-text_unit" v-if="temperature">℃</span></span>
        </div>
      </template>
    </van-nav-bar>
    <div class="main">
      <div class="greet">欢迎您 ，{{ time }}好 ！</div>
      <div class="user">
        <img class="user-icon" src="@/assets/images/index/user.png" alt="" />
        <div class="user-name">{{ userInfo.displayName }}</div>
        <div class="user-info">
          <!-- <div>上次登陆</div>
          <div class="info-time">2024-11-25 17:08:28</div> -->
        </div>
      </div>
      <div class="menu flex">
        <div class="menu-item" v-for="item in menuList" :key="item.id" @click="toPage(item.path)">
          <div class="menu-item-icon" :style="{ backgroundColor: item.bgColor }">
            <img :src="require(`@/assets/images/index/${item.icon}.png`)" alt="" />
          </div>
          <span>{{ item.title }}</span>
        </div>
      </div>
      <!-- 水库基本信息 -->
      <div class="info-card basic-info">
        <div class="info-card-title">水库基本信息</div>
        <div class="info-card-content">
          <div v-for="item in basicList" :key="item.value" class="info-item">
            <span class="info-item-label">{{ item.label }}：</span>
            <span class="info-item-value">{{ processBaseInfo(item.value) || '--' }}</span>
          </div>
        </div>
      </div>
      <!-- 巡查信息 -->
      <div class="info-card patrol-info">
        <div class="info-card-title">巡查信息</div>
        <div class="info-card-content">
          <div class="patrol-summary flex-vc">
            <div ref="patrolChart" class="patrol-summary-chart"></div>
            <div class="patrol-summary-desc">
              <div class="patrol-summary-desc-item">
                <img src="@/assets/images/index/sbwt-icon.png" alt="上报问题" />
                <span>上报问题</span>
                <span class="patrol-summary-num">{{ patrolData['上报问题'] || 0 }}</span
                >个
              </div>
              <div class="patrol-summary-desc-item">
                <img src="@/assets/images/index/yjj-icon.png" />
                <span>已解决</span>
                <span class="patrol-summary-num">{{ patrolData['已解决'] || 0 }}</span
                >个
              </div>
            </div>
          </div>
          <div class="patrol-count">
            <div v-for="item in patrolList" :key="item.value" class="patrol-count-item">
              <div class="item-left">
                <img :src="require(`@/assets/images/index/${item.value}.png`)" />
                {{ item.label }}:
              </div>
              <div class="item-right flex-vc">
                <span>
                  7日内巡查<span class="patrol-count-num">{{ item.num || 0 }}</span
                  >次
                </span>
                <!-- <van-progress :percentage="item.percent" /> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="info-card repair-info">
        <div class="info-card-title">维修养护</div>
        <div class="info-card-content flx-align-center">
          <div ref="repairChartRef" class="repair-summary-chart"></div>
          <div class="legend-part">
            <p class="tip">{{ curYear }}年维修项目</p>
            <div class="legend-item flx-align-center" v-for="item in legendList" :key="item.name">
              <div class="legend-left flx-align-center">
                <div class="legend-color" :style="{ 'background-color': item.itemStyle.color }"></div>
                <div class="legend-title">{{ item.name }}</div>
              </div>
              <div class="legend-num" :style="{ color: item.itemStyle.color }">{{ item.value }}个</div>
            </div>
          </div>
        </div>
      </div>
      <div class="flx w-full">
        <div class="info-card" style="width: 50%;border-right: 1px solid #cccccc;">
          <div class="info-card-title">杂草护理</div>
          <div class="sta-content flx-align-center">
            <img src="@/assets/images/index/tip-weed.png" alt="" />
            <p>
              {{ curYear }}年护理<span>{{ curWeedCount }}</span
              >次
            </p>
          </div>
        </div>
        <div class="info-card" style="width: 50%;padding-left: 20px;">
          <div class="info-card-title">日常保洁</div>
          <div class="sta-content flx-align-center">
            <img src="@/assets/images/index/tip-clean.png" alt="" />
            <p>
              {{ curYear }}年保洁<span>{{ curCleanCount }}</span
              >次
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState, mapGetters } from 'vuex';
// import updateApp from '@/mixins/updateApp';
import { getStorage } from '@/utils/storage';
import menuList from './menuList';
import { weatherConfig } from '@/utils/weatherConfig';
import { getProjectInfo } from '@/api/project';
import { getPatrolStatistics } from '@/api/patrolRecord';
import { statisticsCurrentYearByTypeApi } from '@/api/repair';
import { getCurYearWeedOrCleanCountApi } from '@/api/weedOrClean';
import drawChart from '@/mixins/drawChart';
import { getDictionaryTreeList } from '@/api/dict';
export default {
  name: 'Index',
  mixins: [drawChart],
  props: {},
  data() {
    return {
      time: '',
      curYear: new Date().getFullYear(),
      basicList: [
        { label: '水库名称', value: 'resName' },
        { label: '建成年份', value: 'compDate' },
        { label: '工程规模', value: 'engScal' },
        { label: '主管单位', value: 'cmun' },
        { label: '所在流域', value: 'basin' },
        { label: '所在河流', value: 'river' }
      ],
      dictTree: [],
      engScalDict: [],
      basicData: {},
      patrolList: [
        { label: '日常巡检', value: 'rcxj', num: 0, count: 14, percent: 0 },
        { label: '定期巡检', value: 'dqxj', num: 0, count: 1, percent: 0 },
        { label: '特殊巡检', value: 'tsxj', num: 0, percent: 0 }
      ],
      patrolData: {},
      menuList,
      legendList: [
        { value: 0, name: '设施检修', itemStyle: { color: '#5c70c8' } },
        { value: 0, name: '设备维护', itemStyle: { color: '#98cc72' } },
        { value: 0, name: '清淤疏浚', itemStyle: { color: '#f3c852' } },
        { value: 0, name: '防渗防漏', itemStyle: { color: '#e26664' } }
      ],
      curWeedCount: 0,
      curCleanCount: 0
    };
  },
  computed: {
    ...mapState({
      weather: state => state.weather.weather,
      temperature: state => state.weather.temperature,
      userInfo: state => state.user.userInfo || getStorage('userInfo')
    }),
    ...mapGetters('common', ['getOptions']),
    weatherImg() {
      return val => {
        let obj = weatherConfig.find(d => d.label === val);
        if (obj) {
          return obj.img;
        }
        return require('@/assets/images/weather/duoyun.png');
      };
    }
  },
  methods: {
    ...mapActions({
      getCurPosInfo: 'location/getCurPosInfo',
      getWeather: 'weather/getWeather'
    }),
    getTime() {
      const hour = new Date().getHours();
      if (hour < 5) {
        this.time = '晚上';
      } else if (hour < 11) {
        this.time = '早上';
      } else if (hour < 13) {
        this.time = '中午';
      } else if (hour < 18) {
        this.time = '下午';
      } else {
        this.time = '晚上';
      }
    },
    toPage(path) {
      this.$router.push({ path });
    },
    async loadDictTree() {
      if (this.dictTree.length === 0) {
        const res = await getDictionaryTreeList({ parentId: -1 });
        if (res.status === 200) {
          this.dictTree = res.data;
        }
      }
    },
    getEngScalDict() {
      getDictionaryTreeList({
        parentId: this.dictTree.find(item => item.dictCode === 'PROJECT_SCALE').id
      }).then(res => {
        if (res.status === 200) {
          this.engScalDict = res.data || [];
        }
      });
    },
    getProjectInfo() {
      getProjectInfo({ wrpcd: this.userInfo.assignWrpcdList?.[0] }).then(res => {
        if (res.status === 200) {
          this.basicData = res.data?.[0] || {};
          // this.basicData.gcgm = this.getOptions('projectScale').find(d => d.value === this.basicData.gcgm)?.label || '--';
        }
      });
    },

    getPatrolData() {
      getPatrolStatistics({
        wrpcd: this.userInfo.assignWrpcdList?.[0]
      }).then(res => {
        if (res.status === 200) {
          this.patrolData = res.data || {};
          this.patrolList.forEach(item => {
            item.num = this.patrolData[item.label] || 0;
            const percent =
              item.num ?? item.count ?? false
                ? Math.round((item.num / (item.count ? item.count : item.num)) * 100 * 100) / 100
                : 0;
            item.percent = percent > 100 ? 100 : percent;
          });
          this.$nextTick(() => {
            this.initPatrolChart();
          });
        }
      });
    },

    initPatrolChart() {
      const dom = this.$refs.patrolChart;
      const sbwts = this.patrolData['上报问题'] || 0;
      const yjjs = this.patrolData['已解决'] || 0;
      const percent = yjjs ?? sbwts ?? false ? Math.round((yjjs / sbwts) * 10000) / 100 : 0;
      const options = {
        tooltip: {
          formatter: `{a}: ${sbwts}个<br/>{b} : {c}%`,
          confine: true
        },
        series: [
          {
            name: '上报问题',
            type: 'gauge',
            radius: '90%',
            progress: {
              show: true
            },
            splitLine: { show: false },
            axisTick: { show: false },
            axisLabel: { show: false },
            detail: {
              valueAnimation: true,
              formatter: '{value}%',
              fontSize: 16,
              offsetCenter: [0, '85%']
            },
            title: {
              fontSize: 16,
              offsetCenter: [0, '50%']
            },
            data: [
              {
                value: percent,
                name: '已解决'
              }
            ]
          }
        ]
      };
      this.initChart(dom, options);
    },
    async drawRepairChart() {
      const res = await statisticsCurrentYearByTypeApi({
        wrpcd: this.userInfo.assignWrpcdList[0],
        year: this.curYear
      });
      if (res.data) {
        this.legendList.forEach(it => {
          it.value = res.data[it.name];
        });
        let centerTxt = this.legendList.map(item => item.value).reduce((acc, curr) => acc + curr, 0);
        let option = {
          tooltip: {
            trigger: 'item' // 触发类型，默认数据项触发，可选为：'item' | 'axis'
          },
          series: [
            {
              type: 'pie',
              radius: ['60%', '100%'],
              center: ['25%', '50%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 3
              },
              label: {
                show: true,
                position: 'center',
                color: '#333',
                formatter: '{total|' + `${this.curYear}年` + '}' + '\n\r' + `{active|${centerTxt || '暂无维修项目'}}`,
                rich: {
                  total: {
                    fontSize: 16,
                    color: '#333',
                    lineHeight: 22
                  },
                  active: {
                    fontSize: 18,
                    color: '#333',
                    fontWeight: 'bold'
                  }
                }
              },
              emphasis: {
                label: {
                  show: false
                }
              },
              labelLine: {
                show: false
              },
              data: this.legendList
            }
          ]
        };
        this.$nextTick(() => {
          let chartObj = this.$echarts.init(this.$refs.repairChartRef, null, { renderer: 'svg' });
          chartObj.setOption(option);
        });
      }
    },
    async getCurYearWeedOrCleanCount(type) {
      const res = await getCurYearWeedOrCleanCountApi({
        wrpcd: this.userInfo.assignWrpcdList[0],
        tabType: type,
        year: this.curYear
      });
      let count = this.$appUtils.checkNull(res.data) ? '--' : res.data;
      if (type == 1) {
        this.curCleanCount = count;
      } else {
        this.curWeedCount = count;
      }
    },
    processBaseInfo(value) {
      if (value === 'engScal') {
        return this.basicData[value] ? this.engScalDict.find(d => d.dictValue === this.basicData[value])?.dictName : '--';
      }
      if (value === 'compDate') {
        // 截取年份
        return this.basicData[value] ? this.basicData[value].slice(0, 4) : '--';
      }
      return this.basicData[value] || '--';
    }
  },
  created() {
    this.getTime();
    this.getWeather();
  },
  async mounted() {
    this.getProjectInfo();
    this.getPatrolData();
    this.drawRepairChart();
    this.getCurYearWeedOrCleanCount(1);
    this.getCurYearWeedOrCleanCount(2);
    await this.loadDictTree();
    this.getEngScalDict();
  }
};
</script>

<style lang="scss" scoped>
@import './index';
</style>
