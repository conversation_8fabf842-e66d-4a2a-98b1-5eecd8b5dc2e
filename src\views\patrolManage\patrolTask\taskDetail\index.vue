<template>
  <!-- 巡检详情 -->
  <main class="main flex-column">
    <CustomNavBar @click-left="toPage({ name: 'TaskList' })" />

    <baidu-map
      ref="baiduMap"
      class="map"
      show-polyline
      show-lushu
      show-start-point
      show-end-point
      :polyline="polyline"
      :center="center"
      :start-point="startPoint"
      :end-point="endPoint"
      @onMapReady="onMapReady"
    ></baidu-map>
    <!-- <div class="flex-1" style="overflow:hidden;">
      <CustomMap ref="mapRef" />
    </div> -->

    <div class="map-popover">
      <div class="popover-date">{{ routeInfo.xjStartTime }}</div>
      <div class="popover-content">
        <div class="content-item">
          <div class="item-value">
            {{ taskInfo.xjUserName }}
          </div>
          <div class="item-label">巡查人</div>
        </div>
        <div class="content-item">
          <div class="item-value">{{ routeInfo.time }}</div>
          <div class="item-label">用时</div>
        </div>
        <div class="content-item">
          <div class="item-value">{{ routeInfo.distance }} km</div>
          <div class="item-label">路程</div>
        </div>
      </div>
    </div>

    <div
      class="bottom-panel"
      :style="{
        bottom: `${visible ? 0 : -maxHeight}px`,
        transform: `translateY(${tranY}px)`,
        transition: `all ${transitionTime}s ease ${transitionTime / 3}s`
      }"
    >
      <div class="panel-toggle" @touchstart="touchStart" @touchmove="onTouchmove" @touchend="onTouchend"></div>
      <div class="panel-content" ref="panelContent">
        <div class="content-title flex-vc j-sb">
          隐患情况
          <span class="content-back flex-vc" @click="toPage({ name: 'TaskList' })">
            返回<van-icon name="arrow" class="ml20" />
          </span>
        </div>
        <div class="content-summary flex-hvc" :class="{ 'content-summary--error': taskInfo.status }">
          <img
            class="summary-icon"
            :src="
              taskInfo.status
                ? require('@/assets/images/patrol/patrolTask/error.png')
                : require('@/assets/images/patrol/patrolTask/normal.png')
            "
            alt=""
          />
          <span>{{ taskInfo.status ? '有隐患' : '无隐患' }}</span>
        </div>
        <div class="content-title">检查情况</div>
        <div class="content-detail">
          <RecordCell
            class="detail-item"
            v-for="data in taskInfo.situationListVOList"
            :key="data.id"
            :data="data"
            :show-btn="false"
          />
        </div>
        <!-- <div class="content-title">现场照片</div>
        <div class="content-detail">
          <CustomUpload :module-id="9" module="xjxc" :deletable="false" :show-upload="false" disabled />
        </div> -->
      </div>
    </div>
  </main>
</template>

<script>
import BaiduMap from '@/components/BaiduMap';
import CustomMap from '@/components/CustomMap/index.vue';
import RecordCell from '../taskRecord/recordCell.vue';
// import CustomUpload from '@/components/CustomUpload/index.vue';
import touch from '@/mixins/touch';
import { getTaskInfoById, getRouteByTaskId } from '@/api/patrolManage/patrolTask/index.js';

// import { getCurrentPosition } from '@/utils/location';
export default {
  name: 'TaskDetail',
  components: {
    BaiduMap,
    RecordCell,
    CustomMap
    // CustomUpload
  },
  props: {},
  mixins: [touch],
  data() {
    return {
      isMapReady: false,
      routeInfo: {},
      taskInfo: {
        situationListVOList: []
      },
      polyline: [],
      center: { lng: 113.384429, lat: 23.35814 },
      startPoint: {},
      endPoint: {}
    };
  },
  computed: {},
  watch: {},
  created() {
    const param = this.$route.query;
    this.taskId = +param.taskId;
    this.getTaskInfo();
    this.getRouteInfo();
    document.addEventListener(
      'backbutton',
      e => {
        e.preventDefault();
        this.toPage({ name: 'TaskList' });
      },
      false
    );
  },
  mounted() {},
  methods: {
    toPage(item) {
      let { name, ...rest } = item;
      if (name) {
        this.$router.push({
          name,
          query: { ...rest }
        });
      }
    },
    onMapReady() {
      this.isMapReady = true;
      // getCurrentPosition();
    },
    touchStart(e) {
      if (!this.maxHeight) {
        this.maxHeight = this.$refs.panelContent.offsetHeight;
      }
      this.onTouchStart(e);
    },
    getTaskInfo() {
      getTaskInfoById({
        taskId: this.taskId
      }).then(res => {
        if (res.status === 200 && res.data) {
          this.taskInfo = res.data;
          this.taskInfo.status = (res.data.situationListVOList || []).some(i => i.stnType === 2);
        }
      });
    },
    getRouteInfo() {
      getRouteByTaskId({
        taskId: this.taskId
      }).then(res => {
        if (res.status === 200 && res.data) {
          this.routeInfo = res.data;
          if (res.data.route) {
            this.polyline = JSON.parse(res.data.route);
            if (this.polyline.length) {
              this.center = this.polyline[0];
              this.startPoint = this.polyline[0];
              this.endPoint = this.polyline[this.polyline.length - 1];
            }
          }

          if (res.data.coordinates) {
            const coordinates = JSON.parse(res.data.coordinates);
            if (coordinates.length) {
              const [startLng, startLat] = coordinates[0];
              const [endLng, endLat] = coordinates[coordinates.length - 1];

              const ptArr = [
                {
                  lng: startLng,
                  lat: startLat,
                  Name: '起点',
                  iconUrl: require('@/assets/images/patrol/start.png'),
                  scale: 0.5,
                  isAnimate: false,
                  labelZoom: 12,
                  pointZoom: 8
                },

                {
                  lng: endLng,
                  lat: endLat,
                  Name: '终点',
                  iconUrl: require('@/assets/images/patrol/end.png'),
                  scale: 0.5,
                  isAnimate: false,
                  labelZoom: 12,
                  pointZoom: 8
                }
              ];

              this.$nextTick(() => {
                if (!this.$refs.mapRef) {
                  return;
                }
                this.$refs.mapRef.setArrPtLayerVisible({
                  isVisible: true,
                  layerName: 'patrolPoint',
                  lngFieldName: 'lng',
                  latFieldName: 'lat',
                  labelFieldName: 'Name',
                  ptArr
                });
                this.$refs.mapRef.toggleGeoJson({
                  idf: 'patrolTrack',
                  optType: 'showLayer',
                  features: [
                    {
                      type: 'Feature',
                      geometry: {
                        type: 'LineString',
                        coordinates
                      },
                      properties: {}
                    }
                  ],
                  style: {
                    outlineColor: '#3E80ED',
                    fillColor: '#3E80ED',
                    outlineWidth: 5
                  }
                });
                this.$refs.mapRef.toggleGeoJson({
                  idf: 'patrolTrack',
                  optType: 'flyToLayer'
                });
              });
            }
          }
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
}
.map {
  width: 100%;
  height: 100%;
}
.map-popover {
  position: fixed;
  top: 123px;
  right: 32px;
  left: 32px;
  padding: 24px 0 42px;
  font-size: 28px;
  line-height: 36px;
  color: $color-text-white;
  background: rgba($color-primary, 0.9);
  border-radius: 24px;
  .popover-date {
    margin-bottom: 16px;
    font-size: 26px;
    line-height: 32px;
    color: rgba($color-text-white, 0.5);
    text-align: center;
  }
  .popover-content {
    display: flex;
    align-items: center;
    .content-item {
      flex: 1;
      text-align: center;
      .item-value {
        font-size: 32px;
        font-weight: 500;
        line-height: 40px;
      }
      .item-label {
        margin-top: 2px;
        color: rgba($color-text-white, 0.8);
      }
    }
  }
}
.bottom-panel {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  font-size: 28px;
  background: $bg-page;
  border-radius: 24px 24px 0 0;
  box-shadow: 0 -10px 16px rgba($color-text-black, 0.06);
  .panel-toggle {
    height: 36px;
  }
  .panel-content {
    padding: 0 32px 32px;
    .content-back {
      font-size: 28px;
      color: $color-primary;
    }
    .content-title {
      position: relative;
      padding-left: 26px;
      margin-bottom: 18px;
      font-size: 36px;
      font-weight: 500;
      color: $color-text-black;
      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 10px;
        height: 30px;
        content: '';
        background: $color-primary;
        border-radius: 5px;
        transform: translateY(-50%);
      }
    }
    .content-summary {
      height: 80px;
      margin-bottom: 32px;
      font-size: 32px;
      font-weight: 500;
      color: $color-success;
      background: url('~@/assets/images/patrol/patrolTask/normal-bg.png') no-repeat 0 0;
      background-size: 100% 100%;
      .summary-icon {
        width: 40px;
        height: 40px;
        margin-right: 16px;
      }
    }
    .content-summary--error {
      color: $color-error;
      background: url('~@/assets/images/patrol/patrolTask/error-bg.png') no-repeat 0 0;
      background-size: 100% 100%;
    }
    .content-detail {
      max-height: 20vh;
      overflow: auto;
      overflow: overlay;
      .detail-item {
        padding: 0;
      }
    }
  }
}
</style>
