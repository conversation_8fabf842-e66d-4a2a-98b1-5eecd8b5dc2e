<template>
  <div class="dateTimeRange">
    <div class="flex-vc">
      <div class="timeBar flex-1 flex-hvc" @click="handleClickTime">
        <div>{{ time || '请选择' }}</div>
        <div class="time-btn flex-vc">
          <van-icon name="close" v-if="clearable && time" @click.stop="clearDate" />
          <van-icon name="arrow-down" color="#0f5eda" />
        </div>
      </div>
      <van-icon class="action-icon ml30" v-if="showAction" name="filter-o" color="#266fe8" @click="handleClickAction" />
    </div>
    <van-popup v-model="showSelectTime" position="bottom" :style="{ height: '50%' }" get-container="body">
      <van-datetime-picker
        v-model="currentDate"
        v-bind="$attrs"
        :type="timeType"
        title="请选择"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="confirmDate"
        @cancel="showSelectTime = false"
        @change="changeDate"
      />
    </van-popup>
  </div>
</template>

<script>
import dayjs from 'dayjs';
const minRangeDate = new Date(+dayjs().subtract(10, 'year'));
const maxRangeDate = new Date(+dayjs().add(10, 'year'));
export default {
  name: 'DateTimePicker',
  props: {
    value: {
      default: () => dayjs()
    },
    format: {
      default: 'YYYY-MM-DD'
    },
    valueFormat: {
      default: 'YYYY-MM-DD'
    },
    timeType: {
      default: 'date'
    },
    clearable: {
      type: Boolean,
      default: false
    },
    showAction: {
      type: Boolean,
      default: false
    },
    minDate: {
      type: Date,
      default: () => minRangeDate
    },
    maxDate: {
      type: Date,
      default: () => maxRangeDate
    }
  },
  data() {
    return {
      showSelectTime: false,
      time: ''
    };
  },
  computed: {
    currentDate: {
      get() {
        if (dayjs(this.value).isValid()) {
          return new Date(dayjs(this.value));
        }
        return this.value || '';
      },
      set(val) {
        const time = this.formartTime(val, this.valueFormat);
        this.$emit('input', time);
      }
    }
  },
  created() {
    this.time = this.formartTime(this.value, this.format);
  },
  methods: {
    formartTime(time, format, empty = '') {
      if (dayjs(this.value).isValid()) {
        return dayjs(time).format(format);
      }
      return time || empty;
    },
    handleClickTime() {
      this.showSelectTime = true;
    },
    changeDate({ getValues }) {
      this.$emit('changeDate', getValues());
    },
    confirmDate() {
      this.showSelectTime = false;
      this.$nextTick(() => {
        const time = this.formartTime(this.currentDate, this.valueFormat);
        this.time = time;
        this.$emit('confirmDate', time);
      });
    },
    clearDate() {
      this.currentDate = '';
      this.$emit('confirmDate', '');
    },
    handleClickAction() {
      this.$emit('clickAction');
    }
  }
};
</script>

<style lang="scss" scoped>
.dateTimeRange {
  box-sizing: border-box;
  width: 100%;
  padding: 0 32px;
  font-size: 28px;
  line-height: 36px;
  .action-icon {
    font-size: 48px;
  }
}
.timeBar {
  position: relative;
  box-sizing: border-box;
  height: 76px;
  padding: 0 26px;
  background-color: #ffffff;
  border: 1px solid $border-light;
  border-radius: 20px;
  .time-btn {
    position: absolute;
    right: 20px;
    gap: 10px;
  }
}
.time-icon .van-icon-close {
  margin-right: 5px;
}
</style>
