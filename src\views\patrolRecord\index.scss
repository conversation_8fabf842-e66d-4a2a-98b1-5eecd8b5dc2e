* {
  box-sizing: border-box;
}
.danger-record {
  color: $color-text-main;
  .main {
    height: 100%;
    padding: 15px 15px 0;
    background: url('~@/assets/images/dangerRecord/bg.png') no-repeat 0 0;
    background-size: 100% 100%;
    .nav {
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 15px 0;
      .nav-item {
        flex: 1;
        text-align: center;
        .item-icon {
          width: 66px;
          height: 66px;
          margin-bottom: 8px;
        }
        .item-text {
          font-size: 26px;
          font-weight: 500;
          color: $color-text-black;
        }
      }
    }
    .content {
      flex: 1;
      padding: 12px 16px;
      overflow: hidden;
      background: $bg-page;
      border-radius: 30px;
      &::v-deep {
        .tab-title {
          font-size: 30px;
          font-weight: 500;
          background-image: linear-gradient(180deg, #f5f7fb 0%, #fbfcfe 100%);
        }
        .van-tab--active {
          background: $bg-page;
        }
        .van-tabs__nav {
          height: 70px;
          overflow: hidden;
          border-radius: 30px 30px 0 0;
        }
      }
      .content-detail {
        height: calc(100% - 95px);
        padding: 10px;
        overflow: auto;
        background: $bg-page1;
        border: 1px solid $border-color1;
        border-radius: 30px;
        -webkit-overflow-scrolling: touch;
        .detail-item {
          display: flex;
          padding: 10px;
          margin-bottom: 10px;
          font-size: 28px;
          font-weight: 500;
          background: $bg-page;
          border: 1px solid $border-color1;
          border-radius: 30px;
          &:last-child {
            margin-bottom: 0;
          }
          .item-left {
            // flex: 1;
            width: 32%;
            padding: 24px;
            border-right: 4px solid $color-primary;
            .time {
              white-space: nowrap;
            }
          }
          .item-right {
            flex: 1;
            padding: 20px 0 20px 28px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .right-row {
              display: flex;
              align-items: center;
              justify-content: space-between;
              .row-text {
                flex: 1;
                padding-right: 3px;
              }
            }
          }
          .text-with-icon {
            flex: 1;
            padding-left: 37px;
          }
          .icon-time {
            background: url('~@/assets/images/dangerRecord/time.png') no-repeat left top;
            background-size: 28px 28px;
          }
          .icon-person {
            background: url('~@/assets/images/dangerRecord/person.png') no-repeat left top;
            background-size: 28px 28px;
            span {
              word-break: break-all;
            }
          }
          .icon-problem {
            background: url('~@/assets/images/dangerRecord/problem.png') no-repeat left top;
            background-size: 28px 28px;
          }
          .icon-setting {
            background: url('~@/assets/images/dangerRecord/setting.png') no-repeat left top;
            background-size: 28px 28px;
          }
          .text-mini {
            font-size: 24px;
            line-height: 33px;
            color: $color-text-vice;
          }
          .text-large {
            font-size: 30px;
            line-height: 45px;
          }
          .text-large-more {
            font-size: 36px;
            line-height: 45px;
          }
          .text-bottom {
            margin-top: 8px;
            margin-bottom: 30px;
          }
          .text-align_center {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }
  }
}
