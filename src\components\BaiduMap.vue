<template>
  <baidu-map :key="aliveKey" :ak="ak" :center="center" :zoom="zoom" @ready="onMapReady">
    <!-- <bm-geolocation anchor="BMAP_ANCHOR_TOP_RIGHT"></bm-geolocation> -->
    <bm-polyline v-if="showPolyline" :path="polyline" stroke-color="#3E80ED" :stroke-opacity="1" :stroke-weight="4"></bm-polyline>
    <bml-lushu v-if="showLushu" :path="polyline" :speed="300" :play="true"></bml-lushu>

    <bm-marker
      v-if="showMarker"
      :position="center"
      :icon="{
        url: require('@/assets/images/index/marker.png'),
        size: { width: 40, height: 53 },
        opts: { anchor: { width: 20, height: 53 } }
      }"
    ></bm-marker>

    <bm-marker
      v-if="showStartPoint"
      :position="startPoint"
      :icon="{
        url: require('@/assets/images/patrol/start.png'),
        size: { width: 19, height: 28 },
        opts: { anchor: { width: 9, height: 28 } }
      }"
    ></bm-marker>

    <bm-marker
      v-if="showEndPoint"
      :position="endPoint"
      :icon="{
        url: require('@/assets/images/patrol/end.png'),
        size: { width: 19, height: 28 },
        opts: { anchor: { width: 9, height: 28 } }
      }"
    ></bm-marker>

    <bm-local-search
      v-if="showLocalSearch"
      :keyword="searchKeyword"
      :auto-viewport="true"
      :select-first-result="true"
      :panel="false"
      location="广州"
      @searchcomplete="onSearchComplete"
    ></bm-local-search>
  </baidu-map>
</template>

<script>
import BaiduMap from 'vue-baidu-map/components/map/Map.vue';
import BmPolyline from 'vue-baidu-map/components/overlays/Polyline.vue';
import BmMarker from 'vue-baidu-map/components/overlays/Marker.vue';
import { BmlLushu, BmLocalSearch } from 'vue-baidu-map';
import baiduConfig from '@/utils/baiduConfig';
// import BmGeolocation from 'vue-baidu-map/components/controls/Geolocation.vue'
export default {
  name: 'CustomBaiduMap',
  components: {
    BaiduMap,
    BmPolyline,
    BmMarker,
    BmlLushu,
    BmLocalSearch
    // BmGeolocation
  },
  props: {
    center: {
      type: Object,
      default: () => ({ lng: 101.48684, lat: 25.114957 })
    },
    zoom: {
      type: Number,
      default: 16
    },
    // 轨迹
    showPolyline: {
      type: Boolean,
      default: false
    },
    polyline: {
      type: Array,
      default: () => []
    },
    // 路书
    showLushu: {
      type: Boolean,
      default: false
    },
    // 当前位置
    showMarker: {
      type: Boolean,
      default: false
    },
    // 起点
    showStartPoint: {
      type: Boolean,
      default: false
    },
    startPoint: {
      type: Object,
      default: () => {}
    },
    // 终点
    showEndPoint: {
      type: Boolean,
      default: false
    },
    endPoint: {
      type: Object,
      default: () => {}
    },
    // 搜索
    showLocalSearch: {
      type: Boolean,
      default: false
    },
    searchKeyword: {
      type: String,
      default: ''
    }
  },
  data() {
    let ak = 'w9M6a5fh8Bd6h49r3adjeHqFvxephpVC';
    if (window.plus) {
      const param = baiduConfig[window.plus.os.name];
      if (param) {
        ak = param.ak;
      }
    }
    return {
      ak,
      BMap: null,
      map: null,
      isMapReady: false,
      aliveKey: 0
    };
  },
  computed: {},
  watch: {},
  created() {},
  activated() {
    if (this.isMapReady) {
      this.aliveKey++;
    }
  },
  methods: {
    // 初始化地图
    onMapReady({ BMap, map }) {
      this.BMap = BMap;
      this.map = map;
      this.$store.commit('patrol/setMap', { BMap, map });

      this.isMapReady = true;
      this.$emit('onMapReady', BMap, map);
    },
    // 搜索回调
    onSearchComplete(res) {
      if (res && res.Yr) {
        this.$emit('onMapSearch', [...res.Yr]);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .anchorBL {
  display: none !important;
}
</style>
