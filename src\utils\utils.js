import Pinyin from 'js-pinyin';

// let mergeArr = [];//合并表格数组
const utils = {
  fromJson: obj => JSON.parse(obj), //字符串转json
  toJson: obj => JSON.stringify(obj), //json转字符串
  regPhone: /^(13|15|18|14|17)[0-9]{9}$/g, //手机号正则匹配
  regEmail: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/g,
  regSixCode: /^\d{6}$/g, //六位验证码正则匹配
  regIntegral: /^[0-9]*$/g, //积分数字正则匹配
  //获取url参数
  getQueryArg() {
    const href = location.href;
    if (href.includes('?')) {
      let a = href.split('?');
      const b = a[1];
      //多个参数
      if (b.includes('&')) {
        const c = b.split('&');
        let obj = {};
        c.map(item => {
          const d = item.split('=');
          obj[d[0]] = d[1];
        });
        return obj;
      } else {
        //一个参数
        let obj = {};
        const c = b.split('=');
        obj[c[0]] = c[1];
        return obj;
      }
    }
  },
  //element表单校验手机
  checkTelephone(rule, value, callback) {
    let reg = /^1[34578]\d{9}$/;
    if (!value) {
      return callback(new Error('请输入手机号码'));
    } else if (!reg.test(value)) {
      callback(new Error('请输入正确的11位手机号码'));
    } else {
      callback();
    }
  },
  //函数节流 method为回调执行的方法，wait为执行的时间，_this为了让函数能获取到this的作用域
  throttle(func, wait) {
    let timer = null; // 定时器
    let result = null;
    let previous = 0; // 之前的时间戳
    return function(...params) {
      const now = new Date();
      const context = this;
      // remaining 用于判断上次执行到本次执行的时间间隔是否超过了设置的时间间隔
      const remaining = wait - (now - previous);
      if (remaining <= 0) {
        // remaining <= 0 表示上次至此与本次的时间间隔已经超过了设置的时间间隔，
        // 可以执行回调了，并重置定时器 ，重新缓存本次的执行时间戳
        clearTimeout(timer);
        previous = now;
        timer = null;
        result = func.apply(context, params);
      } else if (!timer) {
        // 如果设置的时间间隔，且定时器为空，则等待剩余的间隔
        timer = setTimeout(() => {
          previous = now;
          timer = null;
          result = func.apply(context, params);
        }, remaining);
      }
      return result;
    };
  },
  // 函数防抖 method为回调执行的方法，wait为执行的时间，_this为了让函数能获取到this的作用域
  debounce(func, wait) {
    let timer = null; // 定时器
    return function(...params) {
      clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(this, params);
      }, wait);
    };
  },
  /*计算表格高度
    _this当前vue实例，_this必传
    num 需要减少的高度，默认为0
    waitTime延时执行时间，默认200
    height要设置表格高度的字段名，默认tableHeight
    autoTable表格ref名，默认autoTable
    pageWrap分页ref名，默认pageWrap*/
  getTableHeight(_this, num = 0, waitTime = 200, height = 'tableHeight', autoTable = 'autoTable', pageWrap = 'pageWrap') {
    _this.$nextTick(() => {
      let timer = null;
      const bodyHeight = document.documentElement.offsetHeight || document.body.offsetHeight;
      const tableSetTop = _this.$refs[autoTable] ? _this.$refs[autoTable].offsetTop : 0;
      const pageHeight = _this.$refs[pageWrap] ? _this.$refs[pageWrap].offsetHeight : 0;
      const autoHeight = bodyHeight - tableSetTop - pageHeight - num;
      // console.log(bodyHeight,'bodyHeight')
      // console.log(tableSetTop,'tableSetTop')
      // console.log(pageHeight,autoTable)
      window.addEventListener('resize', () => {
        clearTimeout(timer);
        timer = setTimeout(() => {
          utils.getTableHeight(_this, num, waitTime, height, autoTable, pageWrap);
        }, waitTime);
      });
      console.log('autoHeight', autoHeight);
      _this.$set(_this.$data, height, autoHeight);
    });
  },
  getDomHeight(_this, num = 0, waitTime = 200, parentDom = 'mailContent', className) {
    _this.$nextTick(() => {
      let timer = null;
      console.log(_this.$refs[parentDom], 'parent');
      console.log(_this.$refs[parentDom].getElementsByClassName(className)[0], 'domItem');
      const domItem = _this.$refs[parentDom].getElementsByClassName(className)[0];
      // console.log(domItem,'domItem')
      const bodyHeight = document.documentElement.offsetHeight || document.body.offsetHeight;
      const domTop = domItem ? domItem.offsetTop : 0;
      // const domHeight = document.getElementsByClassName(className)[0].offsetHeight || 0;
      const autoHeight = bodyHeight - domTop - num;
      // console.log(autoHeight,'height')
      // console.log(bodyHeight,'bodyHeight')
      // console.log(domTop,'domTop')
      // console.log(num,'num')
      window.addEventListener('resize', () => {
        clearTimeout(timer);
        timer = setTimeout(() => {
          utils.getDomHeight(_this, num, waitTime, parentDom, className);
        }, waitTime);
      });
      domItem.style.height = autoHeight + 'px';
    });
  },
  // 紧适用flex布局
  getDomAutoHeight(_this, dom, domWrap) {
    _this.$nextTick(() => {
      console.log('height', _this.$refs[domWrap].clientHeight);
      _this.$refs[dom].style.height = _this.$refs[domWrap].clientHeight + 'px';
    });
  },
  getCustomWidth(_this, waitTime = 200, width = 'customWidth', item) {
    _this.$nextTick(() => {
      let timer = null;
      const itemWidth = _this.$refs[item] ? _this.$refs[item].offsetWidth : 0;
      window.addEventListener('resize', () => {
        clearTimeout(timer);
        timer = setTimeout(() => {
          utils.getCustomWidth(_this, waitTime, width, item);
        }, waitTime);
      });
      _this.$set(_this.$data, width, itemWidth);
    });
  },
  //合并表格相同的列(rowIndex当前行index, dataList合并的数据，name合并的字段名)
  mergeTableRow(rowIndex, dataList, name) {
    if (dataList.length === 0) {
      return;
    }
    let mergeArr = [],
      mergeIndex = 0;
    dataList.forEach((item, index) => {
      if (index === 0) {
        mergeArr.push(1);
      } else {
        //判断连续相同的列合并
        if (item[name] === dataList[index - 1][name]) {
          mergeArr[mergeIndex] += 1;
          mergeArr.push(0);
        } else {
          mergeArr.push(1);
          mergeIndex = index;
        }
      }
    });
    const _row = mergeArr[rowIndex];
    const _col = _row > 0 ? 1 : 0;
    return {
      rowspan: _row,
      colspan: _col
    };
  },
  //转化附件大小，fileSize（附件大小，以KB为单位，大于1024进行转化），changeSize（要转化单位的大小，默认1024），unit(转化的单位，默认为M)
  filterFileSize(fileSize = 0, changeSize = 1024, unit = 'M') {
    let newSize = '';
    if (fileSize > 1024) {
      newSize = (fileSize / changeSize).toFixed(2) + unit;
    } else {
      newSize = fileSize + 'KB';
    }
    return newSize;
  },
  // 数组排序
  arrSort(arr, key) {
    return arr.sort((a, b) => {
      return a[key] - b[key];
    });
  },
  // 数组去重
  uniqueArr(arr) {
    return Array.from(new Set(arr));
  },
  // 对象数组去重
  uniqueObjArr(objArr, id = 'id') {
    let result = [],
      temp = {};
    for (let i = 0; i < objArr.length; i++) {
      let key = objArr[i][id];
      if (temp[key]) {
        continue;
      }
      temp[key] = true;
      result.push(objArr[i]);
    }
    return result;
  },
  // 获取当前日期
  getCurrentDate(type = 'yyyy-MM-dd') {
    let currentDate = '';
    let current = new Date();
    let year = current.getFullYear();
    let month = current.getMonth() + 1;
    let date = current.getDate();
    let hour = current.getHours();
    let minutes = current.getMinutes();
    let seconds = current.getSeconds();
    if (month < 10) {
      month = '-0' + month;
    } else {
      month = '-' + month;
    }
    if (date < 10) {
      date = '-0' + date;
    } else {
      date = '-' + date;
    }
    seconds = seconds < 10 ? '0' + seconds : seconds;
    switch (type) {
      case 'yyyy':
        currentDate = year;
        break;
      case 'yyyy-MM-dd':
        currentDate = year + month + date;
        break;
      case 'yyyy-MM-dd HH:mm':
        currentDate = year + month + date + ' ' + hour + ':' + minutes;
        break;
      case 'yyyy-MM-dd HH:mm:ss':
        currentDate = year + month + date + ' ' + hour + ':' + minutes + ':' + seconds;
        break;
    }
    return currentDate + '';
  },
  getUa() {
    let _browserType = '';
    let userAgent = window.navigator.userAgent.toLowerCase();
    console.log(userAgent, 'userAgent');
    if (userAgent.indexOf('linux') !== -1) {
      return 'linux';
    }
    if (userAgent.indexOf('firefox') !== -1) {
      _browserType = 'Firefox';
    } else if ((userAgent.indexOf('msie') !== -1 || userAgent.indexOf('rv') !== -1) && userAgent.indexOf('trident') !== -1) {
      _browserType = 'IE';
    } else if (userAgent.indexOf('wow') < 0 && userAgent.indexOf('edge') < 0) {
      if (userAgent.indexOf('safari') !== -1 && userAgent.indexOf('chrome') === -1) {
        _browserType = 'Safari';
      } else {
        _browserType = 'Chrome';
      }
    } else if (userAgent.indexOf('wow') !== -1 && userAgent.indexOf('net') < 0 && userAgent.indexOf('firefox') < 0) {
      _browserType = '360';
    } else {
      _browserType = 'other';
    }

    return _browserType;
  },
  // 格式化时间
  formatDate(date, fmt) {
    if (!date) {
      return '';
    }
    if (typeof fmt !== 'string') {
      return date;
    }
    let pad = time => {
      return String(time).padStart(2, '0');
    };

    if (typeof date === 'string') {
      date = date.replace(/\-/g, '/');
    }

    date = new Date(date);
    let obj = {
      yyyy: date.getFullYear(),
      MM: pad(date.getMonth() + 1),
      dd: pad(date.getDate()),
      HH: pad(date.getHours()),
      mm: pad(date.getMinutes()),
      ss: pad(date.getSeconds())
    };

    let newDate = fmt;
    for (let [k, v] of Object.entries(obj)) {
      if (newDate.includes(k)) {
        newDate = newDate.replace(k, v);
      }
    }

    return newDate;
  },
  // 时间转换
  formatTime(time, fmt) {
    if (typeof fmt !== 'string') {
      return time;
    }
    time = Number(time);

    let hour = Math.floor(time / (60 * 60 * 1000));
    let extraTime = time % (60 * 60 * 1000);

    let minutes = Math.floor(extraTime / (60 * 1000));
    extraTime = extraTime % (60 * 1000);

    let second = Math.floor(extraTime / 1000);

    let pad = t => {
      return String(t).padStart(2, '0');
    };

    let obj = {
      h: hour,
      H: pad(hour),
      m: minutes,
      M: pad(minutes),
      s: second,
      S: pad(second)
    };

    let newStr = fmt;
    for (let [k, v] of Object.entries(obj)) {
      if (newStr.includes(k)) {
        newStr = newStr.replace(k, v);
      }
    }

    return newStr;
  },
  // 根据首字母排序
  sortByFirstLetter(origin) {
    origin = origin.sort((pre, next) => Pinyin.getFullChars(pre.name).localeCompare(Pinyin.getFullChars(next.name)));
    const newArr = [];
    origin.map(item => {
      // 取首字母
      const key = Pinyin.getFullChars(item.name).charAt(0);
      const index = newArr.findIndex(subItem => subItem.key === key);
      if (index < 0) {
        newArr.push({
          key: key,
          list: [item]
        });
      } else {
        newArr[index].list.push(item);
      }
    });
    return newArr;
  },
  /**
   * 地点坐标计算中心点
   * @param geoCoordinateList {Array<Array>} [[lat, lng]]
   * @return { Object } {lat lng}
   */
  getCenterPoint(geoCoordinateList) {
    const geoCoordinateListFlat = JSON.parse(geoCoordinateList).map(item => ({
      lng: item[0],
      lat: item[1]
    }));
    const total = geoCoordinateListFlat.length;
    let X = 0;
    let Y = 0;
    let Z = 0;
    for (const g of geoCoordinateListFlat) {
      const lat = (g.lat * Math.PI) / 180;
      const lon = (g.lng * Math.PI) / 180;
      const x = Math.cos(lat) * Math.cos(lon);
      const y = Math.cos(lat) * Math.sin(lon);
      const z = Math.sin(lat);
      X += x;
      Y += y;
      Z += z;
    }

    X = X / total;
    Y = Y / total;
    Z = Z / total;
    const Lon = Math.atan2(Y, X);
    const Hyp = Math.sqrt(X * X + Y * Y);
    const Lat = Math.atan2(Z, Hyp);

    return { lng: (Lon * 180) / Math.PI, lat: (Lat * 180) / Math.PI };
  },
  // 文件下载   fileName：生成文件名 ，      blob: blob对象
  downloadFileByBlob(fileName, res) {
    const blob = new Blob([res]);
    let blobUrl = window.URL.createObjectURL(blob);
    const exportFile = document.createElement('a');
    exportFile.style.display = 'none';
    exportFile.download = fileName;
    exportFile.href = blobUrl;
    document.body.appendChild(exportFile);
    exportFile.click();
    document.body.removeChild(exportFile);
    window.URL.revokeObjectURL(blobUrl);
  },

  /**
   * blod转base64
   */
  blobToBase64(blob) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        resolve(reader.result);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  },
  /**
   * 统计数据判断返回为空还是实际值
   */
  checkNull(value) {
    return [null, undefined, '', NaN].includes(value);
  }
};
export default utils;
