// 主题色
$color-primary: #266fe8;
$color-error: #dc0202;
$color-warning: #ff7a0d;
$color-success: #00c462;
$color-main: #266fe8;
$color-red: #ea1255;
$color-orange: #ffdb40;
$color-green: #23de0f;
$color-note: #e6503f;
$color-blue: #7967e5;
$color-purple: #ff33a9;

// 文字色
$color-text-active: #3177ec;
$color-text-link-main: $color-primary;
$color-text-blue-light: #3177ec;
$color-text-blue-deep: #7967e5;
$color-text-weak: #0cffff;
$color-text-red: #dc0202;
$color-text-green: #19fc01;
$color-text-black: #000000;
$color-text-main: rgb(0 0 0 / 90%);
$color-text-vice: rgb(0 0 0 / 60%);
$color-text-vice1: rgb(0 0 0 / 40%);
$color-text-vice2: rgb(0 0 0 / 26%);
$color-text-vice3: rgb(0 0 0 / 20%);
$color-text-white: #ffffff;
$color-text-gray: #333333;
$color-text-gray1: #666666;
$color-text-gray-light: #cecece;

// 背景色
$bg-page: #ffffff;
$bg-page-main: #009bef;
$bg-page-light: #044463;
$bg-page-gray: #f5f5f5;
$bg-page1: #f4f5f6;
$bg-page2: #f82aaa;
$bg-page3: #f07c4b;
$bg-page4: #f6cb1e;
$bg-page5: #04e090;

// 边框
$border-light: rgba(0 0 0 / 12%);
$border-lighter: #ffcbdb;
$border-color1: #cccccc;
$font14: 12px;
$font14: 14px;
$font14: 16px;
$font14: 18px;
$font14: 20px;
$font22: 22px;
$font24: 24px;
$font26: 26px;
$font28: 28px;
$font30: 30px;
$font32: 32px;
$font34: 34px;
$font36: 36px;
$font40: 40px;
$font50: 50px;
$icon-size1: 32px;
$icon-size2: 36px;
$icon-size3: 48px;
$icon-size4: 54px;
$icon-size5: 60px;
