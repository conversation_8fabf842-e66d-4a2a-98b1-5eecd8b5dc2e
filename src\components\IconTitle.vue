<template>
  <div class="icon-title-box">
    <img :src="img" alt="" class="img" />
    <span class="title">{{ title }}</span>
  </div>
</template>

<script>
export default {
  components: {},
  computed: {},
  props: {
    img: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {},
  created() {},
  mounted() {}
};
</script>

<style lang="scss" scoped>
.icon-title-box {
  display: flex;
  align-items: center;
  font-size: 28px;
  font-weight: 400;
  color: $color-text-main;
  .img {
    width: 46px;
    height: 46px;
  }
  .title {
    margin-left: 17px;

    // max-width: 339px;
    line-height: 45px;
  }
}
</style>
