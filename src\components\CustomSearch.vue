<template>
  <van-search
    class="r-search"
    v-model.trim="keyword"
    shape="round"
    :placeholder="placeholder"
    :show-action="showAction"
    @search="onSearch"
    @cancel="onCancel"
  >
    <template #action>
      <div class="search-action" @click="onCancel">
        <van-icon name="filter-o" class="icon" />
      </div>
    </template>
    <template #right-icon>
      <div class="search-btn">
        <span @click="onSearch">搜索</span>
      </div>
    </template>
  </van-search>
</template>

<script>
export default {
  name: 'rSearch',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入关键字'
    },
    showAction: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  watch: {},
  computed: {
    keyword: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  methods: {
    onSearch() {
      this.$emit('search', this.keyword);
    },
    onCancel() {
      this.$emit('cancel');
    }
  }
};
</script>

<style lang="scss" scoped>
.r-search {
  .search-action {
    display: flex;
    align-items: center;
    padding: 0 8px;
    font-size: $font28;
    font-weight: 500;
    color: $color-primary;
  }
  .search-btn {
    position: relative;
    padding: 0 12px 0 32px;
    font-size: $font28;
    color: $color-text-black;
    &::before {
      position: absolute;
      top: 10%;
      bottom: 10%;
      left: 0;
      width: 1px;
      content: '';
      background: $border-color1;
    }
  }
  &::v-deep {
    .van-field__left-icon {
      color: $color-text-vice;
    }
    .van-search__content--round {
      border-radius: 16px;
    }
    .icon {
      font-size: 48px;
    }
  }
}
</style>
