* {
  box-sizing: border-box;
}
.index {
  position: relative;
  font-size: 36px;
  font-weight: 600;
  color: $color-text-main;
  .main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    padding: 0 66px;
    background: url('~@/assets/images/descript/bg.png') no-repeat 0 0;
    background-size: 100% 100%;
    .title {
      margin-bottom: 90px;
      font-size: 50px;
      color: $color-text-black;
    }
    .text-with-icon {
      padding-left: 76px;
    }
    .icon-stress {
      background: url('~@/assets/images/descript/stress.png') no-repeat left center;
      background-size: 50px 50px;
    }
    .icon-tab {
      background: url('~@/assets/images/descript/tab.png') no-repeat 8px center;
      background-size: 36px 38px;
    }
    .text-mini {
      font-size: 32px;
      font-weight: 400;
    }
    .text-large {
      font-size: 40px;
      color: $color-text-black;
    }
    .text-margin-min {
      margin-bottom: 18px;
    }
    .text-margin-max {
      margin-bottom: 80px;
    }
    .setting {
      margin-left: 20px;
      font-size: 28px;
      font-weight: 400;
      color: $color-primary;
    }
  }
  .bottom-btn {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
  }
}
