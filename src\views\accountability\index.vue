<template>
  <div class="page-index accountability flex-column">
    <van-nav-bar title="责任人" fixed right-text="返回" @click-right="onClickLeft" />
    <div class="main">
      <div class="container">
        <div class="accountability-card" v-for="item in options" :key="item.value">
          <div class="card-title">{{ item.title }}</div>
          <div class="card-content">
            <div class="person-item" v-for="person in item.children" :key="person.value">
              <div class="person-name">{{ person.title }}</div>
              <div class="person-info">
                <section>
                  <span class="info-label">姓名：</span>
                  <span class="info-value">{{ record[person.value + 'Name'] }}</span>
                </section>
                <section>
                  <span class="info-label">单位：</span>
                  <span class="info-value">{{ record[person.value + 'Dept'] }}</span>
                </section>
                <section>
                  <span class="info-label">职务：</span>
                  <span class="info-value">{{ record[person.value + 'Job'] }}</span>
                </section>
                <section>
                  <span class="info-label">手机号：</span>
                  <span class="info-value">{{ record[person.value + 'Phone'] }}</span>
                </section>
              </div>
            </div>
            <div class="person-item-pics">
              <div class="pics-label">坝址附近公示:</div>
              <div class="flex-1 flex pics-container">
                <van-image
                  v-for="(pic, index) in record[item.value + (item.value === 'flood' ? 'DamAttachments' : 'SafelyAttachments')]"
                  :key="pic.id"
                  :src="previewUrl + pic.file_path"
                  @click="
                    previewImage(record[item.value + (item.value === 'flood' ? 'DamAttachments' : 'SafelyAttachments')], index)
                  "
                />
              </div>
            </div>
            <div class="person-item-pics">
              <div class="pics-label">媒体公示:</div>
              <div class="flex-1 flex pics-container">
                <van-image
                  v-for="(pic, index) in record[item.value + 'MediumAttachments']"
                  :key="pic.id"
                  :src="previewUrl + pic.file_path"
                  @click="previewImage(record[item.value + 'MediumAttachments'], index)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { ImagePreview } from 'vant';
import { detailById } from '@/api/accountability';
import { previewUrl } from '@/api/common.js';
export default {
  name: 'Accountability',
  data() {
    const isAndroid = /android/i.test(navigator.userAgent);
    return {
      previewUrl,
      capture: isAndroid ? 'camera' : null,
      options: [
        {
          title: '防汛责任人',
          value: 'flood',
          children: [
            { value: 'A', title: '行政责任人' },
            { value: 'B', title: '技术责任人' },
            { value: 'C', title: '巡查责任人' }
          ]
        },
        {
          title: '大坝安全责任人',
          value: 'dam',
          children: [
            { value: 'D', title: '主管单位责任人' },
            { value: 'E', title: '管理单位责任人' },
            // { value: "F", title: "指挥长" },
            { value: 'G', title: '安全责任人' }
          ]
        }
      ],
      record: {}
    };
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo || getStorage('userInfo')
    })
  },
  methods: {
    onClickLeft() {
      this.$router.back();
    },
    getData() {
      detailById({ wrpcd: this.userInfo.assignWrpcdList?.[0] }).then(res => {
        if (res.status === 200) {
          if (res.data.attWrpPers) {
            this.record = { ...res.data };
            res.data.attWrpPers.forEach(item => {
              const { pername, unit, post, tel, pertp } = item;
              this.record[pertp + 'Name'] = pername;
              this.record[pertp + 'Dept'] = unit;
              this.record[pertp + 'Job'] = post;
              this.record[pertp + 'Phone'] = tel;
            });
          }
        }
      });
    },
    previewImage(attachments, index) {
      ImagePreview({ images: attachments.map(item => previewUrl + item.file_path), startPosition: index });
    }
  },
  created() {
    this.getData();
  }
};
</script>

<style lang="scss" scoped>
@import './index';
</style>
