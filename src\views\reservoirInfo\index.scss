* {
  box-sizing: border-box;
}
.reservoir-info {
  color: $color-text-main;
  .main {
    width: 100%;
    height: 100%;
    padding: 32px;
    background-image: linear-gradient(180deg, #3678e9, #ffffff);
    .container {
      width: 100%;
      height: 100%;
      padding: 32px;
      overflow: auto;
      background-image: linear-gradient(160deg, #cff3ff, #ffffff);
      border-radius: 32px;
      .container-title {
        font-size: 32px;
        font-weight: 500;
        &::before {
          display: inline-block;
          width: 12px;
          height: 30px;
          margin-right: 20px;
          vertical-align: bottom;
          content: '';
          background-color: #3e80ed;
          border-radius: 20px;
        }
      }
      .container-content {
        display: flex;
        flex-wrap: wrap;
        gap: 22px 15px;
        margin-top: 24px;
        section {
          width: calc(50% - 7.5px);
          img {
            width: 28px;
            height: 28px;
            margin-top: 4px;
            margin-right: 20px;
          }
          .desc {
            flex: 1;
            overflow: hidden;
            font-size: 26px;
            font-weight: 500;
            line-height: 40px;
            .label {
              font-weight: bold;
            }
            span {
              width: 100%;
              word-wrap: break-word;
              word-break: break-all;
              white-space: break-spaces;
            }
          }
        }
        .full-width {
          width: 100%;
        }
        .text-ellipsis {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
