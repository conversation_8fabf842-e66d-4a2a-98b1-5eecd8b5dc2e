// 位置上报
import http from '@/utils/apiRequestType';
import apiUrl from '@/utils/apiUrl';

const { defaultUrl } = apiUrl;

/**
 * @description: 运行管理-工程巡查修改
 * @param {object} data
 * @return {object}
 */
export function addTask(data) {
  return http.post(defaultUrl + '/operaEngineeringInspection/saveGcxc', data);
}

/**
 * @description: 运行管理-工程巡查 根据id查询详细信息
 * @param {object} data
 * @return {object}
 */
export function getGcxcDetail(data) {
  return http.post(defaultUrl + '/operaEngineeringInspection/getGcxcDetail', data);
}

// 运行管理-工程巡查删除
export function deleteGcxc(data) {
  return http.post(defaultUrl + '/operaEngineeringInspection/deleteGcxc', data);
}
