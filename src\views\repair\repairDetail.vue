<template>
  <div class="page-index danger-upload">
    <van-nav-bar :title="isAdd ? '维养上报' : curProjName" fixed right-text="返回" @click-right="onClickLeft" />
    <div class="main">
      <van-form ref="formRef" class="form-card-box" :disabled="isDone" :show-error="false">
        <van-field
          v-model="formData.wxyhxm"
          label="维修项目名称"
          type="text"
          placeholder="请输入"
          required
          :rules="[{ required: true, message: '请输入维修项目名称' }]"
          :show-error-message="false"
          input-align="right"
        />
        <van-field
          readonly
          clickable
          name="picker"
          :value="typeOptions.find(item => item.value === formData.wxyhlx)?.label || ''"
          label="维修养护类型"
          placeholder="请选择"
          right-icon="arrow"
          input-align="right"
          required
          :rules="[{ required: true, message: '请选择维修养护类型' }]"
          error-message-align="right"
          :show-error-message="false"
          @click-input="showPicker = true"
        />
        <van-field
          readonly
          clickable
          input-align="right"
          name="sj"
          :value="formData.sj"
          label="时间"
          placeholder="点击选择时间"
          required
          :rules="[{ required: true, message: '请选择' }]"
          error-message-align="right"
          :show-error-message="false"
          @click-input="showTimePopup('sj')"
        />
        <van-field
          name="sfzc"
          label="状态是否正常"
          input-align="right"
          required
          :rules="[{ required: true, message: '请选择状态' }]"
          error-message-align="right"
          :show-error-message="false"
        >
          <template #input>
            <van-radio-group v-model="formData.sfzc" direction="horizontal">
              <van-radio :name="1">是</van-radio>
              <van-radio :name="0">否</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-model="formData.dd"
          label="地点"
          placeholder="请输入"
          input-align="right"
          :required="false"
          :rules="[{ required: false, message: '请输入地点' }]"
          error-message-align="right"
          :show-error-message="false"
        />
        <van-field
          v-model="formData.fzr"
          label="负责人"
          placeholder="请输入"
          input-align="right"
          :required="false"
          :rules="[{ required: false, message: '请输入负责人' }]"
          error-message-align="right"
          :show-error-message="false"
        />

        <van-field name="fjList" label="附件">
          <template #input>
            <CustomUpload v-model="fjList" list-type="text" @input="afterUploadFj" />
          </template>
        </van-field>
        <van-field name="picList" label="维修养护后图片">
          <template #input>
            <CustomUpload v-model="picList" list-type="picture" @input="afterUploadPic" />
          </template>
        </van-field>
        <van-field
          v-model="formData.bz"
          rows="4"
          autosize
          label="备注"
          type="textarea"
          placeholder="请输入"
          show-word-limit
          maxlength="200"
        />
      </van-form>
    </div>
    <BottomBtn confirmText="保存" cancelText="返回" @cancel="onClickLeft" @confirm="onSubmit" />

    <van-popup v-model="showPicker" position="bottom">
      <van-picker show-toolbar value-key="label" :columns="typeOptions" @confirm="onPickerConfirm" @cancel="showPicker = false" />
    </van-popup>
    <van-popup v-model="showTimePicker" position="bottom">
      <van-datetime-picker
        v-model="currentTime"
        type="datetime"
        v-bind="timeProps"
        @confirm="onTimeConfirm"
        @cancel="showTimePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import BottomBtn from '@/components/BottomBtn.vue';
import CustomUpload from '@/components/CustomUpload/index.vue';
import { saveDangerRecord } from '@/api/dangerRecord';
import { getCurrentPosition } from '@/utils/location';
import { saveRepairApi } from '@/api/repair';
import { getStorage } from '@/utils/storage';

export default {
  name: 'RepairDetail',
  components: {
    BottomBtn,
    CustomUpload
  },
  props: {},
  data() {
    const isAndroid = /android/i.test(navigator.userAgent);
    return {
      capture: isAndroid ? 'camera' : null,
      userInfo: {},
      isAdd: false,
      isDone: false,
      formData: {
        id: null,
        wrpcd: '',
        wxyhxm: '',
        wxyhlx: null,
        dd: '',
        sj: '',
        fzr: '',
        sfzc: null,
        fjId: '',
        wxyhhId: '',
        bz: ''
      },
      showPicker: false,
      showTimePicker: false,
      timeType: '',
      timeProps: {},
      currentTime: new Date(),
      typeOptions: [
        { label: '设施检修', value: 1 },
        { label: '设备维护', value: 2 },
        { label: '清淤疏浚', value: 3 },
        { label: '防渗防漏', value: 4 }
      ],
      fjList: [],
      picList: []
    };
  },
  computed: {
    ...mapGetters('common', ['getOptions'])
  },
  watch: {},
  methods: {
    onClickLeft() {
      if (!this.isAdd) {
        this.back();
        return;
      }
      this.$dialog
        .confirm({
          title: '提示',
          message: '上报信息尚未保存，是否确认返回？'
        })
        .then(() => {
          this.back();
        })
        .catch(() => {});
    },
    back() {
      this.$router.go(-1);
    },
    onPickerConfirm(val) {
      this.formData.wxyhlx = val.value;
      this.showPicker = false;
    },
    showTimePopup(type) {
      this.showTimePicker = true;
      this.timeType = type;
      this.currentTime = this.formData[type] ? new Date(+this.$dayjs(this.formData[type])) : new Date();
      // if (type === 'fssj' && this.formData.zgsj) {
      //   this.$set(this.timeProps, 'maxDate', new Date(+this.$dayjs(this.formData.zgsj)));
      //   if (this.timeProps.minDate) {
      //     delete this.timeProps.minDate;
      //   }
      // } else if (type === 'zgsj' && this.formData.fssj) {
      //   this.$set(this.timeProps, 'minDate', new Date(+this.$dayjs(this.formData.fssj)));
      //   if (this.timeProps.maxDate) {
      //     delete this.timeProps.maxDate;
      //   }
      // }
    },
    onTimeConfirm(val) {
      this.formData[this.timeType] = this.$dayjs(val).format('YYYY-MM-DD HH:mm:ss');
      this.showTimePicker = false;
    },
    afterUploadFj(file) {
      console.log(file, 'fj');
      if (file.length) {
        this.formData.fjId = file.map(item => item.id).join(',');
      }
    },
    afterUploadPic(file) {
      console.log(file, 'pic');
      if (file.length) {
        this.formData.wxyhhId = file.map(item => item.id).join(',');
      }
    },
    onSubmit() {
      this.$refs.formRef.validate().then(async valid => {
        const res = await saveRepairApi(this.formData);
        if (res.status === 200) {
          this.$userApp.toast.show({
            text: `上报成功`,
            type: 'text'
          });
          this.$router.back();
        }
      });
    },
    resetForm() {
      this.formData = {
        wrpcd: this.userInfo?.assignWrpcdList[0] || '',
        wxyhxm: '',
        wxyhlx: null,
        dd: '',
        sj: '',
        fzr: '',
        sfzc: null,
        fjId: '',
        wxyhhId: '',
        bz: ''
      };
    }
  },
  created() {
    // 设置所在位置
    // getCurrentPosition(p => {
    //   // this.formData.szwz = p.addresses;
    // });
    this.userInfo = getStorage('userInfo') || {};

    const params = this.$route.params;
    const { type, ...rest } = params;

    this.isAdd = type === 'add';
    if (!this.isAdd) {
      //编辑
      // this.formData = JSON.parse(JSON.stringify({ ...rest }));
      this.fjList = params.fjFileList || [];
      this.picList = params.wxyhhFileList || [];
      this.curProjName = params.wxyhxm || '维养详情';
      this.formData = {
        id: params.id,
        wrpcd: params.wrpcd,
        wxyhxm: params.wxyhxm,
        wxyhlx: params.wxyhlx,
        dd: params.dd,
        sj: params.sj,
        fzr: params.fzr,
        sfzc: params.sfzc,
        fjId: params.fjId,
        wxyhhId: params.wxyhhId,
        bz: params.bz
      };
    } else {
      //新增
      this.resetForm();
    }
  },
  mounted() {}
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.danger-upload {
  padding-bottom: 134px;
  color: $color-text-main;
  background-color: $bg-page1;
  .basic-date {
    padding: 30px 20px;
    font-size: 28px;
    font-weight: 500;
  }
  .main {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 15px 15px 0;
    overflow-y: auto;
    .form-card-box {
      margin-bottom: 14px;
      background: $bg-page;
      border-radius: 30px;
      .input-box {
        &:last-child {
          border-bottom: none;
        }
      }
      &::v-deep {
        .van-cell {
          background: none;
        }
      }
    }
  }
}
.index-readonly {
  padding-bottom: 0;
}
</style>
