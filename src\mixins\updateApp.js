import { Dialog } from 'vant';
export default {
  data() {
    return {
      versionInfo: {},
      updateMessageInfo: ''
    };
  },
  methods: {
    getVersion() {
      this.$appAjax.get(this.$appApiUrl.defaultUrl + '/version/queryVersionInfo').then(res => {
        if (res.status === 200) {
          if (plus.os.name === 'Android') {
            this.versionInfo = res.data.android;
          }
          plus.runtime.getProperty(plus.runtime.appid, appInfo => {
            this.$store.commit('location/setVersion', appInfo.version);
            if (this.versionInfo.version != appInfo.version) {
              this.updateMessageInfo = `发现新版本${this.versionInfo.version || ''}，是否更新？${
                this.versionInfo.updateMsg ? '<br><br>更新说明：<br>' + this.versionInfo.updateMsg : ''
              }`;
              Dialog.confirm({
                title: '版本更新',
                message: this.updateMessageInfo,
                messageAlign: this.versionInfo.updateMsg ? 'left' : 'center',
                confirmButtonText: '立即更新',
                cancelButtonText: '以后再说'
                // showCancelButton: !result.isForce // 强制更新则隐藏取消按钮
              })
                .then(() => {
                  // 全量包
                  if (this.versionInfo.updateType === 'apk') {
                    const appUrl = this.versionInfo.appUrl;
                    plus.runtime.openURL(appUrl);
                  } else if (this.versionInfo.updateType === 'wgt') {
                    // 增量包
                    const wgtUrl = this.versionInfo.wgtUrl;
                    // const wgtUrl = 'http://192.168.228.179:3333/shantou-app.wgt';
                    this.downWgt(wgtUrl);
                  }
                })
                .catch(() => {});
            }
          });
        }
      });
    },
    // 下载wgt文件
    downWgt(wgtUrl) {
      const _this = this;
      if (!wgtUrl) {
        return;
      }
      // plus.nativeUI.showWaiting('发现新版本');
      // plus.nativeUI.closeWaiting();

      plus.nativeUI.showWaiting('正在下载更新文件...');
      plus.downloader
        .createDownload(
          wgtUrl,
          {
            filename: '_doc/update/'
          },
          function(d, status) {
            if (status == 200) {
              _this.installWgt(d.filename); // 安装wgt包
            } else {
              plus.nativeUI.alert('下载更新文件失败！');
            }

            plus.nativeUI.closeWaiting();
          }
        )
        .start();
    },
    // 更新应用资源
    installWgt(path) {
      plus.nativeUI.showWaiting('安装更新文件...');
      plus.runtime.install(
        path,
        {},
        function() {
          plus.nativeUI.closeWaiting();
          plus.nativeUI.alert('更新完成！', function() {
            plus.runtime.restart();
          });
        },
        function(e) {
          plus.nativeUI.closeWaiting();
          plus.nativeUI.alert('安装更新文件失败[' + e.code + ']：' + e.message);
        }
      );
    }
  },
  created() {
    if (process.env.NODE_ENV === 'app') {
      this.getVersion();
    }
  },
  mounted() {}
};
