<template>
  <div class="box-container" :style="domStyle">
    <div
      class="drag-area d-flex flex-row j-center a-center"
      :style="{ height: minHeight + 'px' }"
      @click.stop="changeBoxVisible"
      @touchstart="touchStart"
      @touchmove="touchMoving"
      @touchend="touchEnd"
    >
      <div class="drag-btn"></div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'CustomDragBox',
  comments: {},
  props: {
    maxHeight: {
      type: Number,
      default() {
        return 0;
      }
    },
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isApp: false,
      tranY: 0, // 底部左下角，负数向上，正数向下
      transitionTime: 0,
      startPosition: 0,
      moveDistance: 0,
      rate: 0.3,
      isMoving: false,
      minHeight: 32
    };
  },
  computed: {
    visible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    },
    domStyle() {
      return {
        height: (this.visible ? this.maxHeight - this.tranY : this.minHeight - this.tranY) + 'px',
        transition: this.transitionTime + 's'
      };
    }
  },
  methods: {
    // 切换box显示
    changeBoxVisible() {
      // 判断时候正在滑动
      if (this.isMoving) {
        this.isMoving = false;
        return;
      }
      if (this.visible) {
        this.tranY = 0;
        this.transitionTime = 0.5;
        this.visible = false;
      } else {
        this.tranY = 0;
        this.transitionTime = 0.5;
        this.visible = true;
      }
    },
    // 触摸开始
    touchStart(e) {
      this.startPosition = this.isApp ? e.changedTouches[0].screenY : e.touches[0].clientY;
      this.transitionTime = 0;
      this.moveDistance = 0;
    },
    // 触摸滑动
    touchMoving(e) {
      this.isMoving = true;
      this.moveDistance = (this.isApp ? e.changedTouches[0].screenY : e.touches[0].clientY) - this.startPosition;

      if (this.visible) {
        if (this.moveDistance < 0) {
          // 说明是向上滑，因为已经是显示的，就不管它
        } else {
          // 如果是向下滑
          this.tranY = this.moveDistance;
        }
      } else {
        // 向上滑且滑动大小不超过展示高度
        if (this.moveDistance <= 0 && Math.abs(this.moveDistance) <= this.maxHeight) {
          this.tranY = -Math.abs(this.moveDistance);
        }
      }
    },
    // 触摸结束
    touchEnd() {
      // 滑动最小距离
      const minVal = Math.abs(this.rate * this.maxHeight);
      if (this.moveDistance === 0) {
        return;
      }
      if (this.visible) {
        if (this.moveDistance >= minVal) {
          this.transitionTime = 0.3;
          this.visible = false;
        } else if (this.moveDistance < minVal && this.moveDistance > 0) {
          // 移动距离过小时，恢复原状
          this.transitionTime = 0.3;
          this.visible = true;
        }
      } else {
        if (Math.abs(this.moveDistance) < minVal) {
          this.transitionTime = 0.3;
          this.visible = false;
        } else if (this.moveDistance < 0 && Math.abs(this.moveDistance) >= minVal) {
          // 移动距离过小时，恢复原状
          this.transitionTime = 0.3;
          this.visible = true;
        }
      }
      // 全部初始化为0，tranY只控制滑动的效果
      this.tranY = 0;
    }
  },
  created() {
    this.isApp = process.env.NODE_ENV === 'app';
  }
};
</script>

<style lang="scss" scoped>
.box-container {
  // position: absolute;
  // z-index: 9 !important;
  width: 100%;
  overflow: hidden;

  // background: linear-gradient(to bottom, #1d54ab, #13305d);
  border-radius: 20px 20px 0 0;
  .drag-area {
    padding: 0;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
  }
  .drag-btn {
    width: 80px;
    height: 10px;
    background: #1d1c1c;
    border-radius: 10px;
  }
}
</style>
