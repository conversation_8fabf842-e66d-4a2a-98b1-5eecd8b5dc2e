<template>
  <!-- <van-pull-refresh ref="content" class="content flex-1" v-model="refresh" @refresh="onSearch"> -->
  <van-list class="user-info" v-model="loading" :finished="finished" finished-text="没有更多了">
    <van-cell
      v-for="user in data"
      :key="user.phone"
      :title="user.displayName"
      :label="user.phone"
      value="拨打电话"
      is-link
      @click="call(user.phone)"
    />
  </van-list>
  <!-- </van-pull-refresh> -->
</template>

<script>
import { callPhone } from '@/utils/plusPermission';
export default {
  name: 'UserInfo',
  components: {},
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // userList: [],
      refresh: false,
      loading: false, // 列表是否处于加载状态
      finished: true // 列表是否加载完成
    };
  },
  created() {},
  mounted() {},
  methods: {
    call(phone) {
      if (window.plus) {
        callPhone(phone);
      } else {
        this.$userApp.toast.show({
          text: `应用不支持直接拨打电话`,
          type: 'text'
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.user-info {
  // height: 100%;
  // overflow: auto;
  ::v-deep {
    .van-cell__title {
      font-size: 32px;
    }
    .van-cell__label {
      font-size: 28px;
      color: rgba($color-text-black, 0.6);
    }
    .van-cell__value {
      font-size: 28px;
      color: $color-primary;
    }
    .van-cell__right-icon {
      color: $color-primary;
    }
  }
}
</style>
