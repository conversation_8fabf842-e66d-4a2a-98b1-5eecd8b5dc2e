// 样式重置文件
#app {
  position: relative;
  width: 100%;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: 28px;
  background: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.index {
  height: 100%;
  background: $bg-page;
  &::v-deep {
    .van-nav-bar.van-hairline--bottom::after {
      border-bottom-width: 0;
    }
  }
}
.van-radio .van-radio__icon {
  &.van-radio__icon--disabled .van-icon {
    background-color: #ebedf0;
  }
  .van-icon {
    transition-duration: 0s;
  }
  &:not(.van-radio__icon--checked) .van-icon {
    border: 11px solid #cccccc;
  }
}
.van-button .van-button__content .van-button__text {
  font-weight: 500;
}
.van-button--info {
  background: linear-gradient(180deg, #4193fd 0%, #3f80ed 100%);
}
.van-button--warning {
  background: linear-gradient(180deg, #f56666 0%, #dd5151 100%);
}

// 滚动条重置 scrollbar
::-webkit-scrollbar {
  width: 10px !important;
  height: 10px !important;
}
::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 16px;
  box-shadow: inset 0 0 6px transparent;
}
::-webkit-scrollbar-thumb {
  height: 16px;
  background-color: #c0c0c0;
  border-radius: 16px;
  box-shadow: inset 0 0 6px #ffffff;
}
.c-van-tabs {
  .van-tabs__wrap {
    height: auto !important;
    &::after {
      border-bottom-width: 0;
    }
  }
  .van-tabs__nav {
    box-sizing: border-box;
    padding: 22px 0 37px;
    background-color: #266fe8;
    border-color: #ffffff;
    .van-tab {
      font-size: 32px;
      font-weight: 500;
      color: #ffffff61;
      &--active {
        color: #ffffff;
      }
    }
    .van-tabs__line {
      bottom: 22px;
      background-color: #ffffff;
    }
  }
}
.c-sub-tabs {
  .c-van-tabs .van-tabs__nav {
    background-color: #ffffff;
    border-color: #266fe8;
    .van-tab {
      color: #333333;
    }
    .van-tab--active {
      color: #266fe8;
    }
    .van-tabs__line {
      background-color: #266fe8;
    }
  }
}
