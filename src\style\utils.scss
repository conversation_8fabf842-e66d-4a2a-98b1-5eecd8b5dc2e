/*
例：设置外边距为4px .m4  右外边距为4px .mr4

1366X768
*/

@function vm($size) {
  @return ($size/1920) * 100vw;
}

@function vv($size) {
  @return ($size/960) * 100vh;
}

@mixin four-direction-0 {
  inset: 0;
}

/* flex */

/* horizontal vertical center */
@mixin flexHVC {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* horizontal center */
@mixin flexHC {
  display: flex;
  justify-content: center;
}

/* vertical center */
@mixin flexVC {
  display: flex;
  align-items: center;
}

/* position */

/* absolute-center 绝对定位元素居中 */
@mixin position-a-hvc {
  position: absolute;

  @include four-direction-0;

  margin: auto;
}

/* top right bottom left = 0 */
@mixin position-a-trbl0 {
  position: absolute;

  @include four-direction-0;
}

@mixin position-trbl0 {
  @include four-direction-0;
}

/* width === height */
@mixin width-h($size) {
  width: $size;
  height: $size;
}

/* need width size 单行文本溢出省略号，加元素宽度 */
@mixin ellipsisText {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本溢出省略号 因使用了WebKit的CSS扩展属性，该方法适用于WebKit浏览器及移动端； */
@mixin ellipsisTexts($lines) {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}

// utils 工具
// 颜色
.bg {
  &-red {
    background-color: $color-red !important;
  }
  &-purple {
    background-color: $color-purple !important;
  }
  &-orange {
    background-color: $color-orange !important;
  }
  &-blue {
    background-color: $color-blue !important;
  }
  &-white {
    background: #ffffff !important;
  }
  &-black {
    background: #000000 !important;
  }
}
.color {
  &-red {
    color: $color-red !important;
  }
  &-purple {
    color: $color-purple !important;
  }
  &-orange {
    color: $color-orange !important;
  }
  &-blue {
    color: $color-blue !important;
  }
  &-black {
    color: #000000;
  }
  &-dark {
    color: #333333;
  }
  &-deep {
    color: #555555;
  }
  &-weak {
    color: #b3b3b3;
  }
  &-white {
    color: #ffffff;
  }
}

// 溢出隐藏
.ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  // 多行
  &2 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  &4 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
  }
  &6 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
  }
  &8 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 8;
    -webkit-box-orient: vertical;
  }
  &10 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 10;
    -webkit-box-orient: vertical;
  }
}

// 字体
.font {
  &10 {
    font-size: 10px;
  }
  &12 {
    font-size: 12px;
  }
  &14 {
    font-size: 14px;
  }
  &16 {
    font-size: 16px;
  }
  &18 {
    font-size: 18px;
  }
  &20 {
    font-size: 20px;
  }
  &22 {
    font-size: 22px;
  }
  &24 {
    font-size: 24px;
  }
  &26 {
    font-size: 26px;
  }
  &28 {
    font-size: 28px;
  }
  &30 {
    font-size: 30px;
  }
  &32 {
    font-size: 32px;
  }
  &34 {
    font-size: 34px;
  }
}
.text {
  //text-align
  &-center {
    text-align: center;
  }
  &-left {
    text-align: left;
  }
  &-right {
    text-align: right;
  }
}

// 文字对齐
.align {
  &-l {
    text-align: left;
  }
  &-c {
    text-align: center;
  }
  &-r {
    text-align: right;
  }
}

// 浮动与清除浮动
.float {
  &-l {
    float: left;
  }
  &-r {
    float: right;
  }
}
.clearfix {
  // 引入了zoom以支持IE6/7
  // 同时加入:before以解决现代浏览器上边距折叠的问题
  *zoom: 1;
  &::before,
  &::after {
    display: table;
    content: ' ';
  }
  &::after {
    clear: both;
  }
}

// 显示
.display {
  &-n {
    display: none;
  }
  &-i {
    display: inline;
  }
  &-b {
    display: block;
  }
  &-ib {
    display: inline-block;
  }
  &-t {
    display: table;
  }
}
div.display-ib {
  *display: inline;
  *zoom: 1;
}
.v {
  &m {
    vertical-align: middle;
  }
  &ib {
    display: inline-block;
    vertical-align: middle;
  }
}

// 定位
.position {
  &-r {
    position: relative;
  }
  &-a {
    position: absolute;
  }
  &-f {
    position: fixed;
  }
  &-a-hvc {
    position: absolute;

    @include four-direction-0;

    margin: auto;
  }
  &-a-trbl0 {
    position: absolute;

    @include four-direction-0;
  }
  &-trbl0 {
    @include four-direction-0;
  }
}

//distance
.top {
  &0 {
    top: 0;
  }
  &2 {
    top: 2px;
  }
  &4 {
    top: 4px;
  }
  &6 {
    top: 6px;
  }
  &8 {
    top: 8px;
  }
  &10 {
    top: 10px;
  }
  &12 {
    top: 12px;
  }
  &14 {
    top: 14px;
  }
  &16 {
    top: 16px;
  }
  &18 {
    top: 18px;
  }
  &20 {
    top: 20px;
  }
  &22 {
    top: 22px;
  }
  &24 {
    top: 24px;
  }
  &26 {
    top: 26px;
  }
  &28 {
    top: 28px;
  }
  &30 {
    top: 30px;
  }
}
.right {
  &0 {
    right: 0;
  }
  &2 {
    right: 2px;
  }
  &4 {
    right: 4px;
  }
  &6 {
    right: 6px;
  }
  &8 {
    right: 8px;
  }
  &10 {
    right: 10px;
  }
  &12 {
    right: 12px;
  }
  &14 {
    right: 14px;
  }
  &16 {
    right: 16px;
  }
  &18 {
    right: 18px;
  }
  &20 {
    right: 20px;
  }
  &22 {
    right: 22px;
  }
  &24 {
    right: 24px;
  }
  &26 {
    right: 26px;
  }
  &28 {
    right: 28px;
  }
  &30 {
    right: 30px;
  }
}
.bottom {
  &0 {
    bottom: 0;
  }
  &2 {
    bottom: 2px;
  }
  &4 {
    bottom: 4px;
  }
  &6 {
    bottom: 6px;
  }
  &8 {
    bottom: 8px;
  }
  &10 {
    bottom: 10px;
  }
  &12 {
    bottom: 12px;
  }
  &14 {
    bottom: 14px;
  }
  &16 {
    bottom: 16px;
  }
  &18 {
    bottom: 18px;
  }
  &20 {
    bottom: 20px;
  }
  &22 {
    bottom: 22px;
  }
  &24 {
    bottom: 24px;
  }
  &26 {
    bottom: 26px;
  }
  &28 {
    bottom: 28px;
  }
  &30 {
    bottom: 30px;
  }
}
.left {
  &0 {
    left: 0;
  }
  &2 {
    left: 2px;
  }
  &4 {
    left: 4px;
  }
  &6 {
    left: 6px;
  }
  &8 {
    left: 8px;
  }
  &10 {
    left: 10px;
  }
  &12 {
    left: 12px;
  }
  &14 {
    left: 14px;
  }
  &16 {
    left: 16px;
  }
  &18 {
    left: 18px;
  }
  &20 {
    left: 20px;
  }
  &22 {
    left: 22px;
  }
  &24 {
    left: 24px;
  }
  &26 {
    left: 26px;
  }
  &28 {
    left: 28px;
  }
  &30 {
    left: 30px;
  }
}

// flex布局
.flex {
  display: flex;
  &-1 {
    flex: 1;
  }
  &-column {
    display: flex;
    flex-direction: column;
  }
  &-row {
    display: flex;
    flex-direction: row;
  }
  &-wrap {
    flex-wrap: wrap;
  }
  &-nowrap {
    flex-wrap: nowrap;
  }
  &-shrink {
    flex-shrink: 0;
  }
  &-grow {
    flex-grow: 1;
  }
  &-hvc {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &-hc {
    display: flex;
    justify-content: center;
  }
  &-vc {
    display: flex;
    align-items: center;
  }
}

//justify
.j {
  &-start {
    justify-content: start;
  }
  &-center {
    justify-content: center;
  }
  &-end {
    justify-content: flex-end;
  }
  &-sb {
    justify-content: space-between;
  }
  &-sa {
    justify-content: space-around;
  }
}

//align
.a {
  &-center {
    align-items: center;
  }
  &-start {
    align-items: flex-start;
  }
  &-end {
    align-items: flex-end;
  }
  &-stretch {
    align-items: stretch;
  }
  &-self {
    &-start {
      align-self: flex-start;
    }
    &-auto {
      align-self: auto;
    }
    &-end {
      align-self: flex-end;
    }
    &-stretch {
      align-self: stretch;
    }
    &-baseline {
      align-self: baseline;
    }
  }
}

// 盒子模型
// margin
.m {
  &0 {
    margin: 0;
  }
  &t0 {
    margin-top: 0;
  }
  &r0 {
    margin-right: 0;
  }
  &b0 {
    margin-bottom: 0;
  }
  &l0 {
    margin-left: 0;
  }
  &2 {
    margin: 2px;
  }
  &rl2 {
    margin: 0 2px;
  }
  &tb2 {
    margin: 2px 0;
  }
  &t2 {
    margin-top: 2px;
  }
  &r2 {
    margin-right: 2px;
  }
  &b2 {
    margin-bottom: 2px;
  }
  &l2 {
    margin-left: 2px;
  }
  &4 {
    margin: 4px;
  }
  &rl4 {
    margin: 0 4px;
  }
  &tb4 {
    margin: 4px 0;
  }
  &t4 {
    margin-top: 4px;
  }
  &r4 {
    margin-right: 4px;
  }
  &b4 {
    margin-bottom: 4px;
  }
  &l4 {
    margin-left: 4px;
  }
  &6 {
    margin: 6px;
  }
  &rl6 {
    margin: 0 6px;
  }
  &tb6 {
    margin: 6px 0;
  }
  &t6 {
    margin-top: 6px;
  }
  &r6 {
    margin-right: 6px;
  }
  &b6 {
    margin-bottom: 6px;
  }
  &l6 {
    margin-left: 6px;
  }
  &8 {
    margin: 8px;
  }
  &rl8 {
    margin: 0 8px;
  }
  &tb8 {
    margin: 8px 0;
  }
  &t8 {
    margin-top: 8px;
  }
  &r8 {
    margin-right: 8px;
  }
  &b8 {
    margin-bottom: 8px;
  }
  &l8 {
    margin-left: 8px;
  }
  &10 {
    margin: 10px;
  }
  &rl10 {
    margin: 0 10px;
  }
  &tb10 {
    margin: 10px 0;
  }
  &t10 {
    margin-top: 10px;
  }
  &r10 {
    margin-right: 10px;
  }
  &b10 {
    margin-bottom: 10px;
  }
  &l10 {
    margin-left: 10px;
  }
  &12 {
    margin: 12px;
  }
  &rl12 {
    margin: 0 12px;
  }
  &tb12 {
    margin: 12px 0;
  }
  &l12 {
    margin-left: 12px;
  }
  &r12 {
    margin-right: 12px;
  }
  &t12 {
    margin-top: 12px;
  }
  &b12 {
    margin-bottom: 12px;
  }
  &14 {
    margin: 14px;
  }
  &rl14 {
    margin: 0 14px;
  }
  &tb14 {
    margin: 14px 0;
  }
  &l14 {
    margin-left: 14px;
  }
  &r14 {
    margin-right: 14px;
  }
  &t14 {
    margin-top: 14px;
  }
  &b14 {
    margin-bottom: 14px;
  }
  &16 {
    margin: 16px;
  }
  &rl16 {
    margin: 0 16px;
  }
  &tb16 {
    margin: 16px 0;
  }
  &l16 {
    margin-left: 16px;
  }
  &r16 {
    margin-right: 16px;
  }
  &t16 {
    margin-top: 16px;
  }
  &b16 {
    margin-bottom: 16px;
  }
  &18 {
    margin: 18px;
  }
  &rl18 {
    margin: 0 18px;
  }
  &tb18 {
    margin: 18px 0;
  }
  &l18 {
    margin-left: 18px;
  }
  &r18 {
    margin-right: 18px;
  }
  &t18 {
    margin-top: 18px;
  }
  &b18 {
    margin-bottom: 18px;
  }
  &20 {
    margin: 20px;
  }
  &rl20 {
    margin: 0 20px;
  }
  &tb20 {
    margin: 20px 0;
  }
  &l20 {
    margin-left: 20px;
  }
  &r20 {
    margin-right: 20px;
  }
  &t20 {
    margin-top: 20px;
  }
  &b20 {
    margin-bottom: 20px;
  }
  &22 {
    margin: 22px;
  }
  &rl22 {
    margin: 0 22px;
  }
  &tb22 {
    margin: 22px 0;
  }
  &l22 {
    margin-left: 22px;
  }
  &r22 {
    margin-right: 22px;
  }
  &t22 {
    margin-top: 22px;
  }
  &b22 {
    margin-bottom: 22px;
  }
  &24 {
    margin: 24px;
  }
  &rl24 {
    margin: 0 24px;
  }
  &tb24 {
    margin: 24px 0;
  }
  &l24 {
    margin-left: 24px;
  }
  &r24 {
    margin-right: 24px;
  }
  &t24 {
    margin-top: 24px;
  }
  &b24 {
    margin-bottom: 24px;
  }
  &26 {
    margin: 26px;
  }
  &rl26 {
    margin: 0 26px;
  }
  &tb26 {
    margin: 26px 0;
  }
  &l26 {
    margin-left: 26px;
  }
  &r26 {
    margin-right: 26px;
  }
  &t26 {
    margin-top: 26px;
  }
  &b26 {
    margin-bottom: 26px;
  }
  &28 {
    margin: 28px;
  }
  &rl28 {
    margin: 0 28px;
  }
  &tb28 {
    margin: 28px 0;
  }
  &l28 {
    margin-left: 28px;
  }
  &r28 {
    margin-right: 28px;
  }
  &t28 {
    margin-top: 28px;
  }
  &b28 {
    margin-bottom: 28px;
  }
  &30 {
    margin: 30px;
  }
  &rl30 {
    margin: 0 30px;
  }
  &tb30 {
    margin: 30px 0;
  }
  &l30 {
    margin-left: 30px;
  }
  &r30 {
    margin-right: 30px;
  }
  &t30 {
    margin-top: 30px;
  }
  &b30 {
    margin-bottom: 30px;
  }
  &t32 {
    margin-top: 32px;
  }
}

// padding
.p {
  &0 {
    padding: 0;
  }
  &t0 {
    padding-top: 0;
  }
  &r0 {
    padding-right: 0;
  }
  &b0 {
    padding-bottom: 0;
  }
  &l0 {
    padding-left: 0;
  }
  &2 {
    padding: 2px;
  }
  &rl2 {
    padding: 0 2px;
  }
  &tb2 {
    padding: 2px 0;
  }
  &t2 {
    padding-top: 2px;
  }
  &r2 {
    padding-right: 2px;
  }
  &b2 {
    padding-bottom: 2px;
  }
  &l2 {
    padding-left: 2px;
  }
  &4 {
    padding: 4px;
  }
  &rl4 {
    padding: 0 4px;
  }
  &tb4 {
    padding: 4px 0;
  }
  &t4 {
    padding-top: 4px;
  }
  &r4 {
    padding-right: 4px;
  }
  &b4 {
    padding-bottom: 4px;
  }
  &l4 {
    padding-left: 4px;
  }
  &6 {
    padding: 6px;
  }
  &rl6 {
    padding: 0 6px;
  }
  &tb6 {
    padding: 6px 0;
  }
  &t6 {
    padding-top: 6px;
  }
  &r6 {
    padding-right: 6px;
  }
  &b6 {
    padding-bottom: 6px;
  }
  &l6 {
    padding-left: 6px;
  }
  &8 {
    padding: 8px;
  }
  &rl8 {
    padding: 0 8px;
  }
  &tb8 {
    padding: 8px 0;
  }
  &t8 {
    padding-top: 8px;
  }
  &r8 {
    padding-right: 8px;
  }
  &b8 {
    padding-bottom: 8px;
  }
  &l8 {
    padding-left: 8px;
  }
  &10 {
    padding: 10px;
  }
  &rl10 {
    padding: 0 10px;
  }
  &tb10 {
    padding: 10px 0;
  }
  &t10 {
    padding-top: 10px;
  }
  &r10 {
    padding-right: 10px;
  }
  &b10 {
    padding-bottom: 10px;
  }
  &l10 {
    padding-left: 10px;
  }
  &12 {
    padding: 12px;
  }
  &rl12 {
    padding: 0 12px;
  }
  &tb12 {
    padding: 12px 0;
  }
  &l12 {
    padding-left: 12px;
  }
  &r12 {
    padding-right: 12px;
  }
  &t12 {
    padding-top: 12px;
  }
  &b12 {
    padding-bottom: 12px;
  }
  &14 {
    padding: 14px;
  }
  &rl14 {
    padding: 0 14px;
  }
  &tb14 {
    padding: 14px 0;
  }
  &l14 {
    padding-left: 14px;
  }
  &r14 {
    padding-right: 14px;
  }
  &t14 {
    padding-top: 14px;
  }
  &b14 {
    padding-bottom: 14px;
  }
  &16 {
    padding: 16px;
  }
  &rl16 {
    padding: 0 16px;
  }
  &tb16 {
    padding: 16px 0;
  }
  &l16 {
    padding-left: 16px;
  }
  &r16 {
    padding-right: 16px;
  }
  &t16 {
    padding-top: 16px;
  }
  &b16 {
    padding-bottom: 16px;
  }
  &18 {
    padding: 18px;
  }
  &rl18 {
    padding: 0 18px;
  }
  &tb18 {
    padding: 18px 0;
  }
  &l18 {
    padding-left: 18px;
  }
  &r18 {
    padding-right: 18px;
  }
  &t18 {
    padding-top: 18px;
  }
  &b18 {
    padding-bottom: 18px;
  }
  &20 {
    padding: 20px;
  }
  &rl20 {
    padding: 0 20px;
  }
  &tb20 {
    padding: 20px 0;
  }
  &l20 {
    padding-left: 20px;
  }
  &r20 {
    padding-right: 20px;
  }
  &t20 {
    padding-top: 20px;
  }
  &b20 {
    padding-bottom: 20px;
  }
  &22 {
    padding: 22px;
  }
  &rl22 {
    padding: 0 22px;
  }
  &tb22 {
    padding: 22px 0;
  }
  &l22 {
    padding-left: 22px;
  }
  &r22 {
    padding-right: 22px;
  }
  &t22 {
    padding-top: 22px;
  }
  &b22 {
    padding-bottom: 22px;
  }
  &24 {
    padding: 24px;
  }
  &rl24 {
    padding: 0 24px;
  }
  &tb24 {
    padding: 24px 0;
  }
  &l24 {
    padding-left: 24px;
  }
  &r24 {
    padding-right: 24px;
  }
  &t24 {
    padding-top: 24px;
  }
  &b24 {
    padding-bottom: 24px;
  }
  &26 {
    padding: 26px;
  }
  &rl26 {
    padding: 0 26px;
  }
  &tb26 {
    padding: 26px 0;
  }
  &l26 {
    padding-left: 26px;
  }
  &r26 {
    padding-right: 26px;
  }
  &t26 {
    padding-top: 26px;
  }
  &b26 {
    padding-bottom: 26px;
  }
  &28 {
    padding: 28px;
  }
  &rl28 {
    padding: 0 28px;
  }
  &tb28 {
    padding: 28px 0;
  }
  &l28 {
    padding-left: 28px;
  }
  &r28 {
    padding-right: 28px;
  }
  &t28 {
    padding-top: 28px;
  }
  &b28 {
    padding-bottom: 28px;
  }
  &30 {
    padding: 30px;
  }
  &rl30 {
    padding: 0 30px;
  }
  &tb30 {
    padding: 30px 0;
  }
  &l30 {
    padding-left: 30px;
  }
  &r30 {
    padding-right: 30px;
  }
  &t30 {
    padding-top: 30px;
  }
  &b30 {
    padding-bottom: 30px;
  }
  &32 {
    padding: 32px;
  }
}
.b {
  &b {
    box-sizing: border-box;
  }
  &c {
    box-sizing: content-box;
  }
}
.full {
  &-all {
    width: 100%;
    height: 100%;
  }
  &-w {
    width: 100%;
  }
  &-h {
    height: 100%;
  }
}
.c-p {
  cursor: pointer;
}

// 按钮禁用
.disabled-btn {
  -ms-pointer-events: none;
  pointer-events: none;
  cursor: default !important;
  filter: alpha(opacity=40);
  outline: 0 none;
  opacity: 0.4;
}

// 置灰
.gray {
  text-align: center;

  /* grayscale(val):val值越大灰度就越深 */
  filter: grayscale(100%);
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}
