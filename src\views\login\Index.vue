<template>
  <div class="index">
    <div class="main">
      <div class="login-header">
        <div class="logo">
          <img class="logo-icon" src="@/assets/images/index/logo.png" alt="" />
        </div>
        <div class="name">青山嘴水库矩阵管理系统</div>
      </div>
      <!-- 登录表单 -->
      <van-form ref="loginFormRef" key="login" @submit="login">
        <van-field
          v-model.trim="username"
          name="username"
          class="field-item"
          label-width="5vw"
          left-icon="contact"
          clearable
          label="|"
          placeholder="请输入用户名"
          :rules="[{ required: true }]"
        >
        </van-field>
        <van-field
          v-model.trim="password"
          name="password"
          class="field-item"
          label-width="5vw"
          left-icon="bag-o"
          right-icon="eye-o"
          :type="pswType ? 'password' : 'text'"
          clearable
          label="|"
          placeholder="请输入密码"
          :rules="[{ required: true }]"
          @click-right-icon="pswType = !pswType"
        >
        </van-field>
        <van-field
          name="captcha"
          class="field-item"
          label-width="5vw"
          left-icon="coupon-o"
          v-model.trim="captcha"
          clearable
          label="|"
          placeholder="请输入验证码"
          :rules="[
            { required: true },
            { validator: value => value === this.captchaNumber, message: '验证码错误', trigger: 'blur' }
          ]"
        >
          <template #right-icon>
            <img class="captcha-img" :src="captchaUrl" @click="refreshIdentifyCode" alt="验证码" />
          </template>
        </van-field>
        <van-button class="btns" :round="true" type="info" native-type="submit">登录</van-button>
      </van-form>
    </div>
  </div>
</template>

<script>
import { setStorage, getStorage } from '@/utils/storage';
import { getCaptcha } from '@/api/user';
export default {
  components: {},
  data() {
    return {
      username: '',
      password: '',
      captcha: '',

      pswType: true,
      currentCodeId: '',
      captchaUrl: '', // 验证码地址
      captchaNumber: ''
    };
  },
  computed: {
    // isIos() {
    //   const u = window.navigator.userAgent;
    //   //判断ios
    //   return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    // }
  },
  methods: {
    toPage(item) {
      let { path, ...rest } = item;
      if (path) {
        this.$router.push({
          path,
          query: { ...rest }
        });
      }
    },
    refreshIdentifyCode() {
      this.captcha = '';
      this.getIdentCodeApi();
    },
    // 获取图形验证码
    getIdentCodeApi() {
      const fooArray = new Uint32Array(1);
      this.currentCodeId = crypto.getRandomValues(fooArray)[0] + new Date().getTime();
      getCaptcha({
        codeId: this.currentCodeId
      })
        .then(res => {
          if (res.status === 200) {
            this.captchaUrl = res.data.imageBase64;
            this.captchaNumber = res.data.code;
          }
        })
        .catch(() => {
          this.$notify({ type: 'warning', message: '获取验证码失败,请重新再试' });
        });
    },
    // 登录
    login() {
      let data = {
        username: this.username,
        password: this.password,
        captcha: this.captcha,
        codeId: this.currentCodeId
      };
      let result = this.$store.dispatch('user/login', data);
      result.then(async res => {
        if (res.status === 200) {
          // 记住账号密码
          setStorage('username', btoa(this.username));
          setStorage('password', btoa(this.password));
          this.username = '';
          this.password = '';
          // this.$store.commit('location/resetLocation');
          this.$userApp.toast.show({
            text: '登录成功',
            type: 'text',
            time: 500
          });
          await this.$store.dispatch('user/getUserInfo');
          this.toPage({ path: '/' });
        } else {
          this.$userApp.toast.show({
            text: res.message || '登录失败',
            type: 'text'
          });
          this.captcha = '';
          this.getIdentCodeApi();
        }
      });
    },

    reset() {
      this.username = '';
      this.password = '';
      this.captcha = '';
    }
  },
  created() {},
  mounted() {
    this.getIdentCodeApi();
    // 记住账号密码
    const username = getStorage('username');
    const password = getStorage('password');
    this.username = username ? atob(username) : '';
    this.password = password ? atob(password) : '';
  }
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.index {
  color: $color-text-main;
  .main {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 50px 40px 0;
    background-color: rgb(186 218 242);
    .login-header {
      .logo {
        display: flex;
        justify-content: center;
        .logo-icon {
          width: 210px;
          height: 210px;
          border-radius: 50%;
        }
      }
      .name {
        margin-top: 20px;
        font-size: 40px;
        line-height: 56px;
        color: $color-primary;
        text-align: center;
      }
    }
    .field-item {
      min-height: 96px;
      margin-top: 40px;
      line-height: 48px;
      border: 2px solid #eeeeee;
      border-radius: 42px;
      ::v-deep {
        .van-field__left-icon {
          color: $color-text-gray1;
        }
        .van-field__label {
          color: #eeeeee;
          text-align: center;
        }
      }
    }
    .field-item-no {
      background-color: transparent;
    }
    .captcha-img {
      width: 120px;
      height: 100%;
    }
    ::v-deep {
      .van-cell::after {
        border-bottom: none;
      }
    }
    ::v-deep .van-checkbox {
      padding-left: 20px;
      margin-top: 20px;
      font-size: 24px;
    }
    .btns {
      gap: 26px;
      width: 100%;
      margin-top: 36px;
    }
  }
}
</style>
