<template>
  <div class="section">
    <van-tabs class="section-box flex-column" v-model="activeTab" swipeable animated>
      <van-tab v-for="item in tabList" :key="item.prop" :name="item.prop" :title="item.label">
        <CustomSearch v-model="searchForm.xjName" show-action @search="onSearch" @cancel="showFilterPopup = true" />

        <van-pull-refresh v-if="activeTab === 1" ref="content" class="content" v-model="refresh" @refresh="onSearch">
          <van-list class="content-list" v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <!-- <TaskReservoirCell
              v-for="data in dataList"
              :key="data.tid"
              :data="data"
              @click.native="toPage({ name: 'TaskDetail', taskId: data.tid })"
            /> -->
            <TaskDefaultCell
              v-for="data in dataList"
              :key="data.tid"
              :data="data"
              @delete="deleteTask(data)"
              @click.native="toDetail(data)"
            />
          </van-list>
        </van-pull-refresh>
        <van-pull-refresh v-else ref="content" class="content" v-model="refresh" @refresh="onSearch">
          <van-list class="content-list" v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
            <TaskDefaultCell
              v-for="data in dataList"
              :key="data.tid"
              :data="data"
              @delete="deleteTask(data)"
              @click.native="toDetail(data)"
            />
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>

    <van-popup v-model="showFilterPopup" position="right" :style="{ height: '100%', width: '60%' }">
      <TaskFilter
        :type="activeTab"
        v-model="searchForm"
        @on-choose-time="chooseTime"
        @on-confirm="confirmFilterEvent"
        @on-cancel="showFilterPopup = false"
      />
    </van-popup>

    <TimeSelect
      v-model="showTimePopup"
      type="date"
      format-string="YYYY-MM-DD"
      :time="searchForm[timeKey]"
      @confirm="confirmTimeEvent"
    />

    <van-button class="plus-btn" icon="plus" type="info" round @click="toPage({ name: 'TaskAdd', projectType: activeTab })" />
  </div>
</template>

<script>
import CustomSearch from '@/components/CustomSearch';
import TaskDefaultCell from './taskDefaultCell.vue';
import TaskReservoirCell from './taskReservoirCell.vue';
import TaskFilter from './taskFilter.vue';
import TimeSelect from '@/views/earlyWarning/components/TimeSelect.vue';
import { getYhStnList, delTaskById } from '@/api/patrolManage/patrolTask/index.js';
import { getStorage, removeStorage } from '@/utils/storage';
import { mapState } from 'vuex';
export default {
  name: 'TaskList',
  components: {
    CustomSearch,
    TaskDefaultCell,
    TaskReservoirCell,
    TaskFilter,
    TimeSelect
  },
  props: {},
  data() {
    const defaultSearchForm = {
      xjName: null,
      xjStatus: null,
      xjType: null,
      beginTime: null,
      endTime: null,
      yh: null,
      pageNum: 1,
      pageSize: 10,
      total: 0
    };
    return {
      activeTab: 2,
      tabList: [
        {
          label: '水闸',
          prop: 2
        },
        {
          label: '堤防',
          prop: 3
        },
        {
          label: '泵站',
          prop: 4
        },
        {
          label: '水库',
          prop: 1
        }
      ],
      sluiceSearchForm: { ...defaultSearchForm },
      pumpSearchForm: { ...defaultSearchForm },
      dykeSearchForm: { ...defaultSearchForm },
      reservoirSearchForm: { ...defaultSearchForm },
      sluiceList: [],
      pumpList: [],
      dykeList: [],
      reservoirList: [],
      showFilterPopup: false,
      showTimePopup: false,
      timeKey: 'beginTime',
      refresh: false,
      loading: false, // 列表是否处于加载状态
      finished: true // 列表是否加载完成
    };
  },
  computed: {
    searchForm() {
      let form = {};
      switch (this.activeTab) {
        case 1:
          form = this.reservoirSearchForm;
          break;
        case 2:
          form = this.sluiceSearchForm;
          break;
        case 3:
          form = this.dykeSearchForm;
          break;
        case 4:
          form = this.pumpSearchForm;
          break;
      }

      return form;
    },
    dataList() {
      let list = [];
      switch (this.activeTab) {
        case 1:
          list = this.reservoirList;
          break;
        case 2:
          list = this.sluiceList;
          break;
        case 3:
          list = this.dykeList;
          break;
        case 4:
          list = this.pumpList;
          break;
      }

      return list;
    },
    ...mapState('patrol', {
      patrolTaskKey: state => state.patrolTaskKey,
      patrolInfoKey: state => state.patrolInfoKey
    })
  },
  watch: {
    activeTab: {
      handler() {
        if (!this.dataList.length) {
          this.loadData();
        }
      },
      immediate: true
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 搜索事件
    onSearch() {
      switch (this.activeTab) {
        case 1:
          this.reservoirList = [];
          break;
        case 2:
          this.sluiceList = [];
          break;
        case 3:
          this.dykeList = [];
          break;
        case 4:
          this.pumpList = [];
          break;
      }
      this.searchForm.pageNum = 1;
      this.searchForm.total = 0;
      this.loadData();
    },
    // 加载列表
    onLoad() {
      if (this.dataList.length >= this.searchForm.total) {
        this.finished = true;
      } else {
        this.searchForm.pageNum += 1;
        this.loadData();
      }
    },
    loadData() {
      getYhStnList({
        projectType: this.activeTab,
        ...this.searchForm
      }).then(res => {
        if (res.status === 200 && res.data) {
          const { total, list } = res.data;
          switch (this.activeTab) {
            case 1:
              this.reservoirList.push(...list);
              this.reservoirSearchForm.total = total;
              break;
            case 2:
              this.sluiceList.push(...list);
              this.sluiceSearchForm.total = total;
              break;
            case 3:
              this.dykeList.push(...list);
              this.dykeSearchForm.total = total;
              break;
            case 4:
              this.pumpList.push(...list);
              this.pumpSearchForm.total = total;
              break;
          }
        }

        if (this.dataList.length >= this.searchForm.total) {
          this.finished = true;
        } else {
          this.finished = false;
        }
        this.loading = false;
        this.refresh = false;
      });
    },
    deleteTask(data) {
      delTaskById([data.tid]).then(res => {
        if (res.status === 200) {
          // this.loadData();
          const index = this.dataList.findIndex(i => i.tid === data.tid);
          if (index !== -1) {
            this.dataList.splice(index, 1);
            this.searchForm.total -= 1;
          }
          if (data.xjStatus !== 2) {
            const patrolTaskStorage = getStorage(this.patrolTaskKey) || {};
            if (patrolTaskStorage.id === data.tid) {
              // 进行中的任务清掉暂存数据
              this.$store.commit('patrol/resetPatrol');
            }
          }
        } else {
          this.$userApp.toast.show({
            text: res.message,
            type: 'text'
          });
        }
      });
    },
    chooseTime(key) {
      this.timeKey = key;
      this.showTimePopup = true;
    },
    confirmTimeEvent(value) {
      this.$set(this.searchForm, this.timeKey, value);
      const { beginTime, endTime } = this.searchForm;
      if (beginTime && endTime) {
        if (this.$dayjs(beginTime).diff(endTime) > 0) {
          this.$set(this.searchForm, 'beginTime', endTime);
          this.$set(this.searchForm, 'endTime', beginTime);
        }
      }
    },
    confirmFilterEvent() {
      this.showFilterPopup = false;
      this.onSearch();
    },
    toDetail(data) {
      const patrolTaskStorage = getStorage(this.patrolTaskKey) || {};
      let name = 'TaskDetail';
      if (data.xjStatus !== 2 && patrolTaskStorage.id === data.tid) {
        name = 'TaskOn';
      }

      this.toPage({ name, taskId: data.tid });
    },
    toPage(item) {
      const { name, ...rest } = item;
      if (name) {
        this.$router.push({
          name,
          query: { ...rest }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.section {
  position: relative;
  height: 100%;
  overflow: hidden;
  .plus-btn {
    position: absolute;
    right: 32px;
    bottom: 54px;
    width: 96px;
    height: 96px;
  }
}
.section-box {
  height: 100%;
  &::v-deep .van-tabs__content {
    flex: 1;
    .van-tab__pane {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
}
.content {
  flex: 1;
  overflow: auto;
  background: $bg-page-gray;
  -webkit-overflow-scrolling: touch;
  .content-list {
    padding: 32px 32px 0;
  }
}
</style>
