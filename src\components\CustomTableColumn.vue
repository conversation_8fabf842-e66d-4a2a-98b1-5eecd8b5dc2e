<template>
  <van-row v-if="col.children" class="row-group flex-column">
    <div class="text-center group-title item-cell" :class="{ 'flex-1 flex-hvc': bisect }">{{ col.title }}</div>
    <div class="flex-vc flex-1 a-stretch">
      <CustomTableColumn v-for="item in col.children" bisect :col="item" :key="item.key"></CustomTableColumn>
    </div>
  </van-row>
  <van-col
    v-else
    class="item-cell flex-hvc"
    :style="setColStyle(col.width)"
    :class="{
      'align-center': col.align === 'center'
    }"
  >
    <!-- <div v-if="col.colSlot" :class="{ 'primary-col': colKey === primaryIdx }">
      <slot :data="{ row }" :index="rowKey" :node="col" :name="col.colSlot"></slot>
    </div> -->
    <span>
      {{ col.title }}
      <span v-if="col.unit"><br />{{ col.unit }}</span>
    </span>
  </van-col>
</template>

<script>
export default {
  name: 'CustomTableColumn',
  props: {
    col: {
      type: Object,
      default: () => {}
    },
    bisect: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    px2vw(px) {
      return `${(100 / 750) * px}vw`;
    },
    setColStyle(width) {
      const w = Number(width) || 150;
      return { width: this.px2vw(w) };
    }
  }
};
</script>
<style lang="scss" scoped>
.row-group {
  position: relative;
  height: 100%;
  padding: 0;
  &::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 1px;
    height: 100%;
    content: '';
    background-color: rgba(0 0 0 / 8%);
  }
  & + .item-cell {
    border-left: 1px solid rgba(0 0 0 / 8%);
  }
}
.group-title {
  box-sizing: border-box;
  border-bottom: 1px solid rgba(0 0 0 / 8%);
}
.item-cell {
  box-sizing: border-box;
  padding: 10px 5px;
  white-space: pre-line;
  & ~ .item-cell {
    border-left: 1px solid rgba(0 0 0 / 8%);
  }
}
.align-center {
  justify-content: center;
  text-align: center;
}
</style>
