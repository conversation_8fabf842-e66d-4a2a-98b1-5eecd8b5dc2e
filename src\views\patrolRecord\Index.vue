<template>
  <div class="page-index danger-record flex-column">
    <van-nav-bar title="巡查记录" fixed right-text="返回" @click-right="onClickLeft" />
    <div class="main flex-column">
      <div class="nav">
        <div class="nav-item" @click="toPage({ name: item.pathName, type: 'add' })" v-for="(item, index) in navList" :key="index">
          <img v-if="item.icon" class="item-icon" :src="require(`@/assets/images/${item.icon}`)" alt="" />
          <div class="item-text">{{ item.title }}</div>
        </div>
      </div>

      <summary-info :list="summaryInfo"></summary-info>

      <div class="content">
        <van-tabs
          v-model="activeTab"
          color="#3E80ED"
          title-active-color="#3E80ED"
          title-inactive-color="#455166"
          @click="onRefresh"
        >
          <van-tab
            v-for="item in tabList"
            :key="item.value"
            title-class="tab-title"
            :title="item.name"
            :name="item.value"
          ></van-tab>
        </van-tabs>

        <van-pull-refresh class="content-detail" v-model="options.isLoading" @refresh="onRefresh">
          <van-list
            v-model="options.loading"
            :finished="options.finished"
            :finished-text="list.length ? '没有更多了' : ''"
            @load="onLoad"
          >
            <div class="detail-item" v-for="item in list" :key="item.id" @click="toDetail(item)">
              <div class="item-left">
                <div>
                  <div class="time">{{ item.xjrq | formatTime }}</div>
                  <div class="text-mini text-bottom">巡查日期</div>
                </div>
                <div>
                  <span class="text-large-more">{{ getLucheng(item.distance || 0) }}</span>
                  <span>km</span>
                </div>
                <div class="text-mini">路程</div>
              </div>
              <div class="item-right">
                <div class="text-with-icon  icon-person">
                  巡查人：<span>{{ item.xjry || '--' }}</span>
                </div>
                <div class="text-with-icon  icon-time">用时： {{ item.xjhs || 0 }}</div>
                <div class="text-with-icon icon-problem">巡检情况：{{ situation(item) || '--' }}</div>
              </div>
            </div>
          </van-list>
          <CustomEmpty v-if="!list.length" description="暂无巡查记录" />
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script>
import SummaryInfo from '@/views/components/summaryInfo.vue';
import listLoading from '@/mixins/listLoading';
import dayjs from 'dayjs';
import { getGcxcList, getPatrolStatisticsData } from '@/api/patrolRecord';
import { mapState } from 'vuex';
export default {
  name: 'DangerRecord',
  mixins: [listLoading],
  components: {
    SummaryInfo
  },
  computed: {},
  props: {},
  data() {
    return {
      navList: [
        {
          title: '我要巡查',
          icon: 'dangerRecord/upload.png',
          pathName: 'Patrol'
        },
        {
          title: '隐患记录',
          icon: 'dangerRecord/record.png',
          pathName: 'DangerRecord'
        },
        {
          title: '巡查路线',
          icon: 'dangerRecord/record.png',
          pathName: 'PatrolRoute'
        }
      ],
      summaryInfo: [
        {
          value: '0',
          label: '本日巡查数',
          color: '#3E80ED',
          icon: 'dangerRecord/danger.png',
          bgStyle: {
            background: `url(${require('@/assets/images/dangerRecord/danger-bg.png')}) no-repeat 0 0`,
            backgroundSize: '100% 100%'
          }
        },
        {
          value: '0',
          label: '巡查完成率',
          color: '#02CA74',
          icon: 'dangerRecord/success.png',
          bgStyle: {
            background: `url(${require('@/assets/images/dangerRecord/success-bg.png')}) no-repeat 0 0`,
            backgroundSize: '100% 100%'
          }
        },
        {
          value: '0',
          label: '异常问题数',
          color: '#FF0000',
          icon: 'dangerRecord/error.png',
          bgStyle: {
            background: `url(${require('@/assets/images/dangerRecord/error-bg.png')}) no-repeat 0 0`,
            backgroundSize: '100% 100%'
          }
        }
      ],
      activeTab: 'all',
      tabList: [
        { name: '全部', value: 'all' },
        { name: '日常巡检', value: 1 },
        { name: '定期巡检', value: 2 },
        { name: '特殊巡检', value: 3 }
      ],
      list: [] // 列表数据
    };
  },
  filters: {
    formatTime(val, fmt = 'YYYY-MM-DD') {
      if (!val) return '';
      return dayjs(val).format(fmt);
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.user.userInfo || getStorage('userInfo')
    }),
    situation() {
      return item => {
        if (item.tempsave === 1) {
          return '进行中';
        }
        if (item.status === 0) {
          return '异常';
        }
        if (item.status === 1) {
          return '正常';
        }
        if (item.status === 2) {
          return '已整改';
        }
        return '--';
      };
    }
  },
  methods: {
    getLucheng(val) {
      if (val >= 10) {
        return (val / 1000).toFixed(2);
      }
      return 0;
    },
    onClickLeft() {
      this.$router.back();
    },
    toPage(item) {
      let { name, ...rest } = item;
      if (name) {
        this.$router.push({
          name,
          params: { ...rest }
        });
      }
    },
    toDetail(row) {
      // this.toPage({ name: 'PatrolDetail', taskId: row.id });
      this.$router.push({
        name: 'PatrolReport',
        query: { taskId: row.id, type: 'detail' }
      });
    },
    // 获取统计数据
    gcCounts() {
      getPatrolStatisticsData({
        wrpcd: this.userInfo.assignWrpcdList?.[0]
      }).then(res => {
        this.$set(this.summaryInfo[0], 'value', res.data['本日巡查数'] || 0);
        this.$set(this.summaryInfo[1], 'value', res.data['巡查完成率'] || 0);
        this.$set(this.summaryInfo[2], 'value', res.data['异常问题数'] || 0);
      });
    },
    // 获取列表数据
    getList() {
      getGcxcList({
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        xjlx: this.activeTab === 'all' ? undefined : this.activeTab,
        wrpcd: this.userInfo.assignWrpcdList?.[0]
      }).then(res => {
        if (res.status === 200) {
          this.list = this.list.concat(res.data.list || []);
          this.total = res.data.total;
          this.options.finished = this.list.length >= this.total;
          this.options.isLoading = false;
          this.options.loading = false;
          this.pageNum += 1;
        }
      });
    }
  },
  created() {
    this.gcCounts();
  },
  mounted() {},
  activated() {}
};
</script>

<style lang="scss" scoped>
@import '@/views/components/common';
@import './index';
</style>
