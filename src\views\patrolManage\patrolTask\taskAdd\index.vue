<template>
  <!-- 新增巡检任务 -->
  <main class="main flex-column">
    <CustomNavBar />
    <section class="section flex-1">
      <div class="section-head">
        <div class="head-title">检查人员</div>
        <img class="head-avatar" src="@/assets/images/patrol/patrolTask/avatar.png" alt="头像" />
        <div class="head-name">{{ formData.xjUserName }}</div>
        <div class="head-date">
          巡检日期： <span>{{ formData.xjTime | filterDate }}</span>
        </div>
      </div>
      <van-form ref="ruleForm" class="section-content">
        <van-field name="radio" label="巡检类别">
          <template #input>
            <van-radio-group v-model="formData.xjType" direction="horizontal" @change="changeType">
              <van-radio v-for="item in typeOptions" :key="item.value" :name="item.value">{{ item.label }}</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-if="formData.xjType === 1"
          readonly
          clickable
          name="picker"
          :value="formData.xjName"
          label="巡检对象"
          placeholder="请选择"
          right-icon="arrow"
          input-align="right"
          required
          :rules="[{ required: true, message: '请选择巡检对象' }]"
          @click="showPicker = true"
        />
        <van-field
          v-else
          v-model="formData.xjName"
          rows="4"
          autosize
          label="巡查名称"
          type="textarea"
          placeholder="请输入"
          show-word-limit
          maxlength="50"
          required
          :rules="[{ required: true, message: '请输入巡查名称' }]"
        />

        <van-field
          v-if="[1, 3].includes(projectType) && formData.xjType === 1"
          readonly
          clickable
          name="picker"
          :value="formData.xjRoute"
          label="巡查路线"
          placeholder="请选择"
          right-icon="arrow"
          input-align="right"
          required
          :rules="[{ required: true, message: '请选择巡查路线' }]"
          @click="showRoutePicker = true"
        />
      </van-form>
    </section>

    <BottomBtn :show-cancel-btn="false" confirm-text="开始巡检" @confirm="submit" />

    <van-popup v-model="showPicker" position="bottom">
      <van-picker
        show-toolbar
        value-key="projectName"
        :columns="pickerColumns"
        @confirm="onPickerConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
    <van-popup v-model="showRoutePicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="routeColumns[projectType]"
        @confirm="onPickerConfirmRoute"
        @cancel="showRoutePicker = false"
      />
    </van-popup>
  </main>
</template>

<script>
import BottomBtn from '@/components/BottomBtn.vue';
import { typeOptions } from '../../options';
import { getTaskObjList, addTask, delTaskById } from '@/api/patrolManage/patrolTask/index.js';
import { setStorage, getStorage, removeStorage } from '@/utils/storage';
import { checkBackgroundPermission, checkGPS, checkNotification, checkPower } from '@/utils/plusPermission';
import dayjs from 'dayjs';
import { mapState } from 'vuex';
export default {
  name: 'TaskAdd',
  components: {
    BottomBtn
  },
  props: {},
  data() {
    const currentDate = dayjs().format('YYYY-MM-DD HH:mm:ss');
    return {
      isCheckBackgroundPermission: false,
      isCheckNotification: false,
      isCheckPower: false,
      fromPathName: '',
      allProjectList: [],
      projectType: null,
      formData: {
        xjType: 1,
        xjTime: currentDate,
        wrpcd: null,
        xjName: '',
        xjUserId: '',
        xjUserName: '',
        areaCode: ''
      },
      showPicker: false,
      showRoutePicker: false,
      routeColumns: {
        1: ['坝顶', '上游坝面', '下游坝面', '外围坝脚', '其他'],
        3: ['堤岸', '背水侧']
      }
    };
  },
  computed: {
    pickerColumns() {
      return this.allProjectList.filter(i => i.projectType === this.projectType);
    },
    ...mapState('patrol', {
      patrolTaskKey: state => state.patrolTaskKey,
      patrolInfoKey: state => state.patrolInfoKey
    }),
    typeOptions() {
      let options = this.$store.getters['common/getOptions']('xjxc:type').map(i => {
        i.label = i.label.replace('巡检', '');
        return i;
      });

      if (!options.length) {
        options = typeOptions;
      }

      return options;
    }
  },
  watch: {},
  filters: {
    filterDate(val) {
      return dayjs(val).format('YYYY-MM-DD');
    }
  },
  created() {
    const param = this.$route.query;
    this.projectType = +param.projectType;
    this.loadData();

    const userInfo = getStorage('userInfo');
    if (userInfo) {
      this.formData.xjUserId = userInfo.id;
      this.formData.xjUserName = userInfo.displayName;
      this.formData.areaCode = userInfo.areaCode;
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.fromPathName = from.name;
    });
  },
  mounted() {
    if (this.fromPathName === 'TaskOn') {
      this.$userApp.toast.show({
        text: `已创建巡查任务，\r\n即将跳转到首页`,
        type: 'text'
      });
      setTimeout(() => {
        this.toPage({ name: 'TaskList' });
      }, 1000);
    } else {
      this.checkTask();
    }
  },
  methods: {
    async checkPermission() {
      if (!this.isCheckBackgroundPermission) {
        await checkBackgroundPermission();
        this.isCheckBackgroundPermission = true;
      }
      if (!this.isCheckNotification) {
        await checkNotification();
        this.isCheckNotification = true;
      }
      if (!this.isCheckPower) {
        await checkPower();
        this.isCheckPower = true;
      }
      checkGPS();
    },
    toPage(item) {
      const { name, ...rest } = item;
      if (name) {
        this.$router.push({
          name,
          query: { ...rest }
        });
      }
    },
    checkTask() {
      const patrolTask = getStorage(this.patrolTaskKey);
      if (patrolTask) {
        this.$dialog
          .confirm({
            title: '提示',
            message: '已有巡检任务，是否确认删除任务重新创建？'
          })
          .then(() => {
            this.deleteTask(patrolTask);
          })
          .catch(() => {
            this.$router.back();
          });
      } else {
        this.checkPermission();
      }
    },
    deleteTask(data) {
      delTaskById([data.id]).then(res => {
        if (res.status === 200) {
          this.$dialog.close();
          this.$store.commit('patrol/resetPatrol');
        } else {
          this.$userApp.toast.show({
            text: res.message,
            type: 'text'
          });
        }
      });
    },
    loadData() {
      getTaskObjList().then(res => {
        if (res.status === 200) {
          this.allProjectList = res.data || [];
        }
      });
    },
    onPickerConfirm(val) {
      this.formData.wrpcd = val.wrpcd;
      this.formData.xjName = val.projectName;
      this.formData.areaCode = val.areaCode;
      this.showPicker = false;
    },
    onPickerConfirmRoute(val) {
      this.formData.xjRoute = val;
      this.showRoutePicker = false;
    },
    changeType() {
      this.formData.wrpcd = null;
      this.formData.xjName = null;
      this.formData.areaCode = getStorage('userInfo').areaCode;
      this.$nextTick(() => {
        this.$refs.ruleForm.resetValidation();
      });
    },
    creatTask() {
      this.$refs.ruleForm.validate().then(() => {
        addTask({
          projectType: this.projectType,
          ...this.formData
        }).then(res => {
          if (res.status === 200) {
            this.$userApp.toast.show({
              text: '已新增巡检任务，即将前往任务页面',
              type: 'text'
            });
            setStorage(this.patrolTaskKey, res.data);
            this.$store.dispatch('patrol/getPosition');

            setTimeout(() => {
              this.$router.push({
                name: 'TaskOn',
                query: { taskId: res.data.id }
              });
            }, 800);
          }
        });
      });
    },
    submit() {
      checkGPS().then(res => {
        if (res) {
          this.creatTask();
        } else {
          this.$dialog
            .confirm({
              title: '提示',
              message: '当前没有开启定位，是否确认进行巡查任务？'
            })
            .then(() => {
              this.creatTask();
            })
            .catch(() => {
              this.$dialog.close();
            });
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
}
.section {
  padding-bottom: 145px;
  .section-head {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 390px;
    font-size: 28px;
    line-height: 36px;
    color: $color-text-white;
    background: url('~@/assets/images/patrol/patrolTask/avatar-bg.png') no-repeat 0 0;
    background-size: 100% 100%;
    .head-title {
      margin-top: 24px;
    }
    .head-avatar {
      width: 123px;
      height: 123px;
      margin: 24px 0;
    }
    .head-name {
      margin-bottom: 20px;
      font-size: 32px;
      font-weight: 500;
      line-height: 40px;
    }
    .head-date {
      color: rgba($color-text-white, 0.8);
    }
  }
}
.bottom-btn {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  padding: 24px 46px;
  background: $bg-page;
  border-top: 1px solid $border-color1;
}
</style>
