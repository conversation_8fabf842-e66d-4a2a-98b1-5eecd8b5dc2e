# Technology Stack

## Core Framework
- **Vue 3** with Composition API and `<script setup>` syntax
- **TypeScript** for type safety and better development experience
- **Vite** as build tool and development server

## UI Framework & Styling
- **Element Plus** as primary UI component library with SCSS theming
- **SCSS/Sass** for styling with global variables and mixins
- **PostCSS** with px-to-viewport conversion for responsive design (1920px base)
- **Autoprefixer** for cross-browser compatibility

## State Management & Routing
- **Pinia** for state management with persistence plugin
- **Vue Router 4** for client-side routing

## HTTP & Real-time Communication
- **Alova** as HTTP client with Axios adapter
- **Socket.IO** for real-time WebSocket communication
- **Axios** for HTTP requests

## Data Visualization & 3D
- **ECharts** for charts and data visualization
- **Three.js** (via 3D models) for 3D reservoir visualization
- **D3.js** for advanced data manipulation

## Utilities & Tools
- **Day.js** for date/time handling
- **Lodash-es** for utility functions
- **JSZip** for file compression/decompression
- **DOMPurify** for XSS protection
- **sm-crypto** for encryption

## Development Tools
- **ESLint 9** with TypeScript and Vue support
- **Prettier** for code formatting
- **Stylelint** for CSS/SCSS linting
- **Auto-import** for Vue APIs and Element Plus components

## Build & Environment
- **Multi-environment support**: development, pretreatment, production, hlq variants
- **Environment-specific configurations** via `.env` files
- **Proxy configuration** for API routing during development

## Common Commands

### Development
```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Start HLQ development mode
pnpm dev:hlq
```

### Building
```bash
# Build for production
pnpm build

# Build for pretreatment
pnpm build:pre

# Build HLQ production
pnpm build:hlq

# Build HLQ pretreatment
pnpm build:hlq-pre
```

### Code Quality
```bash
# Lint code
pnpm lint

# Fix linting issues
pnpm lint:fix

# Preview production build
pnpm preview
```

## Node.js Requirements
- **Node.js 18.8.0+** (for Node 18)
- **Node.js 20.9.0+** (for Node 20) 
- **Node.js 21.1.0+** (for Node 21)
- ESLint 9 requires these minimum versions