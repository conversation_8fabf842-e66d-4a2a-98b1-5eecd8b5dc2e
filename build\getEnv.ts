import path from 'path'

export function isDevFn(mode: string): boolean {
  return mode === 'development'
}

export function isProdFn(mode: string): boolean {
  return mode === 'production'
}

export function isTestFn(mode: string): boolean {
  return mode === 'pretreatment'
}

export function isReportMode(): boolean {
  return process.env.VITE_REPORT === 'true'
}

export function formatEnv(envConf: Recordable): ViteEnv {
  const ret: any = {}

  for (const envName of Object.keys(envConf)) {
    let realName = envConf[envName].replace(/\\n/g, '\n')
    realName = realName === 'true' ? true : realName === 'false' ? false : realName
    if (envName === 'VITE_PORT') realName = Number(realName)
    if (envName === 'VITE_PROXY') {
      try {
        realName = JSON.parse(realName)
      } catch (error) {
        console.log(error)
      }
    }
    ret[envName] = realName
  }
  return ret
}

export function getRootPath(...dir: string[]) {
  return path.resolve(process.cwd(), ...dir)
}
