<template>
  <div class="page-index repair-list-page flex-column">
    <van-nav-bar title="维修养护" fixed right-text="返回" @click-right="onClickLeft" />
    <div class="main flex-column">
      <div class="nav">
        <div class="nav-item" @click="toPage({ name: item.pathName, type: 'add' })" v-for="(item, index) in navList" :key="index">
          <img v-if="item.icon" class="item-icon" :src="require(`@/assets/images/${item.icon}`)" alt="" />
          <div class="item-text">{{ item.title }}</div>
        </div>
      </div>

      <summary-info :list="summaryInfo" style="margin-bottom: 0;"></summary-info>

      <date-time-range
        :startTime="defaultBegin"
        :endTime="defaultEnd"
        :mode="2"
        :clearable="false"
        @update:startTime="updateStartTime"
        @update:endTime="updateEndTime"
      ></date-time-range>

      <div class="content">
        <van-tabs
          v-model="activeTab"
          color="#3E80ED"
          title-active-color="#3E80ED"
          title-inactive-color="#455166"
          @click="onRefresh"
        >
          <van-tab
            v-for="item in tabList"
            :key="item.value"
            title-class="tab-title"
            :title="item.name"
            :name="item.value"
          ></van-tab>
        </van-tabs>

        <van-pull-refresh class="content-detail" v-model="options.isLoading" @refresh="onRefresh">
          <van-list
            v-model="options.loading"
            :finished="options.finished"
            :finished-text="list.length ? '没有更多了' : ''"
            @load="onLoad"
          >
            <div class="detail-item" v-for="item in list" :key="item.id" @click="toDetail(item)">
              <div class="main-info flx">
                <div class="info-left flx-align-center">
                  <img src="@/assets/images/repair/repair-icon.png" alt="" />
                  维养设施：{{ item.wxyhxm || '--' }}
                </div>
              </div>

              <div class="flx">
                <div class="repair-man flx-align-center">
                  <div class="tip-icon icon-time"></div>
                  {{ item.sj ? $dayjs(item.sj).format('YYYY-MM-DD HH时') : '--' }}
                </div>

                <div class="repair-man flx-align-center">
                  <div class="tip-icon icon-person"></div>
                  {{ item.fzr || '--' }}
                </div>
              </div>

              <div class="flx-align-center">
                <div class="tip-icon icon-setting"></div>
                维修养护后图片：
                <div v-if="!item.wxyhhFileList || !item.wxyhhFileList.length" class="flx-align-center no-data">暂无照片</div>
              </div>

              <div class="photo-list flx-align-center" v-if="item.wxyhhFileList && item.wxyhhFileList.length">
                <van-image
                  :src="preUrl + it.file_path"
                  fit="cover"
                  class="photo-item"
                  v-for="(it, ind) in item.wxyhhFileList"
                  :key="it.id"
                  @click.stop="toPreviewImg(preUrl + it.file_path)"
                />
              </div>
            </div>
          </van-list>
          <CustomEmpty v-if="!list.length" description="暂无维养记录" />
        </van-pull-refresh>
      </div>
    </div>
    <van-popup v-model="previewShow" style="width: 100%;">
      <van-image :src="curPreviewImg" fit="contain" width="100%" />
    </van-popup>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getStorage } from '@/utils/storage';
import SummaryInfo from '@/views/components/summaryInfo.vue';
import DateTimeRange from '@/components/DateTimeRange.vue';
import listLoading from '@/mixins/listLoading';
import dayjs from 'dayjs';
import { getRepairListApi, getCurYearCountApi } from '@/api/repair';
import { previewUrl } from '@/api/common.js';

export default {
  name: 'RepairList',
  mixins: [listLoading],
  components: {
    SummaryInfo,
    DateTimeRange
  },
  data() {
    return {
      userInfo: {},
      navList: [
        {
          title: '维养上报',
          icon: 'earlyWarning/detail-icon2.png',
          pathName: 'RepairDetail'
        },
        {
          title: '维养统计',
          icon: 'earlyWarning/detail-icon5.png',
          pathName: 'RepairStatistics'
        }
      ],
      summaryInfo: [
        {
          value: '0',
          label: '本年维修项目',
          color: '#3E80ED',
          icon: 'dangerRecord/danger.png',
          bgStyle: {
            background: `url(${require('@/assets/images/dangerRecord/danger-bg.png')}) no-repeat 0 0`,
            backgroundSize: '100% 100%'
          }
        },
        {
          value: '0',
          label: '本年维修正常',
          color: '#02CA74',
          icon: 'dangerRecord/success.png',
          bgStyle: {
            background: `url(${require('@/assets/images/dangerRecord/success-bg.png')}) no-repeat 0 0`,
            backgroundSize: '100% 100%'
          }
        },
        {
          value: '0',
          label: '本年维修异常',
          color: '#FF0000',
          icon: 'dangerRecord/error.png',
          bgStyle: {
            background: `url(${require('@/assets/images/dangerRecord/error-bg.png')}) no-repeat 0 0`,
            backgroundSize: '100% 100%'
          }
        }
      ],
      activeTab: 'all',
      tabList: [
        { name: '全部', value: 'all' },
        { name: '正常', value: 1 },
        { name: '异常', value: 0 }
      ],
      defaultBegin: this.$dayjs().startOf('year'),
      defaultEnd: this.$dayjs(),
      previewShow: false,
      curPreviewImg: '',
      preUrl: ''
      // list: [] // 列表数据
    };
  },
  filters: {
    formatTime(val, fmt = 'YYYY-MM-DD') {
      return dayjs(val).format(fmt);
    },
    situation(val) {
      return val || val === 0 ? (val === 0 ? '未整改' : '已整改') : '--';
    }
  },
  computed: {
    ...mapState('user', ['projectInfo'])
  },
  methods: {
    onClickLeft() {
      this.$router.back();
    },
    toPage(item) {
      let { name, ...rest } = item;
      if (name) {
        this.$router.push({
          name,
          params: { ...rest }
        });
      }
    },
    updateStartTime(time) {
      this.defaultBegin = time;
      sessionStorage.setItem('repairBeginTime', this.$dayjs(time).format('YYYY-MM-DD'));
      this.onRefresh();
    },
    updateEndTime(time) {
      this.defaultEnd = time;
      sessionStorage.setItem('repairEndTime', this.$dayjs(time).format('YYYY-MM-DD'));
      this.onRefresh();
    },
    toPreviewImg(url) {
      this.curPreviewImg = url;
      this.previewShow = true;
    },
    toDetail(row) {
      this.toPage({ name: 'RepairDetail', type: 'edit', ...row });
    },
    // 获取统计数据
    async gcCounts() {
      const res = await getCurYearCountApi({
        wrpcd: this.userInfo.assignWrpcdList[0],
        year: new Date().getFullYear()
      });
      if (res.data) {
        this.$set(this.summaryInfo[0], 'value', res.data['本年维修项目']);
        this.$set(this.summaryInfo[1], 'value', res.data['本年维修正常']);
        this.$set(this.summaryInfo[2], 'value', res.data['本年维修异常']);
      }
    },
    // 获取列表数据
    async getList() {
      const res = await getRepairListApi({
        wrpcd: this.userInfo.assignWrpcdList[0],
        beginDate: this.defaultBegin.format('YYYY-MM-DD HH:mm:ss'),
        endDate: this.defaultEnd.format('YYYY-MM-DD HH:mm:ss'),
        sfzc: this.activeTab === 'all' ? undefined : this.activeTab,
        pageSize: this.pageSize,
        pageNum: this.pageNum
      });
      if (res.data && res.data.list) {
        this.list = this.list.concat(res.data.list || []);
        this.total = res.data.total;
        this.options.finished = this.list.length >= this.total;
        this.options.isLoading = false;
        this.options.loading = false;
        this.pageNum += 1;
      }
    }
  },
  created() {
    this.userInfo = getStorage('userInfo') || {};
    this.preUrl = previewUrl;
    let begin = sessionStorage.getItem('repairBeginTime') || '';
    let end = sessionStorage.getItem('repairEndTime') || '';
    if (begin) this.defaultBegin = this.$dayjs(begin);
    if (end) this.defaultEnd = this.$dayjs(end);
  },
  mounted() {
    this.gcCounts();
  },
  activated() {
    // this.userInfo = localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : {};
  }
};
</script>

<style lang="scss" scoped>
@import '@/views/components/common';
@import './repairList.scss';
</style>
