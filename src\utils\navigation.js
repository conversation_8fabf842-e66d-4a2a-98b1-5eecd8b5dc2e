import GPS from '@/utils/gps';

/**
 * 打开高德或百度地图
 * @param {*} latitude
 * @param {*} longitude
 * @param {*} name  导航目的地名称
 * @param {*} type 1 百度地图 2 高德地图
 */
export const navToMap = (addressInfo, type) => {
  const { lat, lng, name, address } = addressInfo;
  const addressStr = address || '';
  let url;
  const u = navigator.userAgent;
  //判断ios
  const isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
  //判断Android
  const isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1;

  if (!isIOS && !isAndroid) {
    return showToast('此设备暂不支持');
  }
  if (type === 1) {
    // 百度
    url = `${systemSetting.mapUrlConfig.baidu}?destination=${lat},${lng}&title=${name}&content=${addressStr}&output=html&coord_type=gcj02&src=test`;
  } else if (type === 3) {
    // 腾讯
    url = `${systemSetting.mapUrlConfig.tengxun}?marker=coord:${lat},${lng};title:${name};addr:${addressStr}&referer=${systemSetting.txKey}`;
  } else {
    // 高德
    let params = `?sourceApplication=mingyue&poiname=${name}&lat=${lat}&lon=${lng}&dev=0&t=2`;
    const { android, ios } = systemSetting.mapUrlConfig.gaode;
    url = (isAndroid ? android : ios) + params;
  }
  if (url) {
    window.location.href = url;
  }
};

// 导航
export const checkEnv = (val, position) => {
  /* Start  判断手机是IOS还是安卓 */
  let u = navigator.userAgent;
  //判断是否安卓
  let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1;

  // 判断是否IOS
  let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
  /* End */

  // 获取当前位置
  const point = GPS.gcj_encrypt(+position.lat, +position.lng);
  const { lon, lat } = GPS.bd_encrypt(+point.lat, +point.lon);
  console.log(window.plus);

  // 高德地图
  if (val === 'gaode') {
    window.location.href = `https://uri.amap.com/marker?position=${position.lng},${position.lat}&name=${position.title}&src=mypage&coordinate=wgs84&callnative=1`;
    // if (window.plus) {
    //   window.plus.runtime.launchApplication(
    //     {
    //       pname: 'com.autonavi.minimap'
    //     },
    //     () => {
    //       window.plus.runtime.openWeb(
    //         // `https://uri.amap.com/navigation?to=${position.lng},${position.lat},${position.title},endpoint&mode=car&src=mypage&coordinate=wgs84&callnative=1`
    //         `https://uri.amap.com/navigation?position=${position.lng},${position.lat}&name=${position.title}&src=mypage&coordinate=wgs84&callnative=0`
    //       );
    //     }
    //   );
    //   return;
    // } else {
    //   // window.location.href = `https://uri.amap.com/navigation?to=${position.lng},${position.lat},${position.title},endpoint&mode=car&src=mypage&coordinate=wgs84&callnative=0`;
    //   window.location.href = `https://uri.amap.com/navigation?position=${position.lng},${position.lat}&name=${position.title}&src=mypage&coordinate=wgs84&callnative=0`;
    // }
  } else if (val === 'baidu') {
    //百度地图
    let url = `location=${position.lat},${position.lng}&title=${position.title}&coord_type=wgs84`;
    window.location.href = `https://api.map.baidu.com/marker?${url}&output=html&src=webapp.baidu.openAPIdemo`;
    // if (window.plus && isAndroid) {
    //   window.plus.runtime.launchApplication({ pname: 'com.baidu.BaiduMap' });
    // }
    // 坐标转换

    // if (isAndroid) {
    //   window.location.href = `bdapp://map/marker?${url}&src=andr.baidu.openAPIdemo`;
    //   // window.location.href = `baidumap://map/direction?origin=&destination=${position.lat},${position.lng}&coord_type=wgs84&src=andr.baidu.openAPIdemo`;
    // } else if (isIOS) {
    //   window.location.href = `baidumap://map/marker?${url}&src=ios.baidu.openAPIdemo`;
    // } else {
    //   window.location.href = `https://api.map.baidu.com/marker?${url}&output=html&src=webapp.baidu.openAPIdemo`;
    //   return;
    // }
    // window.setTimeout(() => {
    //   console.log('跳转成功');
    //   window.location.href = `https://api.map.baidu.com/marker?${url}&output=html&src=webapp.baidu.openAPIdemo`;
    // }, 1000);
  }
};
