* {
  box-sizing: border-box;
}
.accountability {
  .main {
    width: 100%;
    height: 100%;
    padding: 20px;
    background-image: linear-gradient(180deg, #3678e9, #ffffff);
    .container {
      width: 100%;
      height: 100%;
      padding: 30px;
      background-image: linear-gradient(160deg, #cff3ff, #ffffff);
      border-radius: 32px;
      .accountability-card {
        padding-bottom: 30px;
        margin-bottom: 30px;
        border-bottom: 1px solid #cccccc;
        .card-title {
          margin-bottom: 30px;
          font-size: 36px;
          font-weight: 700;
          &::before {
            display: inline-block;
            width: 12px;
            height: 35px;
            margin-right: 25px;
            vertical-align: bottom;
            content: '';
            background-color: #3e80ed;
            border-radius: 20px;
          }
        }
        .card-content {
          padding-bottom: 10px;
          .person-item {
            margin-bottom: 20px;
            .person-name {
              display: inline-block;
              padding: 16px 20px;
              margin-bottom: 20px;
              font-size: 26px;
              color: #ffffff;
              text-align: center;
              border-radius: 12px;
              &::before {
                display: inline-block;
                width: 32px;
                height: 32px;
                margin-right: 10px;
                vertical-align: sub;
                content: '';
              }
            }
            .person-info {
              display: grid;
              grid-template-columns: 42% 58%;
              gap: 22px 10px;
              font-size: 26px;
              .info-value {
                line-height: 32px;
              }
            }
            &:first-child .person-name {
              background-color: #558ee6;
              &::before {
                background: url('~@/assets/images/accountability/rp-first.png') no-repeat;
                background-size: 100% 100%;
              }
            }
            &:nth-child(2) .person-name {
              background-color: #34c6ea;
              &::before {
                background: url('~@/assets/images/accountability/rp-second.png') no-repeat;
                background-size: 100% 100%;
              }
            }
            &:nth-child(3) .person-name {
              background-color: #6277df;
              &::before {
                background: url('~@/assets/images/accountability/rp-third.png') no-repeat;
                background-size: 100% 100%;
              }
            }
          }
          .person-item-pics {
            padding-top: 20px;
            .pics-label {
              margin-bottom: 20px;
            }
            .pics-container {
              gap: 10px;
            }
            .van-image {
              width: 160px;
              height: 120px;
              overflow: hidden;
              border-radius: 20px;
            }
          }
        }
        &:last-child {
          padding-bottom: 0;
          margin-bottom: 0;
          border-bottom: none;
        }
      }
    }
  }
}
