import apiUrl from '@/utils/apiUrl';
import appAjax from '@/utils/apiRequestType';
const defaultUrl = apiUrl.defaultUrl;

export function getChartsForAppApi(data) {
  //维修养护-app图表统计
  return appAjax.post(defaultUrl + '/operaMaintenance/chartsForApp', data);
}

export function statisticsCurrentYearByTypeApi(data) {
  //维修养护-app首页本年度项目维修养护分类统计
  return appAjax.post(defaultUrl + '/operaMaintenance/statisticsCurrentYearByType', data);
}

export function getCurYearCountApi(data) {
  //维修养护-app本年度项目正常异常统计
  return appAjax.post(defaultUrl + '/operaMaintenance/statisticsCurrentYear', data);
}

export function getRepairListApi(data) {
  //详情-分页查询 pc或app
  return appAjax.post(defaultUrl + '/operaMaintenance/detailList', data);
}

export function saveRepairApi(data) {
  //保存 pc或app
  return appAjax.post(defaultUrl + '/operaMaintenance/save', data);
}

export function getWeedOrCleanListApi(data) {
  //杂草护理或日常保洁分页列表查询日常保洁列表 app或者pc端
  return appAjax.post(defaultUrl + '/cleaning/pageList', data);
}

export function getCleanPartListApi() {
  //获取保洁类按钮类型
  return appAjax.get(defaultUrl + `/common/option/getTreeOption?type=sk:zchl`);
}

export function addWeedOrCleanApi(data) {
  //杂草护理或日常保洁新增 app或者pc端
  return appAjax.post(defaultUrl + '/cleaning/add', data);
}

export function getCleanDetailApi(id) {
  //杂草护理或日常保洁查看详细 app或者pc端
  return appAjax.get(defaultUrl + `/cleaning/findClearningById?id=${id}`);
}

export function getCleanStatisticsApi(data) {
  //杂草护理或日常保洁-app图表统计
  return appAjax.post(defaultUrl + '/cleaning/chartStatisticsForApp', data);
}
