import Vue from 'vue';
import VueRouter from 'vue-router';
import modules from './modules';

Vue.use(VueRouter);

const routes = [
  {
    path: '/',
    name: 'Index',
    component: () => import('../views/index/Index.vue'),
    meta: {
      title: '首页',
      isKeepAlive: true
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/login/Index'),
    meta: {
      title: '登录',
      isKeepAlive: true
    }
  },
  ...modules
];

const router = new VueRouter({
  routes
});

export default router;
