const files = require.context('.', false, /\.js$/);
const routes = [
  {
    path: '/patrol',
    name: 'Patrol',
    component: () => import('@/views/patrol'),
    meta: {
      title: '我要巡查',
      isKeepAlive: false
    }
  },
  {
    path: '/patroltaskAdd',
    name: 'PatrolTaskAdd',
    component: () => import('@/views/patrol/taskOn'),
    meta: {
      title: '巡查',
      isKeepAlive: true
    }
  },
  {
    path: '/patrolReport',
    name: 'PatrolReport',
    component: () => import('@/views/patrol/taskReport'),
    meta: {
      title: '巡查',
      isKeepAlive: false
    }
  },
  {
    path: '/patrolDetail',
    name: 'PatrolDetail',
    component: () => import('@/views/patrol/patrolDetail'),
    meta: {
      title: '巡查详情',
      isKeepAlive: false
    }
  },
  {
    path: '/patrolRecord',
    name: 'PatrolRecord',
    component: () => import('@/views/patrolRecord/Index'),
    meta: {
      title: '巡查记录',
      isKeepAlive: false
    }
  },
  {
    path: '/dangerRecord',
    name: 'DangerRecord',
    component: () => import('@/views/dangerRecord/Index'),
    meta: {
      title: '隐患记录',
      isKeepAlive: false
    }
  },
  {
    path: '/dangerUpload',
    name: 'DangerUpload',
    component: () => import('@/views/dangerRecord/components/detail.vue'),
    meta: {
      title: '隐患上报',
      isKeepAlive: false
    }
  },
  {
    path: '/reservoirInfo',
    name: 'ReservoirInfo',
    component: () => import('@/views/reservoirInfo/index'),
    meta: {
      title: '水库信息',
      isKeepAlive: false
    }
  },
  {
    path: '/accountability',
    name: 'Accountability',
    component: () => import('@/views/accountability/index'),
    meta: {
      title: '水库信息',
      isKeepAlive: false
    }
  },
  {
    path: '/patrolRoute',
    name: 'PatrolRoute',
    component: () => import('@/views/patrol/patrolRoute'),
    meta: {
      title: '巡查路线',
      isKeepAlive: false
    }
  },
  {
    path: '/repair',
    name: 'Repair',
    component: () => import('@/views/repair/repairList'),
    meta: {
      title: '维修养护',
      isKeepAlive: false
    }
  },
  {
    path: '/repairDetail',
    name: 'RepairDetail',
    component: () => import('@/views/repair/repairDetail.vue'),
    meta: {
      title: '维养上报',
      isKeepAlive: false
    }
  },
  {
    path: '/repairStatistics',
    name: 'RepairStatistics',
    component: () => import('@/views/repair/repairStatistics.vue'),
    meta: {
      title: '维养统计',
      isKeepAlive: false
    }
  },
  {
    path: '/weed',
    name: 'Weed',
    component: () => import('@/views/weed/weedList'),
    meta: {
      title: '杂草护理',
      isKeepAlive: false
    }
  },
  {
    path: '/weedDetail',
    name: 'WeedDetail',
    component: () => import('@/views/weed/weedDetail.vue'),
    meta: {
      title: '护理上报',
      isKeepAlive: false
    }
  },
  {
    path: '/weedStatistics',
    name: 'WeedStatistics',
    component: () => import('@/views/weed/weedStatistics.vue'),
    meta: {
      title: '护理统计',
      isKeepAlive: false
    }
  },
  {
    path: '/clean',
    name: 'Clean',
    component: () => import('@/views/clean/cleanList'),
    meta: {
      title: '日常保洁',
      isKeepAlive: false
    }
  },
  {
    path: '/cleanDetail',
    name: 'CleanDetail',
    component: () => import('@/views/clean/cleanDetail.vue'),
    meta: {
      title: '保洁上报',
      isKeepAlive: false
    }
  },
  {
    path: '/cleanStatistics',
    name: 'CleanStatistics',
    component: () => import('@/views/clean/cleanStatistics.vue'),
    meta: {
      title: '保洁统计',
      isKeepAlive: false
    }
  }
];

files.keys().forEach(key => {
  if (key === './index.js') return;
  routes.push(...files(key).default);
});

export default routes;
