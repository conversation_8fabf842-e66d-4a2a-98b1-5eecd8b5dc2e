import router from './index';
import VueRouter from 'vue-router';
import { getStorage } from '@/utils/storage';
import store from '@/store';
import Vue from 'vue';

// 解决Vue-Router升级导致的Uncaught(in promise) navigation guard问题
const originalPush = VueRouter.prototype.push;
//修改原型对象中的push方法
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};

function reLogin(to, next) {
  Vue.$userApp.toast.show({
    text: '登录过期，请重新登录',
    type: 'text'
  });
  store.dispatch('user/logout');
  next({
    path: '/login',
    query: {
      redirect: to.path
    }
  });
}

router.beforeEach(async (to, from, next) => {
  document.title = to.query.title || to.params.title || to.meta.title;
  // 开发、测试、生产环境
  if (!getStorage('std_token')) {
    if (to.path === '/login') {
      next();
    } else {
      // token过期，重新刷新token
      if (getStorage('std-refreshToken')) {
        store.dispatch('user/refreshToken').then(res => {
          if (res && res.status === 200) {
            const redirect = decodeURIComponent(from.query.redirect || to.path);
            if (to.path === redirect) {
              next({ path: to.path, query: {}, replace: true });
            } else {
              next({ path: redirect, query: {} });
            }
          } else {
            // 刷新token失败，跳转到登录页面
            reLogin(to, next);
          }
        });
      } else {
        reLogin(to, next);
      }
    }
  } else {
    if (to.path === '/login') {
      return next({ path: '/' });
    } else {
      if (Object.keys(store.state.user.userInfo).length === 0) {
        store.dispatch('user/getUserInfo').then(res => {
          if (res && res.status === 200) {
            next();
          }
        });
      } else {
        next();
      }
    }
  }
});
