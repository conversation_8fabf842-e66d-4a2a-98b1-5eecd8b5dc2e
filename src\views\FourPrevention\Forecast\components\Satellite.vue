<template>
  <div class="satellite-container">
    <base-tabs
      class="mt11"
      :active-index="satelliteTabIndex"
      :list="IMG_TYPE_TAB_LIST"
      @change-tab="changeSatelliteTab"
      :disabled="!planId"
      @click-disabled="handleClickDisabled"
    ></base-tabs>
    <div class="satellite-image mt9">
      <img
        :src="currentImageUrl"
        :style="{ opacity: currentImageUrl ? 1 : 0 }"
        v-if="currentImageUrl"
      />
      <el-empty
        :image-size="117"
        v-else
        :description="planId ? '暂无数据' : '请先选择预报方案'"
        :image="EMPTY_NO_DATA"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getSatelliteImage } from '@/alova_api/methods/forecast'
import { useWatcher } from 'alova/client'
import { IMG_TYPE_TAB_LIST } from '../constant'
import { satelliteShowUrl } from '@/api/module/attachment'
import { EMPTY_NO_DATA } from '@/utils/constant/icon'
import { useSatelliteCache } from '../useSatelliteCache'
import { getLastForecastMainListDetail } from '@/api/module/normalView'

const props = defineProps({
  planId: {
    type: Number,
    default: 0
  },
  intervalDuration: {
    type: Number,
    default: 500
  }
})
const { removeAllCache, initCache, processUrl } = useSatelliteCache()
const currentImgIndex = ref(0)

const currenntCache = computed(() => initCache(props.planId))

const { data } = useWatcher(() => getSatelliteImage(props.planId), [() => props.planId]).onSuccess(
  () => {
    const newData = data.value

    currentImgIndex.value = 0

    if (
      currenntCache.value.preloadedRadarImages.length ||
      currenntCache.value.preloadedSatelliteImages.length
    ) {
      return
    }
    if (!newData) {
      return
    }

    const sImgsUrls =
      newData.satelliteCloudImages
        ?.filter(item => item.localUrl)
        .map(item => satelliteShowUrl + item.localUrl) || []
    const rImgsUrls =
      newData.radarPuzzleImages
        ?.filter(item => item.localUrl)
        .map(item => satelliteShowUrl + item.localUrl) || []

    if (sImgsUrls.length === 0 && rImgsUrls.length === 0) {
      return
    }

    const satPromises = sImgsUrls.map(url =>
      processUrl(url, props.planId, 'preloadedSatelliteImages')
    )
    const radarPromises = rImgsUrls.map(url =>
      processUrl(url, props.planId, 'preloadedRadarImages')
    )
    //延迟加载
    setTimeout(() => Promise.all([...satPromises, ...radarPromises]), 500)
  }
)

const currentImageUrl = computed(() => {
  const images =
    satelliteTabIndex.value === 0
      ? currenntCache.value.preloadedSatelliteImages
      : currenntCache.value.preloadedRadarImages
  if (images.length === 0) {
    return ''
  }
  return images[currentImgIndex.value % images.length]
})

// 气象卫星数据
const satelliteTabIndex = ref(0)

const changeSatelliteTab = (index: number) => {
  satelliteTabIndex.value = index
  currentImgIndex.value = 0
}

let timer: NodeJS.Timeout | null = null

const stopTimer = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

const startTimer = () => {
  stopTimer() // Ensure no multiple timers are running
  timer = setInterval(() => {
    currentImgIndex.value++
  }, props.intervalDuration)
}

onMounted(() => {
  startTimer()
})

onUnmounted(() => {
  stopTimer()
  getLastForecastMainListDetail().then(res => {
    removeAllCache(res.data.id)
  })
})

const handleClickDisabled = () => {
  ElMessage.warning('请先选择预报方案')
}
</script>

<style lang="scss" scoped>
.satellite-container {
  box-sizing: border-box;
  padding-left: 11px;
}

.satellite-image {
  width: 100%;
  height: 162px;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 7px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  border: #00a1ff80 1px solid;
  position: relative;
  .loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    width: 80%;
  }
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
