<template>
  <div class="repair-sta-page">
    <van-nav-bar title="日常保洁统计" fixed right-text="返回" @click-right="onClickLeft" />
    <div class="content-part">
      <div class="year-select flx-center" @click="yearSelShow = true">
        年份选择：<span>{{ curSelYear }}</span>
      </div>

      <div class="bar-chart-box">
        <h3 class="info-card-title">{{ curSelYear === '全部年份' ? '各年度' : curSelYear }}日常保洁次数统计</h3>
        <div ref="repairBarRef" class="repair-bar-chart"></div>
      </div>
    </div>

    <van-popup v-model="yearSelShow" position="bottom">
      <van-picker title="选择年份" show-toolbar :columns="columns" @confirm="changeYear" @cancel="yearSelShow = false" />
    </van-popup>
  </div>
</template>

<script>
import { getStorage } from '@/utils/storage';
import { getCleanStatisticsApi } from '@/api/repair';

export default {
  name: 'RepairStatistics',
  components: {},
  data() {
    return {
      userInfo: {},
      curSelYear: '全部年份',
      // curRingType: '',
      // barShowType: 'type',
      yearSelShow: false,
      columns: [
        '全部年份',
        '2025',
        '2024',
        '2023',
        '2022',
        '2021',
        '2020',
        '2019',
        '2018',
        '2017',
        '2016',
        '2015',
        '2014',
        '2013',
        '2012',
        '2011',
        '2010'
      ],
      totalData: {},
      barChartObj: null,
      barOption: {
        grid: {
          left: '3%', // 距离容器左侧的距离
          right: '3%', // 距离容器右侧的距离
          top: '12%', // 距离容器顶部的距离，单位通常是像素
          bottom: '12%', // 距离容器底部的距离
          containLabel: true // 确保标签不会被裁剪
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          show: false
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          name: '单位：次',
          interval: 1
        },
        series: [
          { name: '保洁次数', type: 'bar', data: [], itemStyle: { color: '#6eabee' }, label: { show: true, position: 'top' } }
        ]
      }
    };
  },
  methods: {
    onClickLeft() {
      this.$router.back();
    },
    changeYear(year) {
      this.curSelYear = year;
      this.yearSelShow = false;
      this.getChartData();
    },
    async getChartData() {
      const res = await getCleanStatisticsApi({
        wrpcd: this.userInfo.assignWrpcdList[0],
        tabType: 1,
        year: this.curSelYear === '全部年份' ? '' : this.curSelYear
      });
      if (res.data) {
        this.totalData = JSON.parse(JSON.stringify(res.data));
        this.drawBarChart();
      }
    },

    drawBarChart() {
      this.barOption.xAxis.data = this.totalData.map(it => it.yearOrMonth + `${this.curSelYear === '全部年份' ? '' : '月'}`);
      this.barOption.series[0].data = this.totalData.map(it => it.num);
      this.$nextTick(() => {
        if (!this.barChartObj) {
          this.barChartObj = this.$echarts.init(this.$refs.repairBarRef, null, { renderer: 'svg' });
        }
        this.barChartObj.clear();
        this.barChartObj.setOption(this.barOption);
      });
    }
  },
  created() {
    this.userInfo = getStorage('userInfo') || {};
  },
  mounted() {
    this.getChartData();
  },
  activated() {}
};
</script>

<style lang="scss" scoped>
.repair-sta-page {
  .content-part {
    padding: 110px 20px 20px;
    .year-select {
      width: 100%;
      height: 60px;
      color: #999999;
      cursor: pointer;
      border: 1px solid #c3c3c3;
      border-radius: 12px;
      span {
        color: #333333;
      }
    }
    .ring-chart-box,
    .bar-chart-box {
      width: 100%;
      padding: 20px;
      margin-top: 20px;
      border: 1px solid #c3c3c3;
      border-radius: 12px;
    }
    .info-card-title {
      position: relative;
      display: flex;
      align-items: center;
      margin-bottom: 32px;
      font-size: 32px;
      font-weight: 700;
      color: #000000;
      &::before {
        width: 14px;
        height: 30px;
        margin-right: 15px;
        content: '';
        background: linear-gradient(180deg, #1c73f7 0%, #00d4d4 100%);
        border-radius: 6px;
        box-shadow: 0 -1px 4px 0 #0083cf inset;
      }
      .back-btn {
        position: absolute;
        top: 50%;
        right: 0;
        width: 100px;
        height: 50px;
        transform: translateY(-50%);
        &::v-deep .van-button__text {
          font-size: 26px;
        }
      }
    }
    .repair-summary-chart {
      width: 280px;
      height: 280px;
    }
    .legend-part {
      flex: 1;
      padding-left: 40px;
      .tip {
        margin-bottom: 16px;
        font-size: 38px;
      }
      .legend-item {
        padding: 10px;
        margin-bottom: 20px;
        border: 1px solid #c3c3c3;
        border-radius: 12px;
        &:last-child {
          margin-bottom: 0;
        }
        .legend-left {
          .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 14px;
            border-radius: 50%;
          }
          .legend-title {
            font-size: 28px;
          }
        }
        .legend-num {
          margin-right: 10px;
          font-size: 30px;
        }
      }
    }
    .repair-bar-chart {
      width: 100%;
      height: 500px;
      margin-top: 20px;
    }
  }
}
</style>
