<template>
  <div class="repair-sta-page">
    <van-nav-bar title="维修养护统计" fixed right-text="返回" @click-right="onClickLeft" />
    <div class="content-part">
      <div class="year-select flx-center" @click="yearSelShow = true">
        年份选择：<span>{{ curSelYear }}</span>
      </div>

      <div class="ring-chart-box">
        <div class="type-chart" v-show="!curRingType">
          <h3 class="info-card-title">维修养护项目各类型数量统计</h3>
          <div class="info-card-content flx-align-center">
            <div ref="repairChartRef" class="repair-summary-chart"></div>
            <div class="legend-part">
              <div
                class="legend-item flx-justify-between"
                v-for="item in legendList"
                :key="item.name"
                @click="toRingStatus(item)"
              >
                <div class="legend-left flx-align-center">
                  <div class="legend-color" :style="{ 'background-color': item.itemStyle.color }"></div>
                  <div class="legend-title">{{ item.name }}</div>
                </div>
                <div class="flx-align-center">
                  <div class="legend-num" :style="{ color: item.itemStyle.color }">{{ item.value }}个</div>
                  <van-icon name="arrow" color="#c3c3c3" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="status-chart" v-show="curRingType">
          <h3 class="info-card-title">
            {{ curRingType || '' }}
            <van-button class="back-btn" type="info" size="mini" @click="backToType">返回</van-button>
          </h3>

          <div class="info-card-content flx-align-center">
            <div ref="repairStatusChartRef" class="repair-summary-chart"></div>
            <div class="legend-part">
              <div
                class="legend-item flx-justify-between"
                v-for="item in statusLegendList"
                :key="item.name"
                style="border: none;"
              >
                <div class="legend-left flx-align-center">
                  <div class="legend-color" :style="{ 'background-color': item.itemStyle.color }"></div>
                  <div class="legend-title">{{ item.name }}</div>
                </div>
                <div class="flx-align-center">
                  <div class="legend-num" :style="{ color: item.itemStyle.color }">{{ item.value }}个</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bar-chart-box">
        <h3 class="info-card-title">{{ curSelYear === '全部年份' ? '各年度' : curSelYear }}维修养护项目数量统计</h3>
        <van-radio-group v-model="barShowType" direction="horizontal" @change="drawBarChart">
          <van-radio name="type">按类型统计</van-radio>
          <van-radio name="status">按状态统计</van-radio>
        </van-radio-group>
        <div ref="repairBarRef" class="repair-bar-chart"></div>
      </div>
    </div>

    <van-popup v-model="yearSelShow" position="bottom">
      <van-picker title="选择年份" show-toolbar :columns="columns" @confirm="changeYear" @cancel="yearSelShow = false" />
    </van-popup>
  </div>
</template>

<script>
import { getStorage } from '@/utils/storage';
import { getChartsForAppApi } from '@/api/repair';

export default {
  name: 'RepairStatistics',
  components: {},
  data() {
    return {
      userInfo: {},
      curSelYear: '全部年份',
      curRingType: '',
      barShowType: 'type',
      yearSelShow: false,
      columns: [
        '全部年份',
        '2025',
        '2024',
        '2023',
        '2022',
        '2021',
        '2020',
        '2019',
        '2018',
        '2017',
        '2016',
        '2015',
        '2014',
        '2013',
        '2012',
        '2011',
        '2010'
      ],
      totalData: {},
      legendList: [
        { value: 0, name: '设施检修', itemStyle: { color: '#5c70c8' } },
        { value: 0, name: '设备维护', itemStyle: { color: '#98cc72' } },
        { value: 0, name: '清淤疏浚', itemStyle: { color: '#f3c852' } },
        { value: 0, name: '防渗防漏', itemStyle: { color: '#e26664' } }
      ],
      statusLegendList: [
        { value: 0, name: '正常', itemStyle: { color: '#78d9ac' } },
        { value: 0, name: '异常', itemStyle: { color: '#6eaef5' } }
      ],
      ringTypeChartObj: null,
      ringStatusChartObj: null,
      ringOption: {
        tooltip: {
          trigger: 'item' // 触发类型，默认数据项触发，可选为：'item' | 'axis'
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '100%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 3
            },
            label: {
              normal: {
                show: true,
                position: 'center',
                color: '#333',
                formatter: '{total|' + `${'暂无维修项目'}` + '}',
                rich: {
                  total: {
                    fontSize: 18,
                    color: '#333',
                    lineHeight: 22,
                    fontWeight: 'bold'
                  }
                }
              },
              emphasis: {
                //中间文字显示
                show: true
              }
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: []
          }
        ]
      },
      barTypeLegend: [
        { name: '设施检修', type: 'bar', stack: '总量', data: [], itemStyle: { color: '#5c70c8' } },
        { name: '设备维护', type: 'bar', stack: '总量', data: [], itemStyle: { color: '#98cc72' } },
        { name: '清淤疏浚', type: 'bar', stack: '总量', data: [], itemStyle: { color: '#f3c852' } },
        { name: '防渗防漏', type: 'bar', stack: '总量', data: [], itemStyle: { color: '#e26664' } },
        {
          name: '合计',
          type: 'bar',
          stack: '总量',
          data: [],
          itemStyle: { color: 'transparent' },
          label: {
            show: true,
            position: 'insideBottom',
            color: '#333',
            formatter: '{c}'
          },
          // barWidth: 0, // 关键修改：宽度设为0
          // z: 100 // 提高层级确保标签可见
          barGap: '-100%',
          z: -1
        }
      ],
      barStatusLegend: [
        { name: '正常', type: 'bar', stack: '总量', data: [], itemStyle: { color: '#78d9ac' } },
        { name: '异常', type: 'bar', stack: '总量', data: [], itemStyle: { color: '#6eaef5' } },
        {
          name: '合计',
          type: 'bar',
          stack: '总量',
          data: [],
          itemStyle: { color: 'transparent' },
          label: {
            show: true,
            position: 'insideBottom',
            color: '#333',
            formatter: '{c}'
          },
          barGap: '-100%',
          z: -1
        }
      ],
      barChartObj: null,
      barOption: {
        grid: {
          left: '3%', // 距离容器左侧的距离
          right: '3%', // 距离容器右侧的距离
          top: '12%', // 距离容器顶部的距离，单位通常是像素
          bottom: '12%', // 距离容器底部的距离
          containLabel: true // 确保标签不会被裁剪
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          data: ['设施检修', '设备维护', '清淤疏浚', '防渗防漏'],
          bottom: '0%',
          left: 'center',
          symbol: 'circle',
          itemWidth: 14,
          itemHeight: 14
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          name: '单位：个'
          // interval: 1
        },
        series: []
      }
    };
  },
  methods: {
    onClickLeft() {
      this.$router.back();
    },
    changeYear(year) {
      this.curSelYear = year;
      this.yearSelShow = false;
      this.curRingType = '';
      this.getChartData();
    },
    async getChartData() {
      const res = await getChartsForAppApi({
        wrpcd: this.userInfo.assignWrpcdList[0] || 'A532301S2007',
        year: this.curSelYear === '全部年份' ? '' : this.curSelYear
      });
      if (res.data) {
        this.totalData = JSON.parse(JSON.stringify(res.data));

        this.drawRingType();
        this.drawBarChart();
      }
    },
    drawRingType() {
      this.legendList.forEach(it => {
        it.value = this.totalData.typeChart[it.name];
      });
      this.ringOption.series[0].label.normal.formatter = `{total|${this.legendList
        .map(item => item.value)
        .reduce((acc, curr) => acc + curr, 0) || '暂无维修项目'}}`;
      this.ringOption.series[0].data = JSON.parse(JSON.stringify(this.legendList));
      this.$nextTick(() => {
        if (!this.ringTypeChartObj) {
          this.ringTypeChartObj = this.$echarts.init(this.$refs.repairChartRef, null, { renderer: 'svg' });
        }
        this.ringTypeChartObj.setOption(this.ringOption);
      });
    },
    toRingStatus(row) {
      this.curRingType = row.name;
      this.statusLegendList.forEach(it => {
        it.value = this.totalData.typeStatusChart[this.curRingType][it.name];
      });
      this.ringOption.series[0].label.normal.formatter = `{total|${this.statusLegendList
        .map(item => item.value)
        .reduce((acc, curr) => acc + curr, 0)}}`;
      this.ringOption.series[0].data = JSON.parse(JSON.stringify(this.statusLegendList));
      this.$nextTick(() => {
        if (!this.ringStatusChartObj) {
          this.ringStatusChartObj = this.$echarts.init(this.$refs.repairStatusChartRef, null, { renderer: 'svg' });
        }
        this.ringStatusChartObj.setOption(this.ringOption);
      });
    },
    backToType() {
      this.curRingType = '';
      this.drawRingType();
    },
    drawBarChart() {
      if (this.barShowType === 'type') {
        this.barTypeLegend
          .filter(it => it.name != '合计')
          .forEach(item => {
            item.data = this.totalData.typePipeLineList[item.name].map(it => it.num);
          });
        this.getItemTotalCount();
        this.barOption.xAxis.data = this.totalData.typePipeLineList['设备维护'].map(
          it => it.yearOrMonth + `${this.curSelYear === '全部年份' ? '' : '月'}`
        );
        this.barOption.series = JSON.parse(JSON.stringify(this.barTypeLegend));
        this.barOption.legend.data = ['设施检修', '设备维护', '清淤疏浚', '防渗防漏'];
      } else {
        this.barStatusLegend
          .filter(it => it.name != '合计')
          .forEach(item => {
            item.data = this.totalData.statusPipeLineList[item.name].map(it => it.num);
          });
        this.getItemTotalCount();
        this.barOption.xAxis.data = this.totalData.statusPipeLineList['正常'].map(
          it => it.yearOrMonth + `${this.curSelYear === '全部年份' ? '' : '月'}`
        );
        this.barOption.series = JSON.parse(JSON.stringify(this.barStatusLegend));
        this.barOption.legend.data = ['正常', '异常'];
      }
      this.$nextTick(() => {
        if (!this.barChartObj) {
          this.barChartObj = this.$echarts.init(this.$refs.repairBarRef, null, { renderer: 'svg' });
        }
        this.barChartObj.clear();
        this.barChartObj.setOption(this.barOption);
      });
    },
    getItemTotalCount() {
      let totalArr = [];
      if (this.barShowType === 'type') {
        for (let key in this.totalData.typePipeLineList) {
          totalArr.push(this.totalData.typePipeLineList[key]);
        }

        let typeTotalList = this.totalData.typePipeLineList['设施检修'].map(it => 1);

        this.barTypeLegend.find(it => it.name === '合计').data = typeTotalList.map((item, index) => {
          return totalArr.reduce((acc, curr) => acc + curr[index].num, 0);
        });
      } else {
        for (let key in this.totalData.statusPipeLineList) {
          totalArr.push(this.totalData.statusPipeLineList[key]);
        }

        let typeTotalList = this.totalData.statusPipeLineList['正常'].map(it => 1);

        this.barStatusLegend.find(it => it.name === '合计').data = typeTotalList.map((item, index) => {
          return totalArr.reduce((acc, curr) => acc + curr[index].num, 0);
        });
      }
    }
  },
  created() {
    this.userInfo = getStorage('userInfo') || {};
  },
  mounted() {
    this.getChartData();
  },
  activated() {}
};
</script>

<style lang="scss" scoped>
.repair-sta-page {
  .content-part {
    padding: 110px 20px 20px;
    .year-select {
      width: 100%;
      height: 60px;
      color: #999999;
      cursor: pointer;
      border: 1px solid #c3c3c3;
      border-radius: 12px;
      span {
        color: #333333;
      }
    }
    .ring-chart-box,
    .bar-chart-box {
      width: 100%;
      padding: 20px;
      margin-top: 20px;
      border: 1px solid #c3c3c3;
      border-radius: 12px;
    }
    .info-card-title {
      position: relative;
      display: flex;
      align-items: center;
      margin-bottom: 32px;
      font-size: 32px;
      font-weight: 700;
      color: #000000;
      &::before {
        width: 14px;
        height: 30px;
        margin-right: 15px;
        content: '';
        background: linear-gradient(180deg, #1c73f7 0%, #00d4d4 100%);
        border-radius: 6px;
        box-shadow: 0 -1px 4px 0 #0083cf inset;
      }
      .back-btn {
        position: absolute;
        top: 50%;
        right: 0;
        width: 100px;
        height: 50px;
        transform: translateY(-50%);
        &::v-deep .van-button__text {
          font-size: 26px;
        }
      }
    }
    .repair-summary-chart {
      width: 280px;
      height: 280px;
    }
    .legend-part {
      flex: 1;
      padding-left: 40px;
      .tip {
        margin-bottom: 16px;
        font-size: 38px;
      }
      .legend-item {
        padding: 10px;
        margin-bottom: 20px;
        border: 1px solid #c3c3c3;
        border-radius: 12px;
        &:last-child {
          margin-bottom: 0;
        }
        .legend-left {
          .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 14px;
            border-radius: 50%;
          }
          .legend-title {
            font-size: 28px;
          }
        }
        .legend-num {
          margin-right: 10px;
          font-size: 30px;
        }
      }
    }
    .repair-bar-chart {
      width: 100%;
      height: 500px;
      margin-top: 20px;
    }
  }
}
</style>
