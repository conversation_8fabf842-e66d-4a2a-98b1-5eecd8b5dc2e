<template>
  <div v-if="isApp" class="nav-bar">
    <!-- <div v-if="false" class="nav-bar"> -->
    <van-nav-bar :title="navTitle" left-arrow fixed v-bind="$attrs" @click-left="onClickLeft">
      <!--<template #left>
        <div class="nav-left">
          <slot name="left"></slot>
        </div>
      </template>-->
      <template #right>
        <div class="nav-right">
          <slot name="right"></slot>
        </div>
      </template>
    </van-nav-bar>
  </div>
</template>

<script>
export default {
  name: 'CustomNavBar',
  components: {},
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isApp: false
    };
  },
  computed: {
    navTitle() {
      return this.title || this.$route.meta.title;
    }
  },
  watch: {},
  filters: {},
  created() {
    // this.isApp = process.env.NODE_ENV === 'app';
    this.isApp = true;
  },
  mounted() {},
  methods: {
    onClickLeft() {
      if (this.$listeners['click-left']) {
        this.$listeners['click-left']();
      } else {
        this.$router.back();
        this.$emit('clickNavLeft');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.nav-bar {
  // padding-top: 92px;
  .nav-right {
    font-size: 32px;
    color: #ffffff;
  }
  ::v-deep .van-hairline--bottom::after {
    border-bottom: none;
  }
}
</style>
