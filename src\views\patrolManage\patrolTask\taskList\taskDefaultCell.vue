<template>
  <div class="task-cell">
    <div class="cell-head">
      <van-tag class="head-tag" :type="data.xjStatus === 2 ? 'success' : 'warning'">{{ data.xjStatus | filterStatus }}</van-tag>
      <div class="head-date">{{ data.xjName }}</div>
      <van-icon class="head-btn" name="delete-o" color="#00000066" @click.stop="onDelete">
        <span class="btn-text">删除</span>
      </van-icon>
    </div>
    <div class="cell-content">
      <div class="content-item">
        <span>巡检类型：</span>
        <span class="text-info">{{ data.xjType | filterType(typeOptions) }}</span>
      </div>
      <div class="content-item">
        <span>隐患情况：</span>
        <span :class="[data.yhQk === '有隐患' ? 'text-danger' : 'text-success']">{{ data.yhQk }}</span>
      </div>
      <div class="content-item">
        <span>巡检人员：</span>
        <span>{{ data.xjUserName }}</span>
      </div>
      <div class="content-item">
        <span>巡检时间：</span>
        <span>{{ data.xjStartTime }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { statusOptions, typeOptions, formatOptions } from '../../options';
export default {
  name: 'TaskDefaultCell',
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      options: [
        {
          label: '巡检类型',
          prop: 'xjType'
        },
        {
          label: '隐患情况',
          prop: 'yhQk'
        },
        {
          label: '巡检人员',
          prop: 'xjUserName'
        },
        {
          label: '巡检时间',
          prop: 'xjStartTime'
        }
      ]
    };
  },
  filters: {
    filterStatus(val) {
      return formatOptions(val, statusOptions) || '执行中';
    },
    filterType(val, options) {
      const type = formatOptions(val, options);
      return type || '--';
    }
  },
  computed: {
    typeOptions() {
      let options = this.$store.getters['common/getOptions']('xjxc:type');

      if (!options.length) {
        options = typeOptions;
      }

      return options;
    }
  },
  methods: {
    onDelete() {
      this.$dialog
        .confirm({
          title: '提示',
          message: '是否确认删除任务？'
        })
        .then(() => {
          this.$emit('delete');
        })
        .catch(() => {
          this.$dialog.close();
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.task-cell {
  padding: 32px;
  margin-bottom: 24px;
  font-size: 28px;
  color: rgba($color-text-black, 0.9);
  background: $color-text-white;
  border-radius: 20px;
}
.cell-head {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .head-tag {
    padding: 7px 26px;
    border-radius: 8px;
  }
  .head-date {
    flex: 1;
    margin-left: 20px;
    font-size: 32px;
    font-weight: 500;
    color: $color-text-black;
  }
  .head-btn {
    font-size: 32px;
    cursor: pointer;
    .btn-text {
      margin-left: 12px;
      font-size: 28px;
      color: $color-text-vice;
    }
  }
}
.cell-content {
  display: flex;
  flex-wrap: wrap;
  .content-item {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 8px;
    line-height: 36px;
    &:first-child,
    &:nth-child(2) {
      width: 50%;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
.text-info {
  color: $color-primary;
}
.text-danger {
  color: $color-error;
}
.text-success {
  color: $color-success;
}
</style>
