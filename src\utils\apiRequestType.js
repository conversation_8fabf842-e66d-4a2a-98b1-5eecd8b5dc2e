import qs from 'qs';
import ajaxInterceptors from './ajaxInterceptors';
import AjaxPlugin from 'axios';
import Vue from 'vue';
import { getStorage } from '@/utils/storage';
import store from '@/store';
import router from '@/router';
let cancelTokenSource = AjaxPlugin.CancelToken.source();

let isRefreshing = false;
let requests = [];
// 处理token失效
async function handle401Problem(config, type = 'request') {
  if (!isRefreshing) {
    isRefreshing = true;
    await store.dispatch('user/refreshToken');
    const token = getStorage('std_token');
    console.log('token已刷新');

    // 有新token后再重新请求
    config.headers['Qsz-Authorization'] = token; // 新token
    // token 刷新后将数组的方法重新执行
    requests.forEach(cb => cb(token));
    requests = []; // 重新请求完清空
    isRefreshing = false;
    return type === 'request' ? config : service(config);
  } else {
    // 返回未执行 resolve 的 Promise
    return new Promise(resolve => {
      // 用函数形式将 resolve 存入，等待刷新后再执行
      requests.push(token => {
        config.headers['Qsz-Authorization'] = token;
        return type === 'request' ? resolve(config) : service(config);
      });
    });
  }
}

// 检测请求状态
function checkStatus(response) {
  try {
    // 如果 http 状态码正常, 则直接返回数据
    if (response.status === 200 || response.status === 304) {
      return response.data;
    }
    // 异常状态下, 把错误信息返回去
    // 因为前面我们把错误扶正了, 不然像 404, 500 这样的错误是走不到这里的
    return {
      data: {
        status: response.status,
        message: response.data.Error.Message,
        data: response.data.Error
      }
    };
  } catch (error) {
    return {
      data: {
        errorMessage: '网络异常'
      }
    };
  }
}

// 检测状态码
function checkCode(res) {
  // 如果状态 code 异常(这里已经包括网络错误, 服务器错误, 后端抛出的错误), 可以弹出一个错误提示, 告诉用户
  if (res.status && ![200].includes(res.status)) {
    let errorText = res.message;
    if (res.status === 403) {
      errorText = res.msg || 'token值失效';
    } else if (res.status === 10001) {
      errorText = '接口请求失败';
    } else if (res.status === 404) {
      errorText = '资源请求错误';
    }
    Vue.$userApp.toast.show({
      text: errorText,
      type: 'text',
      time: 2000
    });
    const accessToken = getStorage('std_token') || '';
    if (res.status === 403 && accessToken) {
      store.dispatch('user/logout').then(() => {
        router.push({ path: '/login' });
      });
    }
  } else {
    if (res.data && res.data.errorMessage) {
      Vue.$userApp.toast.show({
        text: res.data.errorMessage,
        type: 'text',
        time: 2000
      });
    }
  }
  return res;
}

const service = AjaxPlugin.create({
  timeout: 30000, // 请求超时时间(毫秒)
  withCredentials: true // 异步请求携带cookie
});

// 添加一个请求拦截器
service.interceptors.request.use(
  async config => {
    // 白名单，不作token校验
    /* const isWhiteList = ['/login', '/404'].indexOf(window.location.pathname) === -1 // 登录页面及404页面无须校验token
    const isWhiteUrl = config.url.indexOf('auth/refresh') === -1 // 刷新token的url，请求头信息无须添加token
    if (isWhiteList) {
      if (!getStorage('std_token')) {
        Message.closeAll()
        if (!getStorage('std-refreshToken')) {
          Message({
            message: '登录过期，请重新登录',
            type: 'warning',
            duration: 500,
            onClose: () => {
              store.dispatch('user/logout')
            }
          })
        } else {
          if (isWhiteUrl) {
            return handle401Problem(config)
          }
        }
      }
    } */

    // 添加loding中组件
    // 设置isHideLoading不需要显示loading
    if (config.isHideLoading) {
      Vue.$userApp.loading.hide();
    } else {
      Vue.$userApp.loading.show();
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 添加一个响应拦截器
service.interceptors.response.use(
  async response => {
    // 401状态，token过期
    if (response.data.status === 401) {
      if (response.config.url.indexOf('auth/refresh') === -1) {
        return handle401Problem(response.config, 'response');
      }
    }
    Vue.$userApp.loading.hide();

    if (response.data.status && response.data.status !== 200 && response.data.status !== 304) {
      let errorText = response.data.message;
      if (response.data.status === 10001) {
        errorText = '接口请求失败';
      } else if (response.data.status === 403) {
        errorText = 'token错误';
      } else if (response.data.status === 404) {
        errorText = '资源请求错误';
      }
      Vue.$userApp.toast.show({
        text: errorText,
        type: 'text',
        time: 2000
      });
    } else {
      if (response.data && response.data.errorMessage) {
        Vue.$userApp.toast.show({
          text: response.data.errorMessage,
          type: 'text',
          time: 2000
        });
      }
    }

    return response;
  },
  error => {
    Vue.$userApp.loading.hide();
    return Promise.reject(error);
  }
);

// get or post请求封装
export default {
  cancel() {
    cancelTokenSource.cancel('取消请求');
    cancelTokenSource = AjaxPlugin.CancelToken.source();
  },
  get(url, params, contentType, noAuthorization, isCancel = true) {
    const accessToken = getStorage('std_token') || '';
    ajaxInterceptors();
    return service({
      method: 'get',
      url: url,
      params, // get 请求时带的参数
      timeout: 30000,
      headers: {
        'Content-Type': contentType || 'application/json',
        'Qsz-Authorization': noAuthorization ? '' : accessToken
      },
      ...(isCancel ? { cancelToken: cancelTokenSource.token } : {})
    })
      .then(checkStatus)
      .then(checkCode);
  },
  getNoAut(url, params, contentType, isCancel = true) {
    ajaxInterceptors();
    return service({
      method: 'get',
      url: url,
      params, // get 请求时带的参数
      timeout: 30000,
      headers: {
        'Content-Type': contentType || 'application/json'
      },
      ...(isCancel ? { cancelToken: cancelTokenSource.token } : {})
    })
      .then(checkStatus)
      .then(checkCode);
  },
  postFormData(url, data, contentType) {
    const accessToken = getStorage('std_token') || '';
    ajaxInterceptors();
    return service({
      method: 'post', // 请求协议
      // async : false,
      url: url, // 请求的地址
      data: qs.stringify(data), // post 请求的数据
      timeout: 50000, // 超时时间, 单位毫秒
      headers: {
        'Content-Type': contentType || 'application/x-www-form-urlencoded;charset=utf-8',
        /* 'Authorization': `Bearer ${(accessToken)}` */
        'Qsz-Authorization': accessToken
      }
    })
      .then(checkStatus)
      .then(checkCode);
  },
  post(url, data, contentType, isCancel = true) {
    const accessToken = getStorage('std_token') || '';
    ajaxInterceptors();
    return service({
      method: 'post', // 请求协议
      url: url, // 请求的地址
      data: JSON.stringify(data), // post 请求的数据
      timeout: 30000, // 超时时间, 单位毫秒
      headers: {
        'Content-Type': contentType || 'application/json',
        'Qsz-Authorization': accessToken
      },
      ...(isCancel ? { cancelToken: cancelTokenSource.token } : {})
    })
      .then(checkStatus)
      .then(checkCode);
  },
  postNoAut(url, data) {
    ajaxInterceptors();
    return service
      .post(url, qs.stringify(data))
      .then(checkStatus)
      .then(checkCode);
  },
  postNoAut2(url, data, contentType) {
    ajaxInterceptors();
    return service({
      method: 'post', // 请求协议
      // async : false,
      url: url, // 请求的地址
      data: JSON.stringify(data), // post 请求的数据
      timeout: 30000, // 超时时间, 单位毫秒
      headers: {
        'Content-Type': contentType || 'application/json'
      }
    })
      .then(checkStatus)
      .then(checkCode);
  },
  postUpLoadFile(url, params, contentType, noAuthorization) {
    const accessToken = getStorage('std_token') || '';
    ajaxInterceptors();
    return service({
      method: 'post', // 请求协议
      url: url, // 请求的地址
      data: params, // post 请求的数据
      timeout: 30000, // 超时时间, 单位毫秒
      headers: {
        'Content-Type': 'multipart/form-data',
        'Qsz-Authorization': noAuthorization ? '' : accessToken,
        AppKey: 'WATERLOCKS'
      }
    }).then(checkStatus);
  },
  postParams(url, params, contentType, noAuthorization) {
    const accessToken = getStorage('std_token') || '';
    ajaxInterceptors();
    return service({
      method: 'post', // 请求协议
      url: url, // 请求的地址
      params, // post 请求的数据
      timeout: 30000, // 超时时间, 单位毫秒
      headers: {
        'Content-Type': contentType || 'application/json',
        // 'Authorization': noAuthorization ? '' : accessToken
        token: noAuthorization ? '' : accessToken
      }
    }).then(checkStatus);
  },
  getDownLoad(url, params, contentType, responseType) {
    const accessToken = getStorage('std_token') || '';
    ajaxInterceptors();
    return service({
      method: 'get',
      url: url,
      params: params,
      timeout: 30000,
      headers: {
        'Content-Type': contentType || 'application/json',
        'Qsz-Authorization': accessToken
        // 'token': noAuthorization ? '' : accessToken,
      },
      responseType: responseType
    }).then(checkStatus);
  },

  postDownLoad(url, params, contentType, responseType) {
    const accessToken = getStorage('std_token') || '';
    // ajaxInterceptors();
    return service({
      method: 'post',
      url: url,
      data: params,
      timeout: 30000,
      headers: {
        'Qsz-Authorization': accessToken,
        'Content-Type': contentType || 'application/json'
      },
      responseType: responseType
    }).then(checkStatus);
  }
};
