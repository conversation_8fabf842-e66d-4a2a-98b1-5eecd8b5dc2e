export default {
  data() {
    return {
      options: {
        loading: false, // 列表是否处于加载状态
        isLoading: false,
        finished: false, // 列表是否加载完成
        columns: []
      },
      list: [], // 列表数据
      total: 0,
      pageNum: 1,
      pageSize: 20
    };
  },
  methods: {
    onLoad() {
      if (this.getList) {
        this.getList();
      }
    },
    onRefresh() {
      this.reset();
      this.onLoad();
    },
    reset() {
      this.options.finished = false;
      this.options.isLoading = false;
      this.options.loading = true;
      this.list = [];
      this.pageNum = 1;
    }
  }
};
