<template>
  <!-- <van-popup v-bind="$attrs" position="right" :style="{ height: '100%', width: '60%' }"> -->
  <div class="popup-content">
    <div class="card">
      <div class="card-title">发现时间</div>
      <div class="card-date">
        <van-field
          class="date-input"
          v-model="formData.beginTime"
          right-icon="arrow-down"
          readonly
          placeholder="开始时间"
          @click="chooseTime('beginTime')"
        />
        <van-field
          class="date-input"
          v-model="formData.endTime"
          right-icon="arrow-down"
          readonly
          placeholder="结束时间"
          @click="chooseTime('endTime')"
        />
      </div>
    </div>
    <div class="card" style="flex: 1">
      <div class="card-title">问题状态</div>
      <div class="card-option">
        <div
          @click="selectStatus(item)"
          class="option-item"
          :class="{ 'option-item_checked': item.value === formData.stnHdWay }"
          v-for="item in statusOptions"
          :key="item.value"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="popup-btn">
      <van-button @click="resetEvent" class="btn" size="large" :square="true">取消</van-button>
      <van-button @click="confirmEvent" class="btn" size="large" :square="true" type="info">确定</van-button>
    </div>
  </div>
  <!-- </van-popup> -->
</template>

<script>
import { handingOptions } from '../../options';
export default {
  name: 'ListFilter',
  props: {
    value: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      statusOptions: [
        {
          label: '全部',
          value: null
        },
        ...handingOptions
      ]
    };
  },
  computed: {
    formData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('update:value', val);
      }
    }
  },
  methods: {
    // 选择问题
    selectStatus(item) {
      this.formData.stnHdWay = item.value;
    },
    // 打开选择时间弹窗
    chooseTime(time) {
      this.$emit('on-choose-time', time);
    },
    // 重置筛选条件
    resetEvent() {
      // for (const key of Object.keys(this.formData)) {
      //   this.formData[key] = null;
      // }
      this.$emit('on-cancel');
    },
    // 筛选确认事件
    confirmEvent() {
      this.$emit('on-confirm');
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../common.scss';
</style>
