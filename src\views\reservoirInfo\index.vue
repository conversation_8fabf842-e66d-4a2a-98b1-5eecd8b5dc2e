<template>
  <div class="page-index reservoir-info flex-column">
    <van-nav-bar title="水库信息" fixed right-text="返回" @click-right="onClickLeft" />
    <div class="main">
      <div class="container">
        <div class="container-title">基本信息</div>
        <div class="container-content">
          <section v-for="item in options" :key="item.value" class="flex" :class="{ 'full-width': item.full }">
            <img v-if="item.icon" :src="require('@/assets/images/resercoirInfo/' + item.icon + '.png')" />
            <div class="desc flex-column">
              <span class="label">{{ item.label }}</span>
              <span :class="{ 'text-ellipsis': item.value !== 'resOv' && item.value !== 'note' }"
                >{{ item.list ? getOptionLabel(basicInfo[item.value], item.list) : basicInfo[item.value] || '--'
                }}{{ item.valueUnit }}</span
              >
            </div>
          </section>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { getProjectInfo } from '@/api/project';
import { getDictionaryTreeList } from '@/api/dict';
export default {
  name: 'ReservoirInfo',
  components: {},
  computed: {},
  props: {},
  data() {
    return {
      options: [
        {
          label: '水库代码',
          value: 'resCode',
          valueUnit: '',
          icon: 'skdm'
        },
        {
          label: '水库名称',
          value: 'resName',
          valueUnit: '',
          icon: 'mc'
        },
        {
          label: '水库中心经度',
          value: 'resCenLong',
          valueUnit: '',
          icon: 'jwd'
        },
        {
          label: '水库中心纬度',
          value: 'resCenLat',
          valueUnit: '',
          icon: 'jwd'
        },
        {
          label: '水库所在位置',
          value: 'resLoc',
          valueUnit: '',
          icon: 'szxz'
        },
        {
          label: '水库类型',
          value: 'resType',
          valueUnit: '',
          icon: 'gcgm',
          list: 'sklx'
        },
        {
          label: '水库功能',
          value: 'rsfn',
          valueUnit: '',
          icon: 'gk'
        },
        {
          label: '工程等别',
          value: 'engGrad',
          valueUnit: '',
          icon: 'gcgm',
          list: 'gcdb'
        },
        {
          label: '工程规模',
          value: 'engScal',
          valueUnit: '',
          icon: 'gcgm',
          list: 'gcgm'
        },
        {
          label: '坝址控制流域面积',
          value: 'watShedArea',
          valueUnit: 'km²',
          icon: 'szly'
        },
        {
          label: '工程建设情况',
          value: 'engStat',
          valueUnit: '',
          icon: 'jcnf',
          list: 'engStat'
        },
        {
          label: '开工时间',
          value: 'startDate',
          valueUnit: '',
          icon: 'jcnf'
        },
        {
          label: '建成时间',
          value: 'compDate',
          valueUnit: '',
          icon: 'jcnf'
        },
        {
          label: '归口管理部门',
          value: 'admDep',
          valueUnit: '',
          icon: 'zgdw',
          list: 'admDep'
        },
        {
          label: '管理单位',
          value: 'mnun',
          valueUnit: '',
          icon: 'gldw',
          full: true
        },
        {
          label: '管理单位类别',
          value: 'mnunType',
          valueUnit: '',
          icon: 'gldw',
          list: 'mnunType'
        },
        {
          label: '单位责任人',
          value: 'mnunResponsible',
          valueUnit: '',
          icon: 'gldw'
        },
        {
          label: '管理单位性质',
          value: 'mnunNature',
          valueUnit: '',
          icon: 'gldw'
        },

        {
          label: '主管单位',
          value: 'cmun',
          valueUnit: '',
          icon: 'zgdw'
        },

        // {
        //   label: '是否显示',
        //   value: 'isShow',
        //   valueUnit: '',
        //   icon: 'gk'
        // },
        {
          label: '所在流域',
          value: 'basin',
          valueUnit: '',
          icon: 'szly'
        },
        {
          label: '所在河流',
          value: 'river',
          valueUnit: '',
          icon: 'szhl'
        },
        {
          label: '水库概况',
          value: 'resOv',
          valueUnit: '',
          icon: 'gk',
          full: true
        },
        {
          label: '备注',
          value: 'note',
          valueUnit: '',
          icon: 'gk',
          full: true
        }
      ],
      basicInfo: {},
      dictTree: [],
      listInfo: {
        sklx: [],
        gcgm: [],
        mnunType: [],
        engStat: [],
        admDep: [],
        gcdb: []
      }
    };
  },
  computed: {
    ...mapState({
      projectInfo: state => state.user.projectInfo,
      userInfo: state => state.user.userInfo
    })
  },
  methods: {
    onClickLeft() {
      this.$router.back();
    },
    async loadDictTree() {
      if (this.dictTree.length === 0) {
        const res = await getDictionaryTreeList({ parentId: -1 });
        if (res.status === 200) {
          this.dictTree = res.data;
        }
      }
    },
    async loadListInfo() {
      const { sklx, gcgm, mnunType, engStat, admDep, gcdb } = this.listInfo;
      const tasks = [];
      if (!sklx.length) {
        tasks.push(
          getDictionaryTreeList({ parentId: this.dictTree.find(item => item.dictCode === 'RESERVOIR_TYPE')?.id }).then(res => {
            if (res.status === 200) {
              this.listInfo.sklx = res.data.map(item => ({
                code: item.dictValue,
                name: item.dictName
              }));
            }
          })
        );
      }
      if (!gcgm.length) {
        tasks.push(
          getDictionaryTreeList({ parentId: this.dictTree.find(item => item.dictCode === 'PROJECT_SCALE')?.id }).then(res => {
            if (res.status === 200) {
              this.listInfo.gcgm = res.data.map(item => ({
                code: item.dictValue,
                name: item.dictName
              }));
            }
          })
        );
      }
      if (!mnunType.length) {
        tasks.push(
          getDictionaryTreeList({
            parentId: this.dictTree.find(item => item.dictCode === 'MANAGEMENT_UNIT_TYPE')?.id
          }).then(res => {
            if (res.status === 200) {
              this.listInfo.mnunType = res.data.map(item => ({
                code: item.dictValue,
                name: item.dictName
              }));
            }
          })
        );
      }
      if (!engStat.length) {
        tasks.push(
          getDictionaryTreeList({ parentId: this.dictTree.find(item => item.dictCode === 'ENG_STAT')?.id }).then(res => {
            if (res.status === 200) {
              this.listInfo.engStat = res.data.map(item => ({
                code: item.dictValue,
                name: item.dictName
              }));
            }
          })
        );
      }
      if (!admDep.length) {
        tasks.push(
          getDictionaryTreeList({ parentId: this.dictTree.find(item => item.dictCode === 'ADM_DEP')?.id }).then(res => {
            if (res.status === 200) {
              this.listInfo.admDep = res.data.map(item => ({
                code: item.dictValue,
                name: item.dictName
              }));
            }
          })
        );
      }
      if (!gcdb.length) {
        tasks.push(
          getDictionaryTreeList({ parentId: this.dictTree.find(item => item.dictCode === 'PROJECT_GRADE')?.id }).then(res => {
            if (res.status === 200) {
              this.listInfo.gcdb = res.data.map(item => ({
                code: item.dictValue,
                name: item.dictName
              }));
            }
          })
        );
      }
      if (tasks.length > 0) {
        await Promise.all(tasks);
      }
    },
    getOptionLabel(value, list) {
      const label = this.listInfo[list].find(item => item.code === value + '')?.name || '--';
      return label;
    },
    getData() {
      getProjectInfo({ wrpcd: this.userInfo.assignWrpcdList?.[0] || this.projectInfo.wrpcd }).then(res => {
        if (res.status === 200) {
          this.basicInfo = res.data?.[0] || {};
          this.basicInfo.jwd = `${this.basicInfo.lgtd}${this.basicInfo.lttd ? ',' : ''}${this.basicInfo.lttd}`;
        }
      });
    }
  },
  async created() {
    this.getData();
    await this.loadDictTree();
    this.loadListInfo();
  }
};
</script>

<style lang="scss" scoped>
@import './index';
</style>
