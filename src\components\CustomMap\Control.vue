<template>
  <!-- 地图控制组件 -->
  <div class="comp-wrap">
    <ul class="btn-list">
      <li class="btn" v-for="item in topBtns" :key="item.key" @click="controlEvent(item.key)">
        <img class="icon" :src="require(`@/assets/images/map/${item.icon}`)" alt="" />
      </li>
    </ul>

    <ul class="btn-list">
      <li class="btn" v-for="item in bottomBtns" :key="item.key" @click="controlEvent(item.key)">
        <img class="icon" :src="require(`@/assets/images/map/${item.icon}`)" alt="" />
      </li>
    </ul>

    <van-popup v-model="popupVisible" position="right" :style="{ height: '100%', width: '60%' }">
      <div class="popup-content">
        <div class="card">
          <div class="card-title">基础底图</div>
          <div class="card-option">
            <div
              @click="switchBaseMap(item)"
              class="option-item"
              :class="{ 'option-item_checked': item.name === baseLayer }"
              v-for="item in mapImageOptions"
              :key="item.key"
            >
              <img class="item-icon" :src="require(`@/assets/images/map/${item.icon}`)" alt="" />
              <div class="text-center">{{ item.name }}</div>
            </div>
          </div>
        </div>
        <slot name="popupContent"></slot>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { getCurrentPosition } from '@/utils/location';
export default {
  name: 'MapControl',
  props: {
    value: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      topBtns: Object.freeze([
        {
          key: 'controlPanel',
          icon: 'map-layer.png'
        },
        {
          key: 'position',
          icon: 'current-pos.png'
        },
        {
          key: 'initView',
          icon: 'init-map.png'
        }
      ]),
      bottomBtns: Object.freeze([
        {
          key: 'zoomIn',
          icon: 'map-plus.png'
        },
        {
          key: 'zoomOut',
          icon: 'map-minus.png'
        }
      ]),
      mapImageOptions: Object.freeze([
        {
          key: 'image',
          name: '影像图',
          icon: 'image-map.png'
        },
        {
          key: 'vector',
          name: '矢量图',
          icon: 'vector-map.png'
        },
        {
          key: 'relief',
          name: '地形图',
          icon: 'relief-map.png'
        }
      ]),
      popupVisible: false
    };
  },
  computed: {
    baseLayer: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  methods: {
    controlEvent(key) {
      const that = this;
      switch (key) {
        case 'controlPanel':
          that.popupVisible = true;
          break;
        case 'position':
          getCurrentPosition(position => {
            const { longitude, latitude } = position.coords;
            that.$emit('flyToCoors', longitude, latitude);
          });
          break;
        case 'initView':
          that.$emit('initView');
          break;
        case 'zoomIn':
        case 'zoomOut':
          that.$emit('controlZoom', key === 'zoomIn');
          break;
      }
    },
    switchBaseMap(item) {
      this.baseLayer = item.name;
      this.$emit('switchBaseMap', item.name);
    }
  }
};
</script>

<style lang="scss" scoped>
.comp-wrap {
  position: absolute;
  top: 20px;
  right: 20px;
  .btn-list {
    padding: 10px;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 4px -1px #0000001f, 0 4px 5px 0 #00000014, 0 1px 10px 0 #0000000d;
    .btn {
      width: 48px;
      height: 48px;
      & + .btn {
        margin-top: 20px;
      }
      .icon {
        width: 100%;
        height: 100%;
      }
    }
    & + .btn-list {
      margin-top: 10px;
    }
  }
}
.popup-content {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  font-size: 28px;
  line-height: 36px;
  color: $color-text-black;
  background: $bg-page-gray;
  .card {
    box-sizing: border-box;
    padding: 32px 32px 16px;
    margin-bottom: 16px;
    background: $bg-page;
    .card-title {
      margin-bottom: 16px;
      font-size: 32px;
      font-weight: 500;
      line-height: 40px;
    }
    .card-option {
      display: flex;
      flex-flow: row wrap;
      .option-item {
        box-sizing: border-box;
        width: calc(50% - 10px);
        margin-bottom: 16px;
        &:nth-child(odd) {
          margin-right: 20px;
        }
        .item-icon {
          width: 100%;
          height: 120px;
          overflow: hidden;
          border-radius: 20px;
        }
      }
      .option-item_checked .item-icon {
        box-sizing: border-box;
        border: 5px solid $color-primary;
      }
    }
  }
}
</style>
