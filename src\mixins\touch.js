export default {
  data() {
    return {
      visible: true,
      tranY: 0,
      transitionTime: 0,
      startPosition: 0,
      moveDistance: 0,
      rate: 0.2,
      maxHeight: 0
    };
  },
  methods: {
    onTouchStart(e) {
      //#ifdef MP-WEIXIN || H5
      this.startPosition = e.touches[0].clientY;
      //#endif
      //#ifdef APP-PLUS
      this.startPosition = e.changedTouches[0].screenY;
      //#endif
      this.transitionTime = 0;
      this.moveDistance = 0;
    },
    onTouchmove(e) {
      //#ifdef MP-WEIXIN || H5
      this.moveDistance = e.touches[0].clientY - this.startPosition;
      //#endif
      //#ifdef APP-PLUS
      this.moveDistance = e.changedTouches[0].screenY - this.startPosition;
      //#endif

      if (this.visible) {
        // 显示情况向下滑
        if (this.moveDistance > 0) {
          this.tranY = this.moveDistance;
        }
      } else {
        // 不显示情况向上滑
        if (this.moveDistance < 0) {
          const distance = Math.abs(this.moveDistance) > this.maxHeight ? -this.maxHeight : this.moveDistance;
          this.tranY = distance;
        }
      }
    },
    onTouchend(e) {
      this.transitionTime = 0.3;
      if (this.moveDistance === 0) {
        this.visible = !this.visible;
        return;
      }
      const minVal = Math.abs(this.rate * this.maxHeight);
      if (this.visible) {
        // 显示情况向下滑
        if (this.moveDistance >= minVal) {
          this.visible = false;
        }
      } else {
        // 不显示情况向上滑
        if (this.moveDistance < 0 && Math.abs(this.moveDistance) >= minVal) {
          this.visible = true;
        }
      }
      this.tranY = 0;
    }
  }
};
