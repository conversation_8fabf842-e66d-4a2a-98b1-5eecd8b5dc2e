import apiUrl from '@/utils/apiUrl';
import appAjax from '@/utils/apiRequestType';
const defaultUrl = apiUrl.defaultUrl;

/**
 * 登录接口
 * @param data
 * username string
 * password string
 */
export function login(data) {
  return appAjax.post(defaultUrl + '/auth/login', data);
}

/**
 * 获取验证码接口
 */
export function getCaptcha(params) {
  return appAjax.get(defaultUrl + '/auth/getCaptcha', params);
}

/**
 * 登录用户信息接口
 */
export function userInfo(isHide) {
  return appAjax.post(defaultUrl + '/auth/user-info', '', isHide);
}

/**
 * 刷新token接口
 */
export function refresh(data) {
  return appAjax.post(defaultUrl + '/auth/refresh', data);
}
