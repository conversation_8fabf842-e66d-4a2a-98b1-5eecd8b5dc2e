<template>
  <div class="custom-table">
    <div ref="tableHeadRef" class="head-box">
      <div class="table-head" :style="rowStyle">
        <van-row
          class="item flex-vc"
          :style="headColStyle[colKey]"
          v-for="(col, colKey) in options.columns"
          :key="col.key"
          :class="{
            'align-center': col.align === 'center',
            'fixed-col': col.fixed,
            'fixed-shadow': isScroll && colKey === lastLeftFixedCol,
            'fixed-shadow-right': isRightScroll && colKey === lastRightFixedCol
          }"
        >
          <CustomTableColumn v-if="col.children" :col="col" />
          <template v-else>
            <div class="item-cell" v-if="col.titleSlot">
              <slot :data="{ col }" :name="col.titleSlot"></slot>
            </div>
            <span class="item-cell" v-else>
              {{ col.title }}
              <span v-if="col.unit"><br />{{ col.unit }}</span>
            </span>
          </template>
        </van-row>
      </div>
    </div>

    <van-pull-refresh
      ref="tableBodyRef"
      v-model="options.isLoading"
      @refresh="onRefresh"
      class="table-list"
      :disabled="disabledRefresh"
    >
      <van-list
        v-model="options.loading"
        :style="rowStyle"
        :offset="100"
        :finished="options.finished"
        :immediate-check="immediateCheck"
        @load="onLoad"
        :finished-text="dataList.length ? '没有更多了' : ''"
      >
        <van-row
          class="row-item"
          :class="{ highlight: highlight && rowKey === highlightRowKey }"
          v-for="(row, rowKey) in dataList"
          :key="rowKey"
          @click="clickRow(row, rowKey)"
        >
          <van-col
            :style="colStyle[colKey]"
            class="item flex-vc"
            v-for="(col, colKey) in realColumns"
            :key="col.key"
            :class="{
              'align-center': col.align === 'center',
              'fixed-col': col.fixed,
              'fixed-shadow': isScroll && colKey === lastLeftFixedCol,
              'fixed-shadow-right': isRightScroll && colKey === lastRightFixedCol
            }"
          >
            <div class="item-cell" v-if="col.colSlot" :class="{ 'primary-col': colKey === primaryIdx }">
              <slot :data="{ row }" :index="rowKey" :node="col" :name="col.colSlot"></slot>
            </div>
            <span
              class="item-cell"
              v-else
              :class="{ 'primary-col': colKey === primaryIdx, 'danger-color': col.error && col.error(row) }"
            >
              {{ (col.formatter ? col.formatter(row[col.key], row, rowKey) : row[col.key]) ?? '--' }}
            </span>
          </van-col>
        </van-row>
      </van-list>
      <CustomEmpty v-if="!dataList.length && needEmpty" :description="emptyDesc" />
    </van-pull-refresh>
  </div>
</template>

<script>
import CustomTableColumn from './CustomTableColumn';
export default {
  components: { CustomTableColumn },
  props: {
    options: {
      type: Object,
      default: () => ({
        isLoading: false,
        loading: false,
        finished: false
      })
    },
    dataList: {
      type: Array,
      default: () => []
    },
    // 无数据时，是否需要显示无数据图片
    needEmpty: {
      type: Boolean,
      default: true
    },
    // 显示主色调
    primaryIdx: {
      type: Number,
      default: -1
    },
    emptyDesc: {
      type: String,
      default: '暂无数据'
    },
    // 禁用下拉刷新
    disabledRefresh: {
      type: Boolean,
      default: false
    },
    //是否在初始化时立即执行滚动位置检查
    immediateCheck: {
      type: Boolean,
      default: true
    },
    highlight: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      highlightRowKey: null,
      isScroll: false,
      isRightScroll: false,
      realColumns: [],
      rowStyle: {},
      headColStyle: [],
      colStyle: []
    };
  },
  computed: {
    columnsKeys() {
      return this.options.columns
        .flat(Infinity)
        .filter(col => col.key)
        .map(col => col.key)
        .join(',');
    },
    lastLeftFixedCol() {
      return this.options.columns.filter(col => col.fixed && col.fixed !== 'right').length - 1;
    },
    lastRightFixedCol() {
      return this.options.columns.findIndex(col => col.fixed === 'right');
    }
  },
  watch: {
    'dataList.length': {
      handler(newVal) {
        if (newVal === 0) this.highlightRowKey = null;
      }
    },
    columnsKeys() {
      this.getTableConfig();
    }
  },
  created() {
    this.getTableConfig();
  },
  mounted() {
    this.$refs.tableHeadRef.addEventListener('scroll', this.headScrollEvent);
    this.$refs.tableBodyRef.$el.addEventListener('scroll', this.bodyScrollEvent);
  },
  beforeDestroy() {
    this.$refs.tableHeadRef.removeEventListener('scroll', this.headScrollEvent);
    this.$refs.tableBodyRef.$el.removeEventListener('scroll', this.bodyScrollEvent);
  },
  methods: {
    setColStyle({ width }) {
      return width ? { width: this.px2vw(width) } : { flex: 1 };
    },
    px2vw(px) {
      return `${(100 / 750) * px}vw`;
    },
    getTableConfig() {
      const flatFn = (arr, isChild = false) => {
        return arr.reduce((acc, cur) => {
          if (cur.children) {
            return acc.concat(flatFn(cur.children, true));
          } else {
            if (isChild) {
              cur.width = Number(cur.width) || 150;
            }
            return acc.concat(cur);
          }
        }, []);
      };

      const realColumns = flatFn(this.options.columns);
      this.realColumns = realColumns;

      const tableWidth = realColumns.reduce((acc, cur) => {
        return acc + (Number(cur.width) || 150);
      }, 0);

      this.rowStyle = tableWidth > 750 ? { width: this.px2vw(tableWidth) } : {};

      let fixedWidth = 0;
      this.headColStyle = this.options.columns.map((col, index) => {
        let obj = { flex: 1 };
        if (col.width) {
          obj = { width: this.px2vw(+col.width) };
        }

        if (tableWidth > 750 && col.fixed) {
          if (col.fixed === 'right') {
            let width = this.options.columns
              .filter((i, idx) => idx > index && i.fixed === 'right')
              .reduce((acc, cur) => {
                return acc + (Number(cur.width) || 150);
              }, 0);

            // 右边固定列时，表格滚动条会占去一部分宽度，所以表头右边加10的宽度，避免表头表格固定列的阴影位置错位过大
            if (width) {
              width += 10;
            } else {
              obj.width = this.px2vw((+col.width || 150) + 10);
            }
            obj.right = this.px2vw(width);
          } else {
            obj.left = this.px2vw(fixedWidth);
            fixedWidth += Number(col.width) || 150;
          }
        }

        return obj;
      });

      let colFixedWidth = 0;
      this.colStyle = realColumns.map((col, index) => {
        let obj = { flex: 1 };
        let colWidth = Number(col.width);
        if (tableWidth > 750) {
          colWidth = colWidth || 150;
        }
        if (colWidth) {
          obj = { width: this.px2vw(colWidth) };
        }
        if (tableWidth > 750 && col.fixed) {
          if (col.fixed === 'right') {
            const width = realColumns
              .filter((i, idx) => idx > index && i.fixed === 'right')
              .reduce((acc, cur) => {
                return acc + (Number(cur.width) || 150);
              }, 0);
            obj.right = this.px2vw(width);
          } else {
            obj.left = this.px2vw(colFixedWidth);
            colFixedWidth += colWidth;
          }
        }

        return obj;
      });
    },
    // 下拉刷新
    onRefresh() {
      this.$emit('onRefresh');
    },
    // 加载列表
    onLoad() {
      this.$emit('onLoad');
    },
    // 点击行
    clickRow(row, rowKey) {
      this.highlightRowKey = rowKey;
      this.$emit('onClickRow', row, rowKey);
    },
    bodyScrollEvent(e) {
      this.$refs.tableHeadRef.scrollLeft = e.target.scrollLeft;
    },
    headScrollEvent(e) {
      this.$refs.tableBodyRef.$el.scrollLeft = e.target.scrollLeft;
      this.isScroll = e.target.scrollLeft > 3;
      this.isRightScroll = e.target.scrollLeft + e.target.clientWidth < e.target.scrollWidth - 3;
    }
  }
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.custom-table {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-top: 5px; // 滚动的时候会有点溢出，加个padding 原因不详
  overflow: hidden;
  overflow-x: auto;
  scrollbar-width: none;
  line-height: 35px;
  .head-box {
    overflow-x: auto;
    scrollbar-width: none;
  }
  .table-head {
    display: flex;
    align-items: stretch;
    width: 100%;
    font-size: 30px;
    color: rgb(0 0 0 / 60%);
    background: #dceaf8;
    .item {
      background: #dceaf8;

      // @include ellipsisText;
      &:first-child .item-cell {
        padding-left: 20px;
      }
      &:last-child .item-cell {
        padding-right: 20px;
      }
      .item-cell {
        box-sizing: border-box;
        padding: 18px 5px;
        white-space: pre-line;
      }
    }
  }
  .table-list {
    position: relative;
    flex: 1;
    overflow: auto;

    // scrollbar-width: none;

    // height: 100%;
    .row-item {
      display: flex;
      align-items: stretch;
      font-size: 30px;

      // border-bottom: 1px solid #ededed;
      background: #ffffff;
      &:nth-child(2n) {
        background: #f2f2f2;
        .item {
          background: #f2f2f2;
        }
      }
      .item {
        background: #ffffff;

        // @include ellipsisText;
        &:first-child .item-cell {
          padding-left: 20px;
        }
        &:last-child .item-cell {
          padding-right: 20px;
        }
        .item-cell {
          box-sizing: border-box;
          padding: 22px 5px;
          white-space: pre-line;
        }
      }
      &.highlight {
        background: #b2c7f0;
        .item {
          background: #b2c7f0;
        }
      }
    }
  }

  // &::v-deep .van-list__finished-text {
  //   position: fixed;
  //   right: 0;
  //   left: 0;
  // }
}
.align-center {
  justify-content: center;
  text-align: center;
}
.primary-col {
  color: $color-primary;
}
.fixed-col {
  position: sticky;
  z-index: 9;
}
.fixed-shadow {
  box-shadow: inset -10px 0 10px -10px rgb(0 0 0 / 15%);
}
.fixed-shadow-right {
  box-shadow: inset 10px 0 10px -10px rgb(0 0 0 / 15%);
}
</style>
