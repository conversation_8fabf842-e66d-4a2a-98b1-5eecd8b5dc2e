import dayjs from 'dayjs';
import utils from '@/utils/utils';
import myNJS from '@/utils/myNJS';
import { getStorage, setStorage, removeStorage } from '@/utils/storage';
import GPS from '@/utils/gps';
import { getCurrentPosition, watchPosition, clearWatch } from '@/utils/location';
import { setWakelock, an_setWakelock, an_releaseWakelock, an_getLastKnownLocation } from '@/utils/plusPermission';

const state = {
  patrolTaskKey: 'patrolTaskStorage', // 巡查任务存到Storage的key
  patrolInfoKey: 'patrolInfoStorage', // 巡查任务产生的数据存到Storage的key
  an_wakelock: null, // 安卓唤醒锁
  BMap: null,
  map: null,
  center: { lng: 101.48684, lat: 25.114957 }, // 地图中心点
  patrolTask: {}, // 巡查任务数据
  watchId: null, // 巡查watchPosition
  routeArray: [], // 巡查路线坐标点
  coordinates: [], // 84坐标系巡查轨迹点
  distance: 0, // 巡查路线长度m
  time: '00:00:00', // 巡查经历时间
  timer: null,
  interval: 10000, // 记录点位的间隔时间（毫秒）
  backStatus: true // 是否切换后台，使用相机等非主动切换后台情况要设置为false，避免触发 moveTaskToBack 事件
};

const mutations = {
  setMap(state, { BMap, map }) {
    state.BMap = BMap;
    state.map = map;
  },
  setCenter(state, data) {
    state.center = data;
    console.log('state.center', state.center);
  },
  setPosition(state, data) {
    state.currentPosition = data;
    console.log('state.currentPosition', state.currentPosition);
  },
  setWatchId(state, data) {
    state.watchId = data;
  },
  setInterval(state, data) {
    state.interval = data;
  },
  seBackStatus(state, data = false) {
    state.backStatus = data;
  },
  setAndroidWakelock(state, data) {
    state.an_wakelock = data;
  },
  setRoute(state, data) {
    state.routeArray.push(data);
  },
  setCoordinates(state, data) {
    state.coordinates.push(data);
  },
  setDistance(state, data) {
    state.distance += data;
  },
  setPatrolInfo(state, taskId) {
    const patrolTaskStorage = getStorage(state.patrolTaskKey);
    if (patrolTaskStorage && patrolTaskStorage.id === taskId) {
      state.patrolTask = patrolTaskStorage;
    }
    const patrolInfoStorage = getStorage(state.patrolInfoKey);
    if (patrolInfoStorage && patrolInfoStorage.taskId === taskId) {
      state.coordinates = patrolInfoStorage.coordinates;
      state.routeArray = patrolInfoStorage.routeArray;
      state.distance = patrolInfoStorage.distance;
    }
  },
  setPatrolTimer(state) {
    if (state.patrolTask.xjStartTime) {
      const xjStartTime =
        dayjs().diff(dayjs(state.patrolTask.xjStartTime)) >= 0
          ? state.patrolTask.xjStartTime
          : dayjs().format('YYYY-MM-DD HH:mm:ss');
      if (state.timer) {
        clearInterval(state.timer);
      }
      state.timer = setInterval(() => {
        let diffTime = dayjs().diff(dayjs(xjStartTime));
        state.time = utils.formatTime(diffTime, 'H:M:S');
      }, 1000);
    }
  },
  resetPatrol(state) {
    if (state.watchId) {
      clearWatch(state.watchId);
      state.watchId = null;
    }

    if (window.plus) {
      setWakelock(false);

      if (plus.os.name === 'Android') {
        an_releaseWakelock(state.an_wakelock);
        state.an_wakelock = null;
        // 清除常驻通知
        myNJS.aOSReceive();
      }
    }

    if (state.timer) {
      clearInterval(state.timer);
      state.timer = null;
    }

    state.photoIds = [];
    state.patrolTask = {};
    state.routeArray = [];
    state.coordinates = [];
    state.distance = 0;
    state.time = '00:00:00';

    setTimeout(() => {
      removeStorage(state.patrolTaskKey);
      removeStorage(state.patrolInfoKey);
    });
  }
};

const actions = {
  getPosition({ commit }) {
    const successFn = position => {
      const { longitude, latitude } = position.coords;
      const point = GPS.gcj_encrypt(latitude, longitude);
      const { lat, lon } = GPS.bd_encrypt(point.lat, point.lon);
      if (lat && lon) {
        commit('setCenter', { lng: lon, lat });
        commit('setPosition', { lng: lon, lat });
        console.log('position', position, 'point', point, 'lat', lat, 'lon', lon);
      }
    };

    getCurrentPosition(successFn);
  },
  startPatrol({ commit, state }, taskId) {
    commit('setPatrolInfo', taskId);
    commit('setPatrolTimer');

    if (window.plus) {
      setWakelock(true);
      if (plus.os.name === 'Android') {
        const an_wakelock = an_setWakelock();
        commit('setAndroidWakelock', an_wakelock);

        myNJS.aOSNotify('巡查进行中', '切换到后台将导致巡查轨迹不准确，请保持应用在前台运行');
      }
    }

    let curTime = 0;
    let preTime = 0;
    // todo hbuild3.8.7及之前几个版本(新版本未验证)获取 ios 坐标的坐标系是gcj02，与参数不符，需要转换
    const successFn = position => {
      const timestamp = position.timestamp || dayjs().valueOf();
      curTime = timestamp;
      if (!preTime) {
        preTime = timestamp;
      }
      if (curTime - preTime < state.interval) {
        // 每10秒保存一次坐标
        return;
      }

      const { longitude: lng, latitude: lat, accuracy, speed } = position.coords;

      if (state.routeArray.length) {
        const lastPoint = state.routeArray[state.routeArray.length - 1];

        if (speed === 0 && state.routeArray.length > 1) {
          const lastPrevPoint = state.routeArray[state.routeArray.length - 2];
          if (lastPoint.speed === 0 && lastPrevPoint.speed === 0) {
            // 连续 3 个点速度都为 0 ，不记录
            return;
          }
        }

        const dis = GPS.distance(lastPoint.lat, lastPoint.lng, lat, lng);
        const maxDis = ((timestamp - dayjs(lastPoint.time).valueOf()) / 1000) * 22;
        if (dis < 10 || dis > maxDis) {
          // 如果定位跟上次定位距离相差小于10米或者时速超过80公里就不记录了
          return;
        }
        commit('setDistance', dis);
      }

      const time = dayjs(timestamp).format('HH:mm:ss');
      commit('setRoute', { lng, lat, time, accuracy: accuracy.toFixed(0), speed });
      commit('setCenter', { lng, lat });
      commit('setPosition', { lng, lat });

      let coord = [lng, lat];
      if (position.coordsType === 'gcj02') {
        const point = GPS.gcj_decrypt_exact(lat, lng);
        coord = [point.lon, point.lat];
      } else if (['bd09', 'bd09ll'].includes(position.coordsType)) {
        const { lat: gcjLat, lon: gcjLon } = GPS.bd_decrypt(lat, lng);
        const point = GPS.gcj_decrypt_exact(gcjLat, gcjLon);
        coord = [point.lon, point.lat];
      }
      commit('setCoordinates', coord);

      preTime = curTime;

      setStorage(state.patrolInfoKey, {
        taskId: state.patrolTask.id,
        coordinates: state.coordinates,
        routeArray: state.routeArray,
        distance: state.distance
      });
    };

    if (window.plus && plus.os.name === 'Android' && state.patrolTask.xjStartTime) {
      // 监测GPS定位有没有数据，最多检查20秒
      const xjStartTime = state.patrolTask.xjStartTime;
      const debugTime = +new Date(xjStartTime);
      const intervalTimer = setInterval(() => {
        const lastPoint = an_getLastKnownLocation();

        if (lastPoint || +new Date() - debugTime > 20000) {
          const watchId = watchPosition(successFn);
          commit('setWatchId', watchId);
          clearInterval(intervalTimer);
        }
      }, 2000);
    } else {
      const watchId = watchPosition(successFn);
      commit('setWatchId', watchId);
    }
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
