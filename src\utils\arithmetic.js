// 浮点数四则运算
export default function accFactory(method = '+') {
  return function(...nums) {
    // 将传入的参数转换为Number类型，并过滤掉不是Number类型的结果
    nums = nums.map(Number).filter(num => num || num === 0);
    // 如果过滤后的结果是长度为1的数组，那就返回数组的第一项
    // 如果过滤后的结果为空数组，则返回0
    if (nums.length < 2) return nums[0] || 0;

    if (method === 'plus') return (parseFloat(nums.slice(1).reduce((prev, num) => prev + num, nums[0])) * 100) / 100 || 0;
    else if (method === 'minus') return (parseFloat(nums.slice(1).reduce((prev, num) => prev - num, nums[0])) * 100) / 100 || 0;
    else if (method === 'times') return (parseFloat(nums.slice(1).reduce((prev, num) => prev * num, nums[0])) * 100) / 100 || 0;
    else if (method === 'div') return (parseFloat(nums.slice(1).reduce((prev, num) => prev / num, nums[0])) * 100) / 100 || 0;
  };
}

// 加
export const accAdd = accFactory('plus');
// 减
export const accSub = accFactory('minus');
// 乘
export const accMul = accFactory('times');
// 除
export const accDiv = accFactory('div');

export function getAxisMax(value) {
  const { min, max } = value;
  if (min === -Infinity || max === Infinity) return;
  const diffNum = accSub(max, min);
  if (diffNum === 0) {
    return accAdd(max, 1);
  }
  let spiltNum = accDiv(diffNum, 4);
  let scaleMax = accAdd(max, spiltNum);
  if (diffNum > 0.8 && max > 9) {
    scaleMax = Math.ceil(scaleMax);
  } else {
    const [_, digit] = (max + '').split('.');
    const scaleDigit = Math.pow(10, digit == null ? 0 : digit.length);
    scaleMax = accDiv(Math.ceil(accMul(scaleMax, scaleDigit)), scaleDigit);
  }
  return scaleMax;
}

export function getAxisMin(value) {
  const { min, max } = value;
  if (min === -Infinity || max === Infinity) return;
  const diffNum = accSub(max, min);
  // 最大值和最小值相等
  if (diffNum === 0) {
    return accSub(min, 1);
  }
  // 把数值范围均分为4等份
  let spiltNum = accDiv(diffNum, 4);
  let scaleMin = accSub(min, spiltNum);
  if (min >= 0 && scaleMin < 0) {
    return 0;
  }
  if (diffNum > 0.8 && min > 9) {
    scaleMin = Math.floor(scaleMin);
  } else {
    const [_, digit] = (min + '').split('.');
    const scaleDigit = Math.pow(10, digit == null ? 0 : digit.length);
    scaleMin = accDiv(Math.floor(accMul(scaleMin, scaleDigit)), scaleDigit);
  }
  return scaleMin;
}
