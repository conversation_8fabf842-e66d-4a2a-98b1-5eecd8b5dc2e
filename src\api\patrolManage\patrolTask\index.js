// 巡检任务
import http from '@/utils/apiRequestType';
import apiUrl from '@/utils/apiUrl';

const { defaultUrl } = apiUrl;

/**
 * @description: 新增巡检任务
 * @param {object} data
 * @return {object}
 */
export function addTask(data) {
  return http.post(defaultUrl + '/ydxc/task/addTask', data);
}

/**
 * @description: 删除巡检任务
 * @param {number[]} data
 * @return void
 */
export function delTaskById(data) {
  return http.post(defaultUrl + '/ydxc/task/delTaskById', data);
}

/**
 * @description: 修改状态
 * @param {object} params
 * @param {number} params.taskId
 * @param {number} params.xjStatus 1：执行中 2：已完成
 * @return {object}
 */
export function editXjStatus(params) {
  return http.get(defaultUrl + '/ydxc/task/editXjStatus', params);
}

/**
 * @description: 根据taskId获取任务情况 0没提交 1已提交情况
 * @param {object} params
 * @param {number} params.taskId
 * @return {object}
 */
export function getStnByTaskId(params) {
  return http.get(defaultUrl + '/ydxc/task/getStnByTaskId', params);
}

/**
 * @description: 获取任务详情 包括检查情况
 * @param {object} params
 * @param {number} params.taskId
 * @return {object}
 */
export function getTaskInfoById(params) {
  return http.get(defaultUrl + '/ydxc/task/getTaskInfoById', params);
}

/**
 * @description: 获取巡检对象
 * @return {object}
 */
export function getTaskObjList() {
  return http.get(defaultUrl + '/ydxc/task/getTaskObjList', {});
}

/**
 * @description: 获取隐患情况列表
 * @param {object} data
 * @return {object}
 */
export function getYhStnList(data) {
  return http.post(defaultUrl + '/ydxc/task/getYhStnList', data);
}

/**
 * @description: 巡检路线新增
 * @param {object} data
 * @return {object}
 */
export function addRoute(data) {
  return http.post(defaultUrl + '/ydxc/route/addRoute', data);
}

/**
 * @description: 巡检路线删除
 * @param {array} data
 * @return {object}
 */
export function delRoute(data) {
  return http.post(defaultUrl + '/ydxc/route/delRoute', data);
}

/**
 * @description: 巡检路线编辑
 * @param {object} data
 * @return {object}
 */
export function editRoute(data) {
  return http.post(defaultUrl + '/ydxc/route/editRoute', data);
}

/**
 * @description: 查询巡检路线
 * @param {object} params
 * @param {number} params.taskId
 * @return {object}
 */
export function getRouteByTaskId(params) {
  return http.get(defaultUrl + '/ydxc/route/getRouteByTaskId', params);
}
