<template>
  <!-- 问题定位 -->
  <main class="main">
    <CustomNavBar />

    <baidu-map ref="baiduMap" class="map" show-marker :center="center" @onMapReady="onMapReady"></baidu-map>
  </main>
</template>

<script>
import BaiduMap from '@/components/BaiduMap';
export default {
  name: 'ProblemMap',
  components: {
    BaiduMap
  },
  props: {},
  data() {
    return {
      isMapReady: false,
      center: { lng: 113.384429, lat: 23.35814 }
    };
  },
  computed: {},
  watch: {},
  created() {
    const param = this.$route.query;
    if (param.lng && param.lat) {
      this.center = { lng: param.lng, lat: param.lat };
    }
  },
  mounted() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
}
.map {
  width: 100%;
  height: 100%;
}
</style>
