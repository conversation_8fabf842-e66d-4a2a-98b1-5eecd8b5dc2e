<template>
  <div class="index">
    <CustomNavBar />

    <div class="main">
      <div class="title">
        我要巡查
        <!-- <span v-if="showSetting" class="setting" @click="toPage('/instruction')">(手机巡查设置)</span> -->
      </div>
      <div class="text-with-icon icon-stress text-large text-margin-max">
        巡视检查要求
      </div>
      <div class="text-with-icon icon-tab text-margin-min">
        日常巡查（巡查责任人）
      </div>
      <div class="text-with-icon text-mini text-margin-max">
        每个站点每天至少开展2次检查工作。
      </div>
      <div class="text-with-icon icon-tab text-margin-min">定期检查</div>
      <div class="text-with-icon text-mini text-margin-max">
        分所每月至少进行一次检查
      </div>
      <div class="text-with-icon icon-tab text-margin-min">不定期抽查</div>
      <div class="text-with-icon text-mini">不定期开展检查工作。</div>
    </div>
    <div class="bottom-btn">
      <van-button type="info" :block="true" :round="true" @click="toPage('/patroltaskAdd')">进入</van-button>
    </div>
  </div>
</template>

<script>
import { Dialog } from 'vant';
export default {
  computed: {},
  props: {},
  data() {
    return {
      title: '我要巡查',
      showSetting: true
    };
  },
  watch: {},

  methods: {
    onClickLeft() {
      this.$router.back();
    },
    toPage(path) {
      this.$router.push({ path });
    }
  },
  created() {
    if (window.plus) {
      this.showSetting = plus.os.name !== 'iOS';
    }
  },
  mounted() {
    const _this = this;
    this.$nextTick(async () => {
      if (window.plus) {
        if (plus.os.name == 'Android') {
          await this.$store.dispatch('location/checkPermissionByAndroid');
          await this.$store.dispatch('location/checkGPSByAndroid');
          await this.$store.dispatch('location/checkNotifyByAndroid');

          const callback = () => {
            // Dialog.confirm({
            //   title: '提示',
            //   message: '为了更准确记录巡查轨迹，请设置手机必要项！'
            // })
            //   .then(() => {
            //     _this.$router.push({ path: '/instruction' });
            //   })
            //   .catch(() => {
            //     // on cancel
            //   });
          };
          await this.$store.dispatch('location/checkPowerByAndroid', callback);
        }
        if (plus.os.name == 'iOS') {
          await this.$store.dispatch('location/checkGPSByIos');
          // await this.$store.dispatch('location/checkNotifyByIos');
        }
      }
    });
  }
};
</script>

<style lang="scss" scoped>
// @import '@/views/components/common';
@import './index';
</style>
