<template>
  <!-- 移动巡查 -->
  <main class="main flex-column">
    <CustomNavBar @click-left="onClickLeft" />
    <div class="flex-1 patrol-manage">
      <CustomTabBar :tab-bar-list="tabBarList" />
    </div>
  </main>
</template>

<script>
export default {
  name: 'PatrolManage',
  components: {},
  props: {},
  data() {
    return {
      active: 0,
      tabBarList: [
        {
          id: 'patrolTask',
          label: '巡检任务',
          icon: require('@/assets/images/patrol/task.png'),
          actIcon: require('@/assets/images/patrol/task-active.png'),
          to: {
            name: 'TaskList'
          }
        },
        {
          id: 'patrolProblem',
          label: '巡检问题',
          icon: require('@/assets/images/patrol/problem.png'),
          actIcon: require('@/assets/images/patrol/problem-active.png'),
          to: {
            name: 'ProblemList'
          }
        },
        {
          id: 'patrolUnit',
          label: '巡检单位',
          icon: require('@/assets/images/patrol/unit.png'),
          actIcon: require('@/assets/images/patrol/unit-active.png'),
          to: {
            name: 'UnitList'
          }
        }
      ]
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    onClickLeft() {
      this.$router.push({
        name: 'Index'
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
  .patrol-manage {
    overflow: hidden;
  }
}
</style>
