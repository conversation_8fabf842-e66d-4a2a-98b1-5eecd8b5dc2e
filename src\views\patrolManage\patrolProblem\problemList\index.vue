<template>
  <!-- 巡查问题列表 -->
  <main class="main flex-column">
    <CustomSearch v-model="searchForm.xjName" show-action @search="onSearch" @cancel="showFilterPopup = true" />
    <!-- <section class="section flex-1">
      <ListCell />
    </section> -->
    <van-pull-refresh ref="content" class="section flex-1" v-model="refresh" @refresh="onSearch">
      <van-list class="content-list" v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
        <ListCell v-for="data in dataList" :key="data.id" :data="data" @click.native="toPage(data)" />
      </van-list>
    </van-pull-refresh>
    <van-popup v-model="showFilterPopup" position="right" :style="{ height: '100%', width: '60%' }">
      <ListFilter
        v-model="searchForm"
        @on-choose-time="chooseTime"
        @on-confirm="confirmFilterEvent"
        @on-cancel="showFilterPopup = false"
      />
    </van-popup>

    <TimeSelect
      v-model="showTimePopup"
      type="date"
      format-string="YYYY-MM-DD"
      :time="searchForm[timeKey]"
      @confirm="confirmTimeEvent"
    />
  </main>
</template>

<script>
import CustomSearch from '@/components/CustomSearch';
import ListCell from './listCell.vue';
import ListFilter from './listFilter.vue';
import TimeSelect from '@/views/earlyWarning/components/TimeSelect.vue';
import { pageStnYhList } from '@/api/patrolManage/patrolProblem/index.js';
export default {
  name: 'ProblemList',
  components: {
    CustomSearch,
    ListCell,
    ListFilter,
    TimeSelect
  },
  props: {},
  data() {
    return {
      searchForm: {
        xjName: '',
        stnHdWay: null,
        beginTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      dataList: [],
      showFilterPopup: false,
      showTimePopup: false,
      timeKey: '',
      refresh: false,
      loading: false, // 列表是否处于加载状态
      finished: true // 列表是否加载完成
    };
  },
  computed: {},
  watch: {},
  created() {
    this.loadData();
  },
  mounted() {},
  methods: {
    // 搜索事件
    onSearch() {
      this.dataList = [];
      this.searchForm.pageNum = 1;
      this.total = 0;
      this.loadData();
    },
    // 加载列表
    onLoad() {
      if (this.dataList.length >= this.total) {
        this.finished = true;
      } else {
        this.searchForm.pageNum += 1;
        this.loadData();
      }
    },
    loadData() {
      pageStnYhList({
        ...this.searchForm
      }).then(res => {
        if (res.status === 200 && res.data) {
          const { total, list } = res.data;
          this.dataList.push(...list);
          this.total = total;
        }

        if (this.dataList.length >= this.total) {
          this.finished = true;
        } else {
          this.finished = false;
        }
        this.loading = false;
        this.refresh = false;
      });
    },
    chooseTime(key) {
      this.timeKey = key;
      this.showTimePopup = true;
    },
    confirmTimeEvent(value) {
      this.$set(this.searchForm, this.timeKey, value);
      const { beginTime, endTime } = this.searchForm;
      if (beginTime && endTime) {
        if (this.$dayjs(beginTime).diff(endTime) > 0) {
          this.$set(this.searchForm, 'beginTime', endTime);
          this.$set(this.searchForm, 'endTime', beginTime);
        }
      }
    },
    confirmFilterEvent() {
      this.showFilterPopup = false;
      this.onSearch();
    },
    toPage(row) {
      this.$router.push({
        name: 'ProblemHandling',
        query: { id: row.id, stnHdWay: row.stnHdWay }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  height: 100%;
}
.section {
  padding: 32px 32px 0;
  overflow: auto;
  background: $bg-page-gray;
}
</style>
