// 巡查问题列表
import http from '@/utils/apiRequestType';
import apiUrl from '@/utils/apiUrl';

const { defaultUrl } = apiUrl;

/**
 * @description: 问题隐患列表
 * @param {object} data
 * @return {object}
 */
export function pageStnYhList(data) {
  return http.post(defaultUrl + '/ydxc/stn/pageStnYhList', data);
}

/**
 * @description: 单个任务情况列表
 * @param {object} params
 * @param {number} params.taskId
 * @return {object}
 */
export function getStnList(params) {
  return http.get(defaultUrl + '/ydxc/stn/getStnList', params);
}

/**
 * @description 查询问题详情
 * @param {object} params
 * @param {number} params.id
 * @return {object}
 */
export function getStnInfo(params) {
  return http.get(defaultUrl + '/ydxc/stn/getStnInfo', params);
}

/**
 * @description 情况上报
 * @param {object} data
 * @return void
 */
export function addStn(data) {
  return http.post(defaultUrl + '/ydxc/stn/addStn', data);
}

/**
 * @description 删除
 * @param {number[]} data
 * @return {object}
 */
export function delStnById(data) {
  return http.post(defaultUrl + '/ydxc/stn/delStnById', data);
}

/**
 * @description 隐患情况
 * @param {object} data
 * @return {object}
 */
export function editStn(data) {
  return http.post(defaultUrl + '/ydxc/stn/editStn', data);
}
