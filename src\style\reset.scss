/* http://meyerweb.com/eric/tools/css/reset/   v2.0 | 20110126   License: none (public domain) */
html,
body,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  box-sizing: border-box;
  padding: 0;
  margin: 0;

  /* border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline; */
}
div {
  box-sizing: border-box;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
body {
  line-height: 1;
}
blockquote,
q {
  quotes: none;
}
blockquote::before,
blockquote::after,
q::before,
q::after {
  content: '';
  content: none;
}
table {
  border-spacing: 0;
  border-collapse: collapse;
}
html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  overflow: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}
h1,
h2,
h3,
h4,
h5,
h6,
em,
i {
  font-style: normal;
  font-weight: normal;
}
ul,
ol,
li {
  list-style-type: none;
}
a {
  color: #666666;
  text-decoration: none;
  outline: 0;
}
a:hover {
  text-decoration: none;
}
img {
  content: normal !important;
}
textarea::input-placeholder,
input::input-placeholder {
  /* WebKit browsers */
  font-size: 24px;
}
textarea::placeholder,
input:placeholder {
  /* Mozilla Firefox 4 to 18 */
  font-size: 24px;
}
textarea::placeholder,
input::placeholder {
  /* Mozilla Firefox 19+ */
  font-size: 24px;
}
textarea::input-placeholder,
input:input-placeholder {
  /* Internet Explorer 10+ */
  font-size: 24px;
}
