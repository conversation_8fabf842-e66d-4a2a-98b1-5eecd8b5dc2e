import $appAjax from '@/utils/apiRequestType';
import { getStorage, setStorage } from '@/utils/storage';
import baiduConfig from '@/utils/baiduConfig';
// import $appApiUrl from '@/utils/apiUrl';

const state = {
  weather: '',
  temperature: '',
  time: null
};

const mutations = {
  setWeather(state, { weather, temperature }) {
    state.weather = weather;
    state.temperature = temperature;
    state.time = +new Date();
  }
};

const actions = {
  async getWeather({ commit }, cityid = '450100') {
    console.log('getWeather', cityid);
    const weatherInfo = getStorage('weatherInfo');
    if (weatherInfo) {
      commit('setWeather', {
        weather: weatherInfo.text,
        temperature: weatherInfo.temp
      });
      return;
    }

    let ak = '';
    let mcode = '';
    if (window.plus) {
      const param = baiduConfig[window.plus.os.name];
      if (param) {
        ak = param.ak;
        mcode = param.mcode;
      }
    }
    if (!ak) return;

    return $appAjax
      .getPlus(`https://api.map.baidu.com/weather/v1/?district_id=${cityid}&data_type=all&ak=${ak}&mcode=${mcode}`)
      .then(res => {
        if (res.status === 0) {
          const { now } = res.result;
          if (now) {
            // 每小时获取一次数据
            const hour = 60 * 60 * 1000;
            setStorage('weatherInfo', now, hour);
            commit('setWeather', {
              weather: now.text,
              temperature: now.temp
            });
          }
        }
      });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
