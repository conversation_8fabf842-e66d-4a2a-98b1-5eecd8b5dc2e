<template>
  <div class="task-cell">
    <div class="cell-content">
      <van-image class="content-img" :src="data.imgUrl" @click.stop="showBigImg(data.imgUrl)" alt="巡检图片" />
      <div class="content-detail flex-1">
        <div class="detail-title">{{ data.xjName }}</div>
        <div class="detail-descript">{{ data.stnPmRefer }}</div>
      </div>
    </div>
    <div class="flex-vc j-sb">
      <span class="text-vice">{{ data.stnFdTime }}</span>
      <div class="flex-vc">
        <van-tag class="c-tag" :type="statusColorConfig[data.stnHdWay] || 'danger'" plain>
          {{ handingConfig[data.stnHdWay] || '待处理' }}
        </van-tag>
        <van-tag class="c-tag" :type="typeColorConfig[data.xjType]">
          {{ typeConfig[data.xjType] }}
        </van-tag>
      </div>
    </div>
  </div>
</template>

<script>
import { ImagePreview } from 'vant';
import { handingOptions, typeOptions } from '../../options';
export default {
  name: 'ListCell',
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    showBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    const handingConfig = handingOptions.reduce((acc, cur) => {
      acc[cur.value] = cur.label;
      return acc;
    }, {});
    return {
      handingConfig: handingConfig,
      statusColorConfig: {
        0: 'danger',
        1: 'success',
        2: 'warning'
      },
      typeColorConfig: {
        1: 'primary',
        2: 'primary',
        3: 'danger'
      }
    };
  },
  computed: {
    typeConfig() {
      let options = this.$store.getters['common/getOptions']('xjxc:type');

      if (!options.length) {
        options = typeOptions;
      }

      return options.reduce((acc, cur) => {
        acc[cur.value] = cur.label;
        return acc;
      }, {});
    }
  },
  methods: {
    showBigImg(url) {
      ImagePreview({
        images: [url]
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.task-cell {
  padding: 32px 32px 24px;
  margin-bottom: 24px;
  font-size: 28px;
  line-height: 36px;
  color: rgba($color-text-black, 0.9);
  background: $color-text-white;
  border-radius: 20px;
}
.cell-content {
  display: flex;
  margin-bottom: 32px;
  .content-img {
    width: 175px;
    height: 148px;
    margin-right: 32px;
    overflow: hidden;
    border-radius: 18px;
  }
  .detail-title {
    margin-bottom: 16px;
    font-size: 32px;
    font-weight: 500;
    line-height: 40px;
    color: $color-text-black;
  }
  .detail-descript {
    word-break: break-all;

    @include ellipsisTexts(3);
  }
}
.c-tag {
  padding: 7px 16px;
  border-radius: 8px;
  & ~ .c-tag {
    margin-left: 20px;
  }
}
.text-vice {
  color: rgba($color-text-black, 0.6);
}
.text-danger {
  color: $color-error;
}
.text-success {
  color: $color-success;
}
</style>
