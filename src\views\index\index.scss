* {
  box-sizing: border-box;
}
.index {
  height: 100%;
  overflow-y: overlay;
  background: url('~@/assets/images/index/bg.png') no-repeat 0 0;
  background-color: $bg-page;
  background-attachment: local;
  background-size: 100% 404px;
  &::-webkit-scrollbar {
    display: none; /* 对于Chrome, Safari */
  }
  &::v-deep {
    .van-nav-bar.van-hairline--bottom::after {
      border-bottom-width: 0;
    }
  }
  .nav {
    background: transparent;
    background: url('~@/assets/images/index/bg.png') no-repeat 0 0;
    background-size: 100% 404px;
    height: 12.267vw;
    ::v-deep {
      .van-nav-bar__left {
        font-size: $font36;
        font-weight: 700;
      }
      .nav-right {
        display: flex;
        align-items: center;
        color: $color-text-white;
        .nav-right-icon {
          width: 48px;
          height: 48px;
          margin-right: 8px;
        }
        .nav-right-text {
          font-size: $font36;
          font-weight: 700;
          .nav-right-text_unit {
            font-size: $font24;
            font-weight: 500;
          }
        }
      }
    }
  }
  .main {
    position: relative;
    height: 100%;
    padding: 216px 30px 0;
    .greet {
      position: absolute;
      top: 30px;
      right: 30px;
      padding: 15px 24px;
      font-size: 24px;
      font-weight: 500;
      color: #1249cc;
      background: url('~@/assets/images/index/text-bg.png') no-repeat 0 0;
      background-size: 100% 100%;
    }
    .user {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 33px 53px;
      color: $color-text-black;
      background: url('~@/assets/images/index/user-bg.png') no-repeat 0 0;
      background-size: 100% 100%;
      .user-icon {
        width: 70px;
        height: 70px;
        margin-right: 36px;
        border-radius: 50%;
      }
      .user-name {
        font-size: $font30;
        font-weight: 500;
      }
      .user-info {
        flex: 1;
        font-size: $font24;
        color: $color-text-gray1;
        text-align: right;
        .info-time {
          margin-top: 8px;
          color: $color-text-black;
        }
      }
    }
    .menu {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin: 30px 0;
      .menu-item {
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: center;
        width: 25%;
        margin-bottom: 26px;
        &:nth-child(n + 5) {
          margin-bottom: 0;
        }
        .menu-item-icon {
          box-sizing: border-box;
          width: 76px;
          height: 76px;
          padding: 15px;
          overflow: hidden;
          border-radius: 15px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        span {
          font-size: 28px;
          color: $color-text-black;
        }
      }
    }
    .info-card {
      padding: 30px 0;
      margin-top: 20px;
      border-top: 1px solid $border-color1;
      &-title {
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 32px;
        font-size: 32px;
        font-weight: 700;
        color: #000000;
        &::before {
          width: 14px;
          height: 30px;
          margin-right: 15px;
          content: '';
          background: linear-gradient(180deg, #1c73f7 0%, #00d4d4 100%);
          border-radius: 6px;
          box-shadow: 0 -1px 4px 0 #0083cf inset;
        }
      }
      .sta-content {
        img {
          width: 32px;
          height: 32px;
          margin-right: 16px;
        }
        span {
          margin: 0 20px;
          font-size: 38px;
          color: #02a7f0;
        }
      }
    }
    .basic-info .info-card-content {
      display: grid;
      grid-template-columns: 55% 45%;
      gap: 30px 5px;
      .info-item {
        &-label {
          font-size: 28px;
          font-weight: 500;
          color: #3b3a3a;
        }
        &-value {
          font-size: 28px;
          font-weight: 500;
          color: $color-text-black;
        }
      }
    }
    .patrol-info .info-card-content {
      .patrol-summary {
        justify-content: space-around;
        height: 300px;
        .patrol-summary-chart {
          width: 40%;
          height: 100%;
        }
        .patrol-summary-desc {
          padding: 24px;
          background-color: aliceblue;
          border-radius: 10px;
          &-item {
            margin-bottom: 30px;
            img {
              width: 36px;
              height: 36px;
              margin-right: 10px;
              vertical-align: sub;
            }
            &:last-child {
              margin-bottom: 0;
            }
            .patrol-summary-num {
              margin: 0 8px;
              font-size: 32px;
              font-weight: 500;
              color: $color-text-active;
            }
          }
        }
      }
      .patrol-count {
        &-item {
          display: flex;
          justify-content: space-between;
          margin-top: 30px;
          .item-left {
            font-size: 28px;
            font-weight: 500;
            color: $color-text-black;
            img {
              width: 36px;
              height: 36px;
              margin-right: 10px;
              vertical-align: middle;
            }
          }
          .item-right {
            gap: 15px;
            font-size: 28px;
            .patrol-count-num {
              margin: 0 5px;
              font-size: 32px;
              font-weight: 500;
              color: $color-text-active;
            }
            .van-progress {
              width: 200px;
            }
          }
        }
      }
    }
    .repair-info .info-card-content {
      position: relative;
      width: 100%;
      .legend-part {
        position: absolute;
        top: 50%;
        right: 0;
        padding-left: 40px;
        pointer-events: none;
        transform: translateY(-50%);
        .tip {
          margin-bottom: 16px;
          font-size: 38px;
        }
        .legend-item {
          margin-bottom: 32px;
          &:last-child {
            margin-bottom: 0;
          }
          .legend-left {
            margin-right: 60px;
            .legend-color {
              width: 20px;
              height: 20px;
              margin-right: 14px;
              border-radius: 50%;
            }
            .legend-title {
              font-size: 28px;
            }
          }
          .legend-num {
            font-size: 30px;
          }
        }
      }
      .repair-summary-chart {
        width: 100%;
        height: 280px;
        margin-right: 30px;
      }
    }
  }
}
