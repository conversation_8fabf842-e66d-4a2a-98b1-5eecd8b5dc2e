<template>
  <!-- 新增巡检任务 -->
  <main class="main flex-column">
    <CustomNavBar />
    <section class="section flex-1">
      <baidu-map
        ref="baiduMap"
        class="map"
        show-polyline
        show-marker
        :center="center"
        :polyline="polyline"
        :show-start-point="true"
        :start-point="startPoint"
        :end-point="endPoint"
        :show-end-point="true"
        @onMapReady="onMapReady"
        :zoom="zoom"
      ></baidu-map>
    </section>

    <div class="bottom-panel">
      <div class="panel-detail">
        <div class="detail-info flex-vc">
          <div class="info-item flex-1 flex-column">
            <div class="info-value">{{ currentTime }}</div>
            <div>时间</div>
          </div>
          <div class="info-item flex-1 flex-column">
            <div class="info-value">{{ currentDistance }} km</div>
            <div>路程</div>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<script>
import BaiduMap from '@/components/BaiduMap';
import { getGcxcDetail } from '@/api/patrol';

export default {
  name: 'PatrolDetail',
  components: {
    BaiduMap
  },
  props: {},
  data() {
    this.timer = null;
    return {
      isMapReady: false,
      taskId: null,
      currentTime: '', // 时间
      currentDistance: '', // 路程
      polyline: [],
      center: { lng: 113.384429, lat: 23.35814 },
      zoom: 17,
      startPoint: {},
      endPoint: {}
    };
  },

  methods: {
    async getDetail(BMap, map) {
      const { data } = await getGcxcDetail({
        id: this.taskId
        // id: 86
      });
      // 巡检耗时
      this.currentTime = data.xjhs;
      // 路程
      if (data.distance >= 10) {
        this.currentDistance = (data.distance / 1000).toFixed(2);
      } else {
        this.currentDistance = 0;
      }
      // 巡检路线
      // data.rcglInspectionRouteList = [
      //   {
      //     lgtd: 113.373566,
      //     lttd: 23.145106
      //   },
      //   {
      //     lgtd: 113.266233,
      //     lttd: 23.158309
      //   },
      //   {
      //     lgtd: 113.334775,
      //     lttd: 23.114547
      //   },
      //   {
      //     lgtd: 113.290592,
      //     lttd: 23.39287
      //   }
      // ];

      const polyline = data.rcglInspectionRouteList
        .filter(item => item.lgtd)
        .map(item => {
          return {
            lng: item.lgtd,
            lat: item.lttd
          };
        });

      this.polyline = polyline;

      const { zoom, center } = map.getViewport(polyline);
      this.center = center;
      this.zoom = zoom;
      this.startPoint = polyline[0] || {};
      this.endPoint = polyline.slice(-1)[0] || {};

      this.walk(0);
    },

    walk(idx) {
      if (this.polyline[idx]) {
        this.timer = setTimeout(() => {
          this.center = this.polyline[idx];
          this.walk(++idx);
        }, 500);
      }
    },

    clearTimer() {
      this.timer && clearTimeout(this.timer);
    },

    onMapReady(BMap, map) {
      this.isMapReady = true;

      const param = this.$route.params;
      if (param.taskId) {
        this.taskId = +param.taskId;
        this.getDetail(BMap, map);
      }
    }
  },
  beforeDestroy() {
    this.timer && clearTimeout(this.timer);
  }
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
}
.section {
  position: relative;
  padding-bottom: 132px;
  overflow: hidden;
  .map {
    width: 100%;
    height: 100%;
  }
}
.top-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  left: 20px;
  z-index: 9999;
  .btn {
    width: 180px;
    height: 70px;
  }
}
.bottom-panel {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  font-size: 28px;
  background: linear-gradient(to bottom, transparent 0%, $bg-page 60%);
  .panel-detail {
    box-sizing: border-box;
    padding: 40px 32px 36px;
    margin: 0 20px;
    margin-bottom: 20px;
    background: rgba($color-primary, 0.9);
    border-radius: 18px;
    .detail-info {
      color: $color-text-white;
      .info-item {
        align-items: center;
        color: rgba($color-text-white, 0.8);
        .info-value {
          margin-bottom: 8px;
          font-size: 32px;
          font-weight: 500;
          line-height: 40px;
          color: $color-text-white;
        }
      }
    }
    .action-item {
      padding: 15px 0;
      font-weight: 500;
      color: $color-text-black;
      background: $bg-page;
      border-radius: 18px;
      &:first-child {
        margin-right: 24px;
      }
      .item-icon {
        width: 70px;
        height: 70px;
        margin-right: 8px;
      }
    }
  }
  .panel-note {
    padding: 0 46px;
    font-size: 20px;
    line-height: 28px;
    color: rgba($color-text-black, 0.6);
    text-align: center;
  }
  .panel-btn {
    box-sizing: border-box;
    padding: 16px 46px 24px;
    .btn {
      height: 98px;
      font-size: 34px;
      font-weight: 500;
    }
  }
}
</style>
