import apiUrl from '@/utils/apiUrl';
import appAjax from '@/utils/apiRequestType';
const defaultUrl = apiUrl.defaultUrl;

/**
 * 获取隐患记录列表 - 分页
 * @param data
 * "projectId": 工程id
 */
export function getDangerRecordList(data) {
  return appAjax.post(
    `${defaultUrl}/safetyHiddenDangerLedger/getYhtzList?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  );
}

/**
 * 保存隐患记录
 * @param data
 */
export function saveDangerRecord(data) {
  return appAjax.post(defaultUrl + '/safetyHiddenDangerLedger/saveYhtz', data);
}

/**
 * 获取隐患统计数
 * @param data
 */
export function getDangerRecordCount(data) {
  return appAjax.post(defaultUrl + '/safetyHiddenDangerLedger/currentDayChart', data);
}
