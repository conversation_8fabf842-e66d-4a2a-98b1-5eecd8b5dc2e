import Vue from 'vue';
// 请按需引入，减少代码体积
import {
  Lazyload,
  Collapse,
  CollapseItem,
  ActionSheet,
  Calendar,
  NavBar,
  Button,
  Tab,
  Tabs,
  Col,
  Row,
  Icon,
  Stepper,
  Switch,
  Field,
  Dialog,
  Popup,
  Picker,
  ImagePreview,
  Uploader,
  Radio,
  RadioGroup,
  DatetimePicker,
  SwipeCell,
  List,
  Search,
  PullRefresh,
  Cell,
  CellGroup,
  Swipe,
  SwipeItem,
  Empty,
  Loading,
  IndexBar,
  IndexAnchor,
  Image as VanImage,
  Progress,
  Grid,
  GridItem,
  DropdownMenu,
  DropdownItem,
  Step,
  Steps,
  Tag,
  Card,
  Tabbar,
  TabbarItem,
  Form,
  Checkbox,
  CheckboxGroup,
  Popover,
  Notify,
  Badge
} from 'vant';

Vue.use(Lazyload);
Vue.use(Collapse);
Vue.use(CollapseItem);
Vue.use(ActionSheet);
Vue.use(Calendar);
Vue.use(IndexBar);
Vue.use(IndexAnchor);
Vue.use(NavBar);
Vue.use(Button);
Vue.use(Tab);
Vue.use(Tabs);
Vue.use(Row);
Vue.use(Col);
Vue.use(Icon);
Vue.use(Stepper);
Vue.use(Switch);
Vue.use(Field);
Vue.use(Dialog);
Vue.use(Popup);
Vue.use(Picker);
Vue.use(Uploader);
Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(DatetimePicker);
Vue.use(ImagePreview);
Vue.use(SwipeCell);
Vue.use(Empty);
Vue.use(List);
Vue.use(Search);
Vue.use(PullRefresh);
Vue.use(Cell);
Vue.use(CellGroup);
Vue.use(Swipe);
Vue.use(SwipeItem);
Vue.use(Loading);
Vue.use(VanImage);
Vue.use(Progress);
Vue.use(Grid);
Vue.use(GridItem);
Vue.use(DropdownMenu);
Vue.use(DropdownItem);
Vue.use(Step);
Vue.use(Steps);
Vue.use(Tag);
Vue.use(Tabbar);
Vue.use(TabbarItem);
Vue.use(Form);
Vue.use(Checkbox);
Vue.use(CheckboxGroup);
Vue.use(Card);
Vue.use(Popover);
Vue.use(Notify);
Vue.use(Badge);
