<template>
  <div class="page-index danger-upload">
    <van-nav-bar :title="isAdd ? '保洁上报' : '保洁详情'" fixed right-text="返回" @click-right="onClickLeft" />
    <div class="main">
      <van-form ref="formRef" class="form-card-box" :show-error="false">
        <van-field
          v-model="formData.dailyUserName"
          label="保洁人"
          placeholder="请输入"
          input-align="right"
          required
          :rules="[{ required: true, message: '请输入保洁人' }]"
          error-message-align="right"
          :show-error-message="false"
          :readonly="!isAdd"
        />
        <van-field
          readonly
          clickable
          input-align="right"
          name="dailyStartTime"
          :value="formData.dailyStartTime"
          label="保洁开始时间"
          placeholder="点击选择时间"
          required
          :rules="[{ required: true, message: '请选择开始时间' }]"
          error-message-align="right"
          :show-error-message="false"
          @click-input="showTimePopup('dailyStartTime')"
        />
        <van-field
          readonly
          clickable
          input-align="right"
          name="dailyEndTime"
          :value="formData.dailyEndTime"
          label="保洁结束时间"
          placeholder="点击选择时间"
          required
          :rules="[{ required: true, message: '请选择结束时间' }]"
          error-message-align="right"
          :show-error-message="false"
          @click-input="showTimePopup('dailyEndTime')"
        />

        <div v-for="(area, index) in formData.areas" :key="index">
          <div v-for="(areaChild, cIndex) in area.areaSmalls" :key="cIndex">
            <van-field
              :label="areaChild.name"
              input-align="right"
              required
              :rules="[{ required: true, message: '请选择' }]"
              error-message-align="right"
              :show-error-message="false"
            >
              <template #input>
                <van-radio-group v-model="areaChild.status" direction="horizontal" :disabled="!isAdd">
                  <van-radio name="保洁完成">保洁完成</van-radio>
                  <van-radio name="不涉及">不涉及</van-radio>
                </van-radio-group>
              </template>
            </van-field>
          </div>

          <van-field :label="`${area.dailyArea}-保洁后图片`">
            <template #input>
              <CustomUpload
                v-model="area.attachments"
                list-type="picture"
                @input="afterUploadPic"
                :style="{ 'pointer-events': isAdd ? 'all' : 'none' }"
              />
            </template>
          </van-field>
        </div>
      </van-form>
    </div>
    <BottomBtn v-if="isAdd" confirmText="保存" cancelText="返回" @cancel="onClickLeft" @confirm="onSubmit" />

    <!-- <van-popup v-model="showPicker" position="bottom">
      <van-picker show-toolbar value-key="label" :columns="typeOptions" @confirm="onPickerConfirm" @cancel="showPicker = false" />
    </van-popup> -->
    <van-popup v-model="showTimePicker" position="bottom">
      <van-datetime-picker
        v-model="currentTime"
        type="datetime"
        v-bind="timeProps"
        @confirm="onTimeConfirm"
        @cancel="showTimePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import BottomBtn from '@/components/BottomBtn.vue';
import CustomUpload from '@/components/CustomUpload/index.vue';
import { addWeedOrCleanApi, getCleanPartListApi, getCleanDetailApi } from '@/api/repair';
import { getStorage } from '@/utils/storage';

export default {
  name: 'WeedDetail',
  components: {
    BottomBtn,
    CustomUpload
  },
  props: {},
  data() {
    const isAndroid = /android/i.test(navigator.userAgent);
    return {
      capture: isAndroid ? 'camera' : null,
      userInfo: {},
      isAdd: false,
      formData: {
        wrpcd: '',
        dailyUserName: '',
        dailyStartTime: '',
        dailyEndTime: '',
        tabType: 1,
        areas: [],
        appKey: 'QSZSKJZ'
      },
      showPicker: false,
      showTimePicker: false,
      timeType: '',
      timeProps: {},
      currentTime: new Date(),
      typeOptions: [
        // { label: '设施检修', value: 1 },
        // { label: '设备维护', value: 2 },
        // { label: '清淤疏浚', value: 3 },
        // { label: '防渗防漏', value: 4 }
      ]
    };
  },
  computed: {
    ...mapGetters('common', ['getOptions'])
  },
  watch: {},
  methods: {
    onClickLeft() {
      if (!this.isAdd) {
        this.back();
        return;
      }
      this.$dialog
        .confirm({
          title: '提示',
          message: '上报信息尚未保存，是否确认返回？'
        })
        .then(() => {
          this.back();
        })
        .catch(() => {});
    },
    back() {
      this.$router.go(-1);
    },
    onPickerConfirm(val) {
      this.formData.wxyhlx = val.value;
      this.showPicker = false;
    },
    async getCleanPartList() {
      const res = await getCleanPartListApi();
      if (res.status === 200) {
        const areas = res.data.map(item => {
          return {
            id: item.id,
            dailyArea: item.name,
            attachments: [],
            areaSmalls: item.children && item.children.length ? item.children : [item]
          };
        });
        this.$set(this.formData, 'areas', areas);
      }
    },
    async getCleanDetail(id) {
      const res = await getCleanDetailApi(id);
      if (res.data && res.status === 200) {
        this.formData = res.data;
      }
    },
    showTimePopup(type) {
      if (!this.isAdd) return;
      this.showTimePicker = true;
      this.timeType = type;
      this.currentTime = this.formData[type] ? new Date(+this.$dayjs(this.formData[type])) : new Date();
    },
    onTimeConfirm(val) {
      this.formData[this.timeType] = this.$dayjs(val).format('YYYY-MM-DD HH:mm:ss');
      this.showTimePicker = false;
    },
    afterUploadPic(file) {
      console.log(file, 'pic');
      if (file.length) {
        // this.formData.wxyhhId = file.map(item => item.id).join(',');
      }
    },
    onSubmit() {
      this.$refs.formRef.validate().then(async valid => {
        this.$set(this.formData, 'wrpcd', this.userInfo?.assignWrpcdList[0] || '');
        this.$set(this.formData, 'tabType', 1); //杂草护理2 日常保洁1
        this.$set(this.formData, 'appKey', 'QSZSKJZ');
        const res = await addWeedOrCleanApi(this.formData);
        if (res.status === 200) {
          this.$userApp.toast.show({
            text: `上报成功`,
            type: 'text'
          });
          this.$router.back();
        }
      });
    },
    resetForm() {
      this.formData = {
        wrpcd: this.userInfo?.assignWrpcdList[0] || '',
        dailyUserName: '',
        dailyStartTime: '',
        dailyEndTime: '',
        tabType: 1,
        areas: [],
        appKey: 'QSZSKJZ'
      };
    }
  },
  created() {
    this.userInfo = getStorage('userInfo') || {};

    this.getCleanPartList();

    const params = this.$route.params;
    const { type, ...rest } = params;

    this.isAdd = type === 'add';
    if (!this.isAdd) {
      //编辑
      this.getCleanDetail(params.id);
    } else {
      //新增
      this.resetForm();
    }
  },
  mounted() {}
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
.danger-upload {
  padding-bottom: 134px;
  color: $color-text-main;
  background-color: $bg-page1;
  .basic-date {
    padding: 30px 20px;
    font-size: 28px;
    font-weight: 500;
  }
  .main {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 15px 15px 0;
    overflow-y: auto;
    .form-card-box {
      margin-bottom: 14px;
      background: $bg-page;
      border-radius: 30px;
      .input-box {
        &:last-child {
          border-bottom: none;
        }
      }
      &::v-deep {
        .van-cell {
          background: none;
        }
      }
    }
  }
}
.index-readonly {
  padding-bottom: 0;
}
</style>
