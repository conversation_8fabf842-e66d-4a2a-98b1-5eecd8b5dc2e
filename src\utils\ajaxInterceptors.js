import Vue from 'vue';
import AjaxPlugin from 'axios';
import store from '@/store';

export default function ajaxInterceptors() {
  // AjaxPlugin.defaults.withCredentials = true;
  let isHideLoading = false;
  //添加一个api请求拦截器
  AjaxPlugin.interceptors.request.use(
    config => {
      //添加loding中组件
      //设置isHide不需要显示loading
      isHideLoading = (config.params && config.params.isHide) || config.headers.isHide;
      if (!isHideLoading) {
        Vue.$userApp.loading.show();
      }
      return config;
    },
    function(error) {
      Vue.$userApp.loading.hide();
      return Promise.reject(error);
    }
  );
  //添加一个响应拦截器
  AjaxPlugin.interceptors.response.use(
    function(response) {
      !isHideLoading && Vue.$userApp.loading.hide();
      return response;
    },
    function(error) {
      !isHideLoading && Vue.$userApp.loading.hide();
      const data = error.response?.data || {};
      if (data.status === 401) {
        Vue.$userApp.toast.show({
          text: 'token值失效',
          type: 'text',
          time: 2000
        });
        store.dispatch('user/logout').then(() => {
          setTimeout(() => router.push({ path: '/login' }), 1000);
        });
      }
      return Promise.resolve(error.response);
    }
  );
}
