<template>
  <div class="task-cell">
    <div class="cell-content van-hairline--bottom">
      <van-image class="content-img" :src="data.imgUrl" @click.stop="showBigImg(data.imgUrl)" alt="巡检图片" />
      <div class="content-detail">
        <div class="detail-title">{{ data.name }}</div>
        <div class="detail-vice">{{ data.area }}</div>
        <div class="detail-descript">
          <span>隐患情况：</span>
          <span :class="[data.status ? 'text-danger' : 'text-success']">{{ data.status ? '无隐患' : '有隐患' }}</span>
        </div>
        <div class="detail-descript">
          <span>用时路程：</span>
          <span>{{ data.time || '0分' }}/{{ data.lucheng || 0 }}km</span>
        </div>
      </div>
    </div>
    <div class="cell-footer">
      <div>{{ data.date }}</div>
      <div>{{ data.person }}</div>
    </div>
  </div>
</template>

<script>
import { ImagePreview } from 'vant';
export default {
  name: 'TaskReservoirCell',
  props: {},
  data() {
    return {
      data: {
        name: '某水库名称的文字',
        area: '汕头市潮阳区',
        status: 0,
        lucheng: 0.4,
        date: '2023-12-04',
        person: '陈某某'
      },
      options: [
        {
          label: '巡检类型',
          prop: 'type'
        },
        {
          label: '隐患情况',
          prop: 'situation'
        },
        {
          label: '巡检人员',
          prop: 'person'
        },
        {
          label: '巡检时间',
          prop: 'time'
        }
      ]
    };
  },
  methods: {
    showBigImg(url) {
      ImagePreview({
        images: [url]
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.task-cell {
  padding: 32px 32px 18px;
  margin-bottom: 24px;
  font-size: 28px;
  line-height: 36px;
  color: rgba($color-text-black, 0.9);
  background: $color-text-white;
  border-radius: 20px;
}
.cell-content {
  display: flex;
  padding-bottom: 20px;
  margin-bottom: 10px;
  .content-img {
    width: 213px;
    height: 180px;
    margin-right: 32px;
    overflow: hidden;
    border-radius: 18px;
  }
  .detail-title {
    margin-bottom: 8px;
    font-size: 32px;
    font-weight: 500;
    line-height: 40px;
    color: $color-text-black;
  }
  .detail-vice {
    margin-bottom: 16px;
    color: rgba($color-text-black, 0.6);
  }
  .detail-descript {
    margin-bottom: 8px;
  }
}
.cell-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba($color-text-black, 0.6);
}
.text-danger {
  color: $color-error;
}
.text-success {
  color: $color-success;
}
</style>
