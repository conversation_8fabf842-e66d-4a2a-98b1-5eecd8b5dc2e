<template>
  <div class="plan-outer-box">
    <left-region style="color: #fff" :overflow="'visiable'">
      <div class="left-content flx-column">
        <div class="plan-selector">
          <div class="plan-sel-box flx-justify-between">
            <div class="sel-box-item flx-align-center">
              <span class="label">预报方案：</span>
              <el-input
                v-model="currentPlan.forecastName"
                placeholder="请选择预报方案"
                class="c-black-input"
                :clearable="false"
                readonly
                style="width: 160px"
              />
            </div>
            <div class="sel-box-item flx-align-center">
              <span class="label">调度：</span>
              <el-select
                v-model="curDispatchId"
                placeholder="请选择调度"
                :clearable="false"
                class="c-black-el-select plan-sel"
                style="width: 130px"
                @change="changeDispatchPlan"
              >
                <el-option
                  :label="item.dispatchName"
                  :value="item.id!"
                  v-for="item in dispatchList"
                  :key="item.id"
                />
              </el-select>
            </div>
          </div>

          <div class="flx-justify-between">
            <div class="forecast-time">
              预报时段：<span>{{ forecastTimeRange }}</span>
            </div>
            <plan-select v-model="currentPlan"></plan-select>
          </div>
        </div>

        <base-title title="发布调令" />
        <dispatch-order
          :current-plan="currentPlan"
          :dispatch-rule="curDispatchId"
          :dispatch-name="curDispatchName"
          :res-code="wrpcd"
        ></dispatch-order>
      </div>

      <div class="legend-box legend-right">
        <h3>水深图例</h3>
        <div class="legend-item flx-align-center" v-for="item in deepLegendArr" :key="item.label">
          <div class="dot" :style="{ backgroundColor: item.color }"></div>
          <span>{{ item.label }}</span>
        </div>
      </div>
    </left-region>

    <right-region style="color: #fff" :overflow="'visiable'">
      <div class="left-content flx-column" style="padding-top: 40px">
        <div class="tab-btns flx-center">
          <div
            class="tab"
            :class="{ 'active-tab': activeTab === index }"
            v-for="(item, index) in tabList"
            :key="item.value"
            @click="tabChange(index)"
          >
            {{ item.label }}
          </div>
        </div>
        <div class="tab-item-box" v-show="activeTab === 0">
          <base-title title="简报" />
          <forecast-overview :current-data="planSummary"></forecast-overview>

          <base-title title="下游断面情况" />
          <dm-situation :current-data="planSummary"></dm-situation>

          <base-title title="受灾情况" />
          <disaster-situation :current-data="planSummary"></disaster-situation>
        </div>
        <div class="tab-item-box" v-show="activeTab === 1">
          <base-title title="防汛应急预案" />
          <emergency-plan
            :current-data="floodControlSupport"
            @emergency-click="manageItemClick"
          ></emergency-plan>

          <base-title title="防汛责任人" />
          <proj-responser :current-data="floodControlSupport"></proj-responser>

          <base-title title="防汛物资情况">
            <template #extra>
              <div class="flx-align-center" @click="moreMaterial">
                更多
                <el-icon style="margin-left: 4px; font-size: 16px">
                  <ArrowRight />
                </el-icon>
              </div>
            </template>
          </base-title>
          <material-part ref="materialPartRef" :current-data="floodControlSupport"></material-part>

          <base-title title="避险安置点">
            <template #extra>
              <div class="flx-align-center" @click="moreShelter">
                更多
                <el-icon style="margin-left: 4px; font-size: 16px">
                  <ArrowRight />
                </el-icon>
              </div>
            </template>
          </base-title>
          <shelter-part ref="shelterPartRef" :current-data="floodControlSupport"></shelter-part>
        </div>
      </div>

      <div class="legend-box legend-left">
        <h3>图例</h3>
        <div class="legend-item flx-align-center" v-for="item in stationList" :key="item.name">
          <img :src="item.icon" alt="" />
          <span>{{ item.name }}</span>
        </div>
      </div>
    </right-region>

    <file-preview-dialog ref="filePreviewDialogRef"></file-preview-dialog>

    <site-detail-dialog
      v-if="detailDialogShow"
      :site-info="curSiteInfo"
      @dialog-close="detailDialogShow = false"
    ></site-detail-dialog>
  </div>
</template>

// 预案
<script setup lang="ts">
import { getAssetsImg, checkNull } from '@/utils'
import { previewUrl } from '@/api/module/attachment'
import { CircleCheckFilled, ArrowRight } from '@element-plus/icons-vue'
import DispatchOrder from './components/DispatchOrder.vue'
import ForecastOverview from './components/ForecastOverview.vue'
import DisasterSituation from './components/DisasterSituation.vue'
import DmSituation from './components/DmSituation.vue'
import EmergencyPlan from './components/EmergencyPlan.vue'
import ProjResponser from './components/ProjResponser.vue'
import MaterialPart from './components/MaterialPart.vue'
import ShelterPart from './components/ShelterPart.vue'
import SiteDetailDialog from './components/SiteDetailDialog.vue'
import {
  getForecastSchemeApi,
  getPlanSummaryApi,
  getFloodControlSupportApi,
  getStationAndDataToMapApi
} from '@/api/module/fourPrevention/plan'
import { getForecastPlanApi } from '@/api/module/fourPrevention/warning'
import {
  GetForecastSchemeVO,
  GetPlanSummaryVO,
  GetFloodControlSupportVO,
  GetStationAndDataToMapVO
} from '@/api/interface/plan/VO'
import $dayjs from 'dayjs'
import { wrpcd } from '@/api/index'
import { getStationInfo } from '@/api/module/fullFactor'
import { useMap3dStore } from '@/stores/modules/map3d'
import { markerConfig, getPopupHtml } from '@/hooks/useMapMarker'
import { getSubmergeArea } from '@/api/module/fullFactor'
import { getDispatchByForecastId } from '@/api/module/fourPrevention/simulation/index'

const {
  addComPointLayer,
  removeLayer,
  removePointLayers,
  pointHeight,
  flyTo,
  updateLayer,
  addCustomGeojsonLayer,
  createTransferMgr,
  removeTransferMgr
} = useMap3dStore()
const activeTab = ref(0)
const tabList = ref([
  { label: '方案概况', value: 0 },
  { label: '防汛支撑', value: 1 }
])

const tabChange = index => {
  activeTab.value = index
}

const filePreviewDialogRef = ref()

const manageItemClick = data => {
  filePreviewDialogRef.value.previewFileList(data.data, data.index)
}

const shelterPartRef = ref()

const moreShelter = () => {
  shelterPartRef.value.showMoreDetail()
}

const materialPartRef = ref()

const moreMaterial = () => {
  materialPartRef.value.showMoreDetail()
}

const deepLegendArr = ref([
  { label: '<0.3m', color: '#C7DFFD' },
  { label: '0.3-0.5m', color: '#8CBDFD' },
  { label: '0.5-1.0m', color: '#4E8CE6' },
  { label: '1.0-2.0m', color: '#4A75D6' },
  { label: '>2.0m', color: '#3C518F' }
])

const curDispatchId = ref('')
const curDispatchName = ref('')
const dispatchList = ref<Array<any>>([])

const forecastTimeRange = ref('')

const currentPlan = ref<any>({
  id: 0,
  forecastName: ''
})

watch(
  currentPlan,
  () => {
    if (!currentPlan.value.id) return
    // console.log(currentPlan.value, '选择方案')
    forecastTimeRange.value = `${$dayjs(currentPlan.value.beginTime || '').format('MM-DD HH:mm')} ~ ${$dayjs(currentPlan.value.endTime || '').format('MM-DD HH:mm')}`
    getPlanSummary()
    getFloodControlSupport()
    getStationData()
    loadDispatchPlan()

    setTimeout(() => {
      loadSubmergeArea()
    }, 500)
  },
  {
    deep: true
  }
)

const loadDispatchPlan = async () => {
  const res = await getDispatchByForecastId({ forecastId: currentPlan.value.id })
  if (res.data.length) {
    dispatchList.value = res.data || []
    const recommendDispatch = dispatchList.value.find(i => i.recommend == 1)
    if (recommendDispatch) {
      curDispatchId.value = recommendDispatch.id
      curDispatchName.value = recommendDispatch.dispatchName
    } else {
      curDispatchId.value = dispatchList.value[0].id || ''
      curDispatchName.value = dispatchList.value[0].dispatchName || ''
    }
  }
}

const changeDispatchPlan = () => {
  curDispatchName.value =
    dispatchList.value.find(i => i.id === curDispatchId.value).dispatchName || ''
}

const planSummary = ref<GetPlanSummaryVO>()

const getPlanSummary = async () => {
  const res = await getPlanSummaryApi({ resCode: wrpcd, schemeId: Number(currentPlan.value.id) })
  if (res.data) {
    planSummary.value = res.data
  }
}

const floodControlSupport = ref<GetFloodControlSupportVO>()

const getFloodControlSupport = async () => {
  const res = await getFloodControlSupportApi({
    resCode: wrpcd,
    schemeId: Number(currentPlan.value.id)
  })
  if (res.data) {
    floodControlSupport.value = res.data
    if (res.data.geom) {
      // setFloodGeojson(res.data.geom)
    }
  }
}

const stationList = reactive<Array<any>>([
  {
    name: '水库',
    type: 'RESERVOIR',
    minDist: 50000,
    icon: getAssetsImg('common/marker/reservoir.png'),
    data: []
  },
  {
    name: '断面',
    type: 'SECTION',
    minDist: 50000,
    icon: getAssetsImg('common/marker/section.png'),
    data: []
  },
  {
    name: '安置点',
    type: 'SETTLEMENT',
    minDist: 50000,
    icon: getAssetsImg('common/marker/marker-settlement.png'),
    data: []
  },
  {
    name: '物资仓库',
    type: 'MATERIAL',
    minDist: 50000,
    icon: getAssetsImg('common/marker/marker-material.png'),
    data: []
  }
  // {
  //   name: '抢险队伍',
  //   type: 'RESCUE_TEAM',
  //   minDist: 50000,
  //   isMoveShow: false,
  //   icon: getAssetsImg('common/marker/marker-team.png'),
  //   data: []
  // }
])

const popConfig = ref({
  RESERVOIR: {
    titleKey: 'stnm',
    configList: [
      { label: '时间：', key: 'currentTm' },
      { label: '当前流量(m³/s)：', key: 'currentFlow' },
      { label: '预报流量(m³/s)：', key: 'forecastFlowRage' }
    ]
  },
  // VIDEO: {
  //   titleKey: 'stnm',
  //   configList: []
  // },
  SETTLEMENT: {
    titleKey: 'stnm',
    configList: [
      { label: '容纳人数(人)：', key: 'people' },
      { label: '联系人：', key: 'contact' },
      { label: '联系方式：', key: 'tel' }
    ]
  },
  MATERIAL: {
    titleKey: 'stnm',
    configList: []
  },
  SECTION: {
    titleKey: 'stnm',
    configList: [
      { label: '时间：', key: 'currentTm' },
      { label: '汛限水位(m)：', key: 'limitWl' },
      { label: '预报水位(m)：', key: 'forecastWlRage' }
    ]
  }
  // RESCUE_TEAM: {
  //   titleKey: 'stnm',
  //   configList: [
  //     { label: '人员人数：', key: 'teamPersons', value: 0 },
  //     { label: '队长：', key: 'teamLeader', value: '--' },
  //     { label: '联系方式：', key: 'contact', value: '--' }
  //   ]
  // }
})

let rawStationList: any = []
const curSiteInfo = ref<GetStationAndDataToMapVO>({})

const detailDialogShow = ref(false)

const getStationData = async () => {
  const res = await getStationAndDataToMapApi({
    resCode: wrpcd,
    schemeId: Number(currentPlan.value.id)
  })
  if (res.data) {
    stationList.forEach(it => (it.data = []))
    res.data.forEach(item => {
      if (item.sttp === 'SECTION' && item.sectionGeomVo) {
        //预警和预告页仅展示重点断面，故采用打点，而不是画线
        item.lgtd = item.sectionGeomVo.coordinates[0][0][0]
        item.lttd = item.sectionGeomVo.coordinates[0][0][1]
      }
      if (!item.dtmel) {
        item.dtmel = pointHeight
      }
      let target = stationList.findIndex(it => it.type === item.sttp)
      if (target !== -1 && item.lgtd && item.lttd && item.dtmel) {
        stationList[target].data.push(item)
      }
    })
    //toRaw将变量变为只读
    rawStationList = toRaw(stationList)
    rawStationList.forEach(item => {
      setPoints(item)
    })
  }
}

const getHtmlContent = layerItem => {
  let list = popConfig.value[layerItem.type]?.configList
    ?.map(item => {
      if (item.key === 'currentTm') {
        return `<div style="margin-bottom:4px;margin-top:-4px;padding-left:4px;">{${item.key}}</div>`
      } else {
        return `<div style="background-color:#00224350;margin-bottom:2px;padding:4px 8px;" ><span style="color:#7ff;">${item.label}</span>{${item.key}}</div>`
      }
    })
    ?.join('')
  return list
}

const curLayers = ref<Array<string>>([])

const setPoints = (item: any, isZoom?: boolean) => {
  let opt: any = {
    id: item.type,
    idAttr: 'stcd',
    data: item.data,
    symbol: {
      billboard: {
        image: markerConfig[item.type === 'RESERVOIR' ? 'RR' : item.type]
        // clampToGround: true
      },
      window: {
        div: getPopupHtml(getHtmlContent(item), 'title'),
        divData: {
          title: data => checkNull(data.stnm),
          currentDrp: data => checkNull(data.currentDrp),
          forecast24HDrp: data => checkNull(data.forecast24HDrp),
          forecast24HDrpLevel: data => checkNull(data.forecast24HDrpLevel),
          currentFlow: data => checkNull(data.currentFlow),
          forecastFlowRage: data => checkNull(data.forecastFlowRage),
          currentWl: data => checkNull(data.currentWl),
          limitWl: data => checkNull(data.limitWl),
          forecastWlRage: data => checkNull(data.forecastWlRage),
          currentTm: data => checkNull(data.currentTm),
          people: data => checkNull(data.people),
          contact: data => checkNull(data.contact),
          tel: data => checkNull(data.tel),
          teamPersons: data => checkNull(data.teamPersons) + '人',
          teamLeader: data => checkNull(data.teamLeader)
        },
        moveShow: true,
        pixelOffset: [0, -50]
      }
    },
    click: function (e) {
      console.log(e.overlay, '点位信息')
      if (['RESERVOIR', 'SECTION'].includes(e.overlay._attr.sttp)) {
        curSiteInfo.value = JSON.parse(JSON.stringify(e.overlay._attr))
        detailDialogShow.value = true
      }
      flyTo(e.overlay)
    },
    zoom: {
      minDist: item.minDist
    }
  }
  if (!isZoom) delete opt.zoom
  if (curLayers.value.includes(item.type)) {
    updateLayer(item.type, item.data)
  } else {
    curLayers.value.push(item.type)
    addComPointLayer(opt)
  }
}

const downstreamTabs: any[] = [
  {
    label: '重点保护对象',
    key: 'keyProtectedObject',
    icon: getAssetsImg('fullFactor/icon-flag.png'),
    activeIcon: getAssetsImg('fullFactor/icon-flag-active.png'),
    marker: 'RESCUE_TEAM',
    data: []
  },
  {
    label: '安置区',
    key: 'relocationArea',
    icon: getAssetsImg('fullFactor/icon-settlement.png'),
    activeIcon: getAssetsImg('fullFactor/icon-settlement-active.png'),
    marker: 'SETTLEMENT',
    data: []
  },
  {
    label: '转移路线',
    key: 'evacuationRoute',
    icon: getAssetsImg('fullFactor/icon-line.png'),
    activeIcon: getAssetsImg('fullFactor/icon-line-active.png'),
    marker: '',
    data: []
  }
]
// 下游点位信息
const loadSubmergeArea = () => {
  return getSubmergeArea('千年一遇').then(res => {
    const data = res.data?.[0]
    try {
      if (data) {
        for (const item of downstreamTabs) {
          if (data[item.key]) {
            item.data = JSON.parse(data[item.key])
            if (item.marker) {
              item.data.forEach(item => (item.h = pointHeight))
            }
          }
        }
        downstreamTabs.forEach(item => handleDownstreamClick(item))
        changeItem(data)
      }
    } catch (error) {
      console.log('下游点位：', error)
    }
  })
}

const selectedDsTabs = ref<string[]>([])
const transferId = 'evacuationRoute'
const handleDownstreamClick = (row: (typeof downstreamTabs)[0]) => {
  if (selectedDsTabs.value.includes(row.key)) {
    selectedDsTabs.value = selectedDsTabs.value.filter(item => item !== row.key)
    if (row.marker) {
      removeLayer(row.key)
    } else {
      removeTransferMgr(transferId)
    }
    return
  }
  selectedDsTabs.value.push(row.key)

  if (row.data.length && row.marker) {
    addComPointLayer({
      id: row.key,
      data: row.data,
      idAttr: 'name',
      posAttr: { x: 'lon', y: 'lat', z: 'h' }, //位置属性
      symbol: {
        label: {
          text: '{name}',
          maxRange: 1250
        },
        billboard: {
          image: markerConfig[row.marker]
        }
      },
      zoom: {
        minDist: 20000
      }
    })
  } else {
    const originRow = downstreamTabs[0]
    const targetRow = downstreamTabs[1]
    if (!selectedDsTabs.value.includes(originRow.key)) {
      handleDownstreamClick(originRow)
    }
    if (!selectedDsTabs.value.includes(targetRow.key)) {
      handleDownstreamClick(targetRow)
    }
    const options = {
      id: transferId, //方案ID
      target: {
        data: targetRow.data, //目标点,，在人员转移业务中为安置点数据
        idAttr: 'id',
        posAttr: { x: 'lon', y: 'lat', z: 'h' }, //位置属性
        symbol: {
          label: { text: '{name}' }, //标签类配置，详见点图层相关设置
          billboard: {
            image: markerConfig[targetRow.marker],
            width: 22,
            height: 22
          }
        }
      },
      origin: {
        //起点，在人员转移业务中为风险点数据
        data: originRow.data,
        idAttr: 'id',
        posAttr: { x: 'lon', y: 'lat', z: 'h' }, //位置属性
        targetIdAttr: 'targetId', //关联目标点的字段
        symbol: {
          label: { text: '{name}' }, //标签类配置
          billboard: {
            image: markerConfig[originRow.marker],
            width: 22,
            height: 22
          }
          // conditionSetDef: function (data) {
          //   //条件设置，目前提供以下四种默认样式，也可以参照点图层接口自行配置
          //   if (data.level == '0') {
          //     return { type: 'origin_settle1' }
          //   }
          //   if (data.level == 'low') {
          //     return { type: 'transfer_low' }
          //   }
          //   if (data.level == 'mid') {
          //     return { type: 'transfer_mid' }
          //   }
          //   if (data.level == 'high') {
          //     return { type: 'transfer_high' }
          //   }
          // }
        }
      },
      transferInfo: {
        //转移信息设置
        symbol: {
          //当需要自定义转移路线时，在转移点信息中添加route字段，放置转移路线路线（geojson数据）
          deltaHeight: 1000, //当没有路线数据，采用转移示意线时，示意线的最高点
          //line:{//转移示意线样式设置，默认是箭头图片流动线
          //color:{r:0,g:255,b:153,a:1},//颜色
          //width:10.0,//线宽
          //speed:5;//流动速度
          //},
          info: {
            //转移信息设置
            //timeInterval:100,//信息位置移动的时间间隔
            label: { text: '{transInfo}' } //标签类配置，详见点图层相关设置
          }
        }
      }
    }
    createTransferMgr(options)
  }
}

const floodMaxLayerId = 'floodMaxLayer'
const changeItem = async item => {
  removeLayer(floodMaxLayerId)
  const opt = {
    id: floodMaxLayerId,
    data: JSON.parse(item.geom),
    symbol: {
      enableFeatureLabel: true,
      featureLabel: {
        featureIdAttr: 'name',
        fillColor: { r: 255, g: 255, b: 255, a: 1 },
        outlineColor: { r: 0, g: 0, b: 0, a: 1 }, //字体描边颜色
        outlineWidth: 1, //描边宽度
        font: '14px', //字体设置
        showBackground: true, //是否显示背景颜色
        pixelOffset: [0, 0], //设置屏幕空间中距此标签原点的像素偏移量
        text: function (properties) {
          return properties.name
        }
        // minRange: 5000, //最小可视距离
        // maxRange: 200000000 //最大可视距离 53,116,210
      },

      polygon: {
        // color: { r: 255, g: 204, b: 0, a: 0.5 },
        // outlineColor: { r: 255, g: 204, b: 0, a: 1 }, 50,80,140
        color: { r: 553, g: 116, b: 210, a: 0.5 },
        outlineColor: { r: 50, g: 80, b: 140, a: 1 },
        outlineWidth: 1,
        outline: true,
        fill: true,
        minLevel: 4, //最小地图显示层级
        maxLevel: 20, //最大地图显示层级
        zIndex: 101,
        // height: 1820, //设置整体高度,
        clampToGround: true //设置不要贴地
      }
    },
    zoom: true
  }
  addCustomGeojsonLayer(opt)
}

onMounted(() => {
  // getDefaultPlan()
})

onUnmounted(() => {
  removePointLayers()
  removeLayer('SECTION')
  stationList.forEach(item => {
    removeLayer(item.type)
  })
  rawStationList = []
  downstreamTabs.forEach(item => {
    removeLayer(item.key)
  })
  removeTransferMgr(transferId)
  removeLayer(floodMaxLayerId)
})
</script>

<style lang="scss" scoped>
.plan-outer-box {
  position: relative;
  width: 100%;
  height: 100%;

  .left-content {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden scroll;
  }

  .self-title {
    width: 100%;

    .text-btn {
      font-family: 'Source Han Sans SC', sans-serif;
      font-size: 14px;
      font-weight: 400;
      color: #7ff;
      letter-spacing: 0;
      text-shadow: none;
      cursor: pointer;
    }
  }

  .plan-selector {
    padding: 14px 4px;
    background: url('@/assets/images/warning/selector-bg.png') no-repeat;
    background-position: center center;
    background-size: 100% 100%;

    .plan-sel-box {
      margin-bottom: 10px;

      .sel-box-item {
        .sel-value {
          font-size: 14px;
        }
      }

      .label {
        font-size: 14px;
        color: #7ff;
        white-space: nowrap;
      }

      :deep(.plan-sel) {
        .el-select__wrapper {
          background-color: transparent !important;
        }

        background-color: transparent !important;

        .el-select__selected-item {
          span {
            color: #fff;
          }
        }
      }
    }

    .forecast-time {
      font-size: 14px;
      color: #7ff;

      span {
        font-size: 18px;
        line-height: 18px;
        color: $color-base-white;
      }
    }

    :deep(.sel-btn) {
      margin-left: 8px;
      background: url('@/assets/images/strengthenMaintenance/active-tab.png') no-repeat;
      background-position: center center;
      background-size: 100% 100%;
      border: none;

      .el-icon {
        color: #7ff !important;
      }

      span {
        color: #7ff !important;
      }
    }
  }

  .tab-btns {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);

    .tab {
      position: relative;
      padding: 0 20px;
      font-family: YouSheBiaoTiHei, serif;
      font-size: 26px;
      line-height: 30px;
      color: #fff;
      white-space: nowrap;
      cursor: pointer;
    }

    .active-tab {
      color: transparent; /* 文字颜色设置为透明 */
      background-image: linear-gradient(0deg, #ffd060, #ffeec5);
      -webkit-background-clip: text;
      background-clip: text;

      &::after {
        position: absolute;
        bottom: -12px;
        left: 50%;
        width: 24px;
        height: 12px;
        content: '';
        background: url('@/assets/images/plan/cur-tab-icon.png') no-repeat;
        background-position: center center;
        background-size: 100% 100%;
        transform: translateX(-50%);
      }
    }
  }

  .tab-item-box {
    width: 100%;
    height: 100%;
    overflow: hidden scroll;
  }

  .legend-box {
    position: absolute;
    bottom: 0;
    width: 120px;
    padding: 10px 14px;
    font-size: 14px;
    color: $color-base-white;
    background-color: rgb(8 64 120 / 60%);

    h3 {
      margin-bottom: 12px;
      font-weight: 700;
      color: #7ff;
    }

    .legend-item {
      margin-bottom: 6px;

      &:last-child {
        margin-bottom: 0;
      }

      img {
        width: 20px;
        height: 20px;
        margin-right: 6px;
      }

      .dot {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        border: 1px solid #fff;
        border-radius: 50%;
      }
    }
  }

  .legend-right {
    right: -120px;
  }

  .legend-left {
    left: -120px;
  }
}
</style>
