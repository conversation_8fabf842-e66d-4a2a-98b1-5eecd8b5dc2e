<template>
  <!-- <van-popup v-bind="$attrs" position="right" :style="{ height: '100%', width: '60%' }"> -->
  <div class="popup-content">
    <div v-if="type !== 'reservoir'" class="card">
      <div class="card-title">任务状态</div>
      <div class="card-option">
        <div
          v-for="item in statusOptions"
          :key="item.value"
          class="option-item"
          :class="{ 'option-item_checked': item.value === formData.xjStatus }"
          @click="selectStatus(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <div v-if="type !== 'reservoir'" class="card">
      <div class="card-title">检查类型</div>
      <div class="card-option">
        <div
          v-for="item in typeOptions"
          :key="item.value"
          class="option-item"
          :class="{ 'option-item_checked': item.value === formData.xjType }"
          @click="selectType(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <div class="card">
      <div class="card-title">巡查时间</div>
      <div class="card-date">
        <van-field
          v-model="formData.beginTime"
          class="date-input"
          readonly
          right-icon="arrow-down"
          placeholder="开始时间"
          @click="chooseTime('beginTime')"
        />
        <van-field
          v-model="formData.endTime"
          class="date-input"
          readonly
          right-icon="arrow-down"
          placeholder="结束时间"
          @click="chooseTime('endTime')"
        />
      </div>
    </div>
    <div class="card" style="flex: 1">
      <div class="card-title">隐患情况</div>
      <div class="card-option">
        <div
          v-for="item in dangerOptions"
          :key="item.value"
          class="option-item"
          :class="{ 'option-item_checked': item.value === formData.yh }"
          @click="selectDanger(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <div class="popup-btn">
      <van-button @click="resetEvent" class="btn" size="large" :square="true">取消</van-button>
      <van-button @click="confirmEvent" class="btn" size="large" :square="true" type="info">确定</van-button>
    </div>
  </div>
  <!-- </van-popup> -->
</template>

<script>
import { typeOptions, statusOptions, dangerOptions } from '../../options';
export default {
  name: 'TaskFilter',
  props: {
    type: {
      type: Number,
      default: null
    },
    value: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      statusOptions: [
        {
          label: '全部',
          value: null
        },
        ...statusOptions
      ],
      dangerOptions: [
        {
          label: '全部',
          value: null
        },
        ...dangerOptions
      ]
    };
  },
  computed: {
    formData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('update:value', val);
      }
    },
    typeOptions() {
      let options = this.$store.getters['common/getOptions']('xjxc:type').map(i => {
        i.label = i.label.replace('巡检', '');
        return i;
      });

      if (!options.length) {
        options = typeOptions;
      }

      return [
        {
          label: '全部',
          value: null
        },
        ...options
      ];
    }
  },
  methods: {
    // 选择类型
    selectType(item) {
      this.formData.xjType = item.value;
    },
    // 选择问题
    selectStatus(item) {
      this.formData.xjStatus = item.value;
    },
    // 选择状态
    selectDanger(item) {
      this.formData.yh = item.value;
    },
    // 打开选择时间弹窗
    chooseTime(time) {
      this.$emit('on-choose-time', time);
    },
    // 重置筛选条件
    resetEvent() {
      // for (const key of Object.keys(this.formData)) {
      //   this.formData[key] = null;
      // }
      this.$emit('on-cancel');
    },
    // 筛选确认事件
    confirmEvent() {
      this.$emit('on-confirm');
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../common.scss';
</style>
