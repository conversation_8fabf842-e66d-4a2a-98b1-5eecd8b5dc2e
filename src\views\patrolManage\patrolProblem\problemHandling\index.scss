.handing-card {
  box-sizing: border-box;
  padding: 32px;
  font-size: 28px;
  color: rgba($color-text-black, 0.9);
  background: $bg-page;
  border-radius: 20px;
  box-shadow: 0 5px 16px 0 rgba($color-text-black, 0.06);
  .card-item {
    padding: 32px 0;
    font-size: 32px;
    line-height: 40px;
    &:last-child {
      padding-bottom: 0;
    }
    .item-title {
      margin-bottom: 16px;
      font-weight: 500;
      color: $color-text-black;
      .title-icon {
        width: 36px;
        height: 36px;
        margin-right: 16px;
      }
    }
    .item-descript {
      word-break: break-all;
    }
  }
}
.text-vice {
  font-size: 24px;
  color: rgba($color-text-black, 0.4);
}
.text-danger {
  color: $color-error;
}
.text-success {
  color: $color-success;
}
.text-primary {
  color: $color-primary;
}
