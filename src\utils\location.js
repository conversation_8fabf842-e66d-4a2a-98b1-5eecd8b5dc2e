import Vue from 'vue';
import store from '@/store';
import { isBrowser, an_checkGPS, ios_checkGPS } from '@/utils/plusPermission';
import GPS from '@/utils/gps';

const _MIN = 0.00000001;

const _locationOptions = {
  enableHighAccuracy: true,
  timeout: 600000,
  maximumAge: 0,
  geocode: false,
  // coordsType: 'bd09ll',
  // provider: 'baidu'
  // coordsType: 'gcj02', //火星坐标系（国测局）
  // provider: 'amap'
  coordsType: 'wgs84', //系统自带坐标系
  provider: 'system'
};

const _errorFn = e => {
  // 定位错误提示
  let text = '其它未知错误导致无法获取位置信息';
  switch (e.code) {
    case 1:
      text = '浏览器策略不允许程序获取定位功能';
      break;
    case 2:
      text = '无法获取有效的位置信息';
      break;
    case 3:
      text = '无法在指定的时间内获取位置信息';
      break;
    case e.UNKNOWN_ERROR:
      text = '其它未知错误导致无法获取位置信息';
      break;
  }
  // Vue.$userApp.toast.show({
  //   text,
  //   type: 'text'
  // });
};

function _getNavigator() {
  return window.plus || window.navigator;
}

function getCurrentPosition(successFn, errorFn = _errorFn, locationOptions = _locationOptions) {
  Vue.$userApp.loading.show();
  // 百度定位功能
  if (BMapGL) {
    const geolocation = new BMapGL.Geolocation();
    geolocation.getCurrentPosition(function(res) {
      if (this.getStatus() == BMAP_STATUS_SUCCESS) {
        const { lng, lat } = res.point;
        const bdPoint = new BMapGL.Point(lng, lat);
        const geo = new BMapGL.Geocoder();
        geo.getLocation(bdPoint, result => {
          // 坐标转换
          const { lat: gcjLat, lon: gcjLon } = GPS.bd_decrypt(lat, lng);
          const point = GPS.gcj_decrypt_exact(gcjLat, gcjLon);
          successFn({
            coords: {
              longitude: point.lon,
              latitude: point.lat,
              originPoint: {
                longitude: lng,
                latitude: lat
              },
              accuracy: res.accuracy
            },
            address: {
              streetNum: result.addressComponents.streetNumber,
              ...result.addressComponents
            },
            addresses: result.address
          });
        });
      } else {
        const obj = {
          6: 1,
          2: 2,
          8: 3
        };
        const code = this.getStatus();
        const error = {
          code: obj[code],
          PERMISSION_DENIED: code === 6,
          POSITION_UNAVAILABLE: code === 2,
          TIMEOUT: code === 8
        };
        errorFn(error);
      }
      Vue.$userApp.loading.hide();
    }, _locationOptions);
    return;
  }

  // ======================= 浏览器定位功能 =======================
  const _navigator = _getNavigator();
  // 判断当前设备是否支持浏览器定位功能
  if (!_navigator.geolocation) {
    Vue.$userApp.toast.show({
      text: '您的设备不支持获取地理位置',
      type: 'text'
    });
    Vue.$userApp.loading.hide();
    return;
  }

  _navigator.geolocation.getCurrentPosition(
    position => {
      Vue.$userApp.loading.hide();
      const { longitude, latitude, accuracy } = position.coords;

      if (longitude < _MIN || latitude < _MIN || accuracy === 0) {
        Vue.$userApp.toast.show({
          text: '无法获取有效的位置信息',
          type: 'text'
        });
        return;
      }
      const point = GPS.gcj_decrypt_exact(latitude, longitude);
      position.coords.longitude = point.lon;
      position.coords.latitude = point.lat;
      successFn(position);
    },
    err => {
      Vue.$userApp.loading.hide();
      errorFn(err);
    },
    {
      ...locationOptions,
      geocode: true
    }
  );
}

function watchPosition(successFn, errorFn = _errorFn, locationOptions = _locationOptions) {
  const _navigator = _getNavigator();

  if (!_navigator.geolocation) {
    Vue.$userApp.toast.show({
      text: '您的设备不支持获取地理位置',
      type: 'text'
    });
    return;
  }

  return _navigator.geolocation.watchPosition(
    position => {
      const { longitude, latitude, accuracy } = position.coords;

      if (longitude < _MIN || latitude < _MIN || accuracy === 0 || accuracy >= 100) {
        return;
      }

      successFn(position);
    },
    errorFn,
    locationOptions
  );
}

function clearWatch(watchId) {
  const _navigator = _getNavigator();
  _navigator.geolocation.clearWatch(watchId);
}

export { getCurrentPosition, watchPosition, clearWatch };
