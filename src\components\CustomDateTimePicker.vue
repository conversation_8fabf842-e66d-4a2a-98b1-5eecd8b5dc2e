<template>
  <div class="custom-datetime flex">
    <van-datetime-picker
      v-model="time"
      class="flex-5 first-picker"
      :type="type"
      format="YYYY-MM-DD HH:mm:ss"
      value-format="YYYY-MM-DD HH:mm:ss"
      v-bind="timeProps"
    >
      <template>
        <div class="cancel-btn" @click="handleCancel">取消</div>
      </template>
      <template v-if="type !== 'datetime'">
        <div class="confirm-btn" @click="popupConfirm">确认</div>
      </template>
    </van-datetime-picker>
    <van-picker
      v-if="type === 'datetime'"
      ref="secondPickerRef"
      class="flex-1 second-picker"
      :default-index="defaultIndex"
      :show-toolbar="true"
      :columns="secondColumns"
      @confirm="onSecondConfirm"
    >
      <template>
        <div class="confirm-btn" @click="popupConfirm">确认</div>
      </template>
    </van-picker>
  </div>
</template>
<script>
export default {
  name: 'CustomDateTimePicker',
  props: {
    value: {
      type: String,
      default: ''
    },
    type: { type: String, default: 'date' },
    format: { type: String, default: '' },
    minDate: { type: String, default: '' },
    mixDate: { type: String, default: '' }
  },
  data() {
    return {
      columns: [],
      formatOptions: {
        date: 'YYYY-MM-DD',
        datetime: 'YYYY-MM-DD HH:mm:ss',
        'month-day': 'MM-DD',
        'year-month': 'YYYY-MM',
        datehour: 'YYYY-MM-DD HH'
      },
      defaultIndex: 0
    };
  },

  computed: {
    time: {
      get() {
        return new Date(+this.$dayjs(this.value));
      },
      set(val) {
        this.$emit('input', this.$dayjs(val).format(this.valueFormat));
      }
    },
    valueFormat() {
      if (this.format) {
        return this.format;
      } else {
        return this.formatOptions[this.type];
      }
    },
    timeProps() {
      let attrs = {};
      if (this.minDate) {
        attrs.minDate = new Date(+this.$dayjs(this.minDate));
      }
      if (this.mixDate) {
        attrs.minDate = new Date(+this.$dayjs(this.mixDate));
      }
      return attrs;
    },
    secondColumns() {
      const time = this.$dayjs(this.time).format('YYYY-MM-DD HH:mm');
      const minOrMax = this.$dayjs(this.timeProps.minDate || this.timeProps.maxDate);
      if (minOrMax && time === this.$dayjs(minOrMax).format('YYYY-MM-DD HH:mm')) {
        const second = minOrMax.second();
        return new Array(60)
          .fill()
          .map((_, i) => (i < 10 ? '0' + i : i + ''))
          .filter(v => (this.timeType === 'zgsj' ? v >= second : v <= second));
      } else {
        return new Array(60).fill().map((_, i) => (i < 10 ? '0' + i : i + ''));
      }
    }
  },
  mounted() {
    if (this.type === 'datetime') {
      this.$nextTick(() => {
        this.defaultIndex = this.secondColumns.indexOf(this.$dayjs(this.time).format('ss'));
      });
    }
  },
  methods: {
    popupConfirm() {
      this.type === 'datetime' && this.$refs.secondPickerRef.confirm();
      this.$emit('cancel');
    },

    onSecondConfirm(val) {
      this.time = this.$dayjs(this.time).format('YYYY-MM-DD HH:mm:' + val);
    },

    handleCancel() {
      this.$emit('cancel');
    }
  }
};
</script>
<style lang="scss" scoped>
.custom-datetime {
  .flex-5 {
    flex: 5;
  }
  .cancel-btn {
    width: 100%;
    padding: 0 32px;
    font-size: 28px;
    color: #969799;
    text-align: left;
  }
  .confirm-btn {
    width: 100%;
    padding: 0 32px;
    font-size: 28px;
    color: #3e80ed;
    text-align: right;
  }
  ::v-deep .first-picker {
    .van-hairline-unset--top-bottom {
      right: 0;
    }
  }
  ::v-deep .second-picker {
    .van-picker-column__item {
      padding-left: 0;
    }
    .van-hairline-unset--top-bottom {
      left: 0;
    }
  }
}
</style>
