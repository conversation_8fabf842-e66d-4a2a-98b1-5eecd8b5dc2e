<template>
  <div class="patrol-route page-index">
    <van-nav-bar title="巡查路线" fixed right-text="返回" @click-right="onClickRight" />
    <div class="main flex-vc">
      <img src="@/assets/images/patrol/patrolRoute.png" @click="preview" />
    </div>
  </div>
</template>
<script>
import { ImagePreview } from 'vant';
export default {
  name: 'PatrolRoute',
  data() {
    return {};
  },
  methods: {
    onClickRight() {
      this.$router.back();
    },
    preview() {
      ImagePreview({
        images: [require('@/assets/images/patrol/patrolRoute.png')],
        overlayStyle: { background: 'rgba(255, 255, 255, 1)' },
        showIndex: false
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.patrol-route {
  box-sizing: border-box;
  .main {
    height: 100%;
    padding: 0 20px;
    img {
      width: 100%;
      height: auto;
    }
  }
}
</style>
