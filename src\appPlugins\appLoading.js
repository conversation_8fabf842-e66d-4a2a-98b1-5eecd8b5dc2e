import AppLoading from '../components/appComponents/AppLoading';
const appLoading = {
  install: Vue => {
    let loadingTpl = Vue.extend(AppLoading);
    // 2、创建实例，挂载到文档以后的地方
    const instance = new loadingTpl();
    // 3、把创建的实例添加到body中
    instance.$mount(document.createElement('div'));
    // 挂载实例到我们创建的DOM上
    document.body.appendChild(instance.$el);
    const loading = {
      show: val => {
        instance.text = val || '加载中';
        instance.show = true;
      },
      hide: () => {
        instance.show = false;
      }
    };
    if (!Vue.$userApp) {
      Vue.$userApp = {
        loading
      };
    } else {
      Vue.$userApp.loading = loading;
    }
    Vue.mixin({
      created: function() {
        this.$userApp = Vue.$userApp;
      }
    });
  }
};
export default appLoading;
