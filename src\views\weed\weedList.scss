* {
  box-sizing: border-box;
}
.repair-list-page {
  color: $color-text-main;
  .main {
    height: 100%;
    padding: 15px 15px 0;
    background: url('~@/assets/images/dangerRecord/bg.png') no-repeat 0 0;
    background-size: 100% 100%;
    .nav {
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 15px 0 0;
      .nav-item {
        flex: 1;
        text-align: center;
        .item-icon {
          width: 66px;
          height: 66px;
          margin-bottom: 8px;
        }
        .item-text {
          font-size: 26px;
          font-weight: 500;
          color: $color-text-black;
        }
      }
    }
    .content {
      flex: 1;
      padding: 12px 16px;
      overflow: hidden;
      background: $bg-page;
      border-radius: 30px;
      &::v-deep {
        .tab-title {
          font-size: 30px;
          font-weight: 500;
          background-image: linear-gradient(180deg, #f5f7fb 0%, #fbfcfe 100%);
        }
        .van-tab--active {
          background: $bg-page;
        }
        .van-tabs__nav {
          height: 70px;
          overflow: hidden;
          border-radius: 30px 30px 0 0;
        }
      }
      .content-detail {
        // height: calc(100% - 95px);
        height: 100%;
        padding: 10px;
        overflow: auto;
        background: $bg-page1;
        border: 1px solid $border-color1;
        border-radius: 30px;
        -webkit-overflow-scrolling: touch;
        .detail-item {
          // display: flex;
          padding: 16px;
          margin-bottom: 10px;
          font-size: 28px;
          font-weight: 500;
          background: $bg-page;
          border: 1px solid $border-color1;
          border-radius: 30px;
          &:last-child {
            margin-bottom: 0;
          }
          .main-info {
            margin-bottom: 12px;
          }
          .info-left {
            font-size: 32px;
            color: #30b3e9;
            img {
              width: 40px;
              height: 40px;
              margin-right: 12px;
            }
          }
          .repair-man {
            flex: 1;
            margin-bottom: 20px;
            font-size: 28px;
            color: #333333;
            white-space: nowrap;
          }
          .photo-list {
            gap: 16px;
            width: 100%;
            padding-top: 12px;
            padding-bottom: 4px;
            overflow: scroll hidden;

            // &::-webkit-scrollbar-thumb {
            //   background-color: #3177ec70 !important; /* 应用于滚动条滑块的颜色 */
            // }
            &::-webkit-scrollbar {
              display: none; /* 对于Chrome, Safari */
            }
            .photo-item {
              min-width: 140px;
              max-width: 140px;
              height: 68px;
              overflow: hidden;
              cursor: pointer;
              border-radius: 12px;
            }
          }
          .no-data {
            padding-left: 20px;
            color: #999999;
          }
          .icon-time {
            background: url('~@/assets/images/dangerRecord/time.png') no-repeat left top;
          }
          .icon-person {
            background: url('~@/assets/images/dangerRecord/person.png') no-repeat left top;
          }
          .icon-setting {
            background: url('~@/assets/images/dangerRecord/setting.png') no-repeat left top;
          }
          .tip-icon {
            width: 28px;
            height: 28px;
            margin-right: 10px;
            background-size: 100% 100%;
          }
        }
      }
    }
  }
}
