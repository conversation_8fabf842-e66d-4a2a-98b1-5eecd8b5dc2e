<template>
  <div class="maintenance-detail flex-column">
    <!--    <div class="std-detail-header flex j-sb mb20">-->
    <!--      &lt;!&ndash; <div class="title"> &ndash;&gt;-->
    <!--      &lt;!&ndash; <el-button style="margin-right: 20px;" v-if="btnShow" type="primary" class="el-icon-back" @click="back">返回</el-button> &ndash;&gt;-->
    <!--      &lt;!&ndash; <h1 style="font-weight: 700;">{{ title }}</h1> &ndash;&gt;-->
    <!--      &lt;!&ndash; </div> &ndash;&gt;-->
    <!--      &lt;!&ndash; <el-radio-group v-model="searchForm.range" @input="loadData">-->
    <!--        <el-radio-button label="week">近7天</el-radio-button>-->
    <!--        <el-radio-button label="month">近30天</el-radio-button>-->
    <!--        <el-radio-button label="year">近一年</el-radio-button>-->
    <!--      </el-radio-group> &ndash;&gt;-->
    <!--    </div>-->
    <el-radio-group v-model="activeTab" class="tab-group">
      <el-radio-button label="plan">养护计划</el-radio-button>
      <el-radio-button label="maintenance">维修养护</el-radio-button>
    </el-radio-group>
    <div class="std-detail-header flex j-sb mb20"></div>
    <div v-if="activeTab === 'plan'">
      <div class="maintenance-plan">
        <!-- 查询条件 -->
        <el-form :inline="true" class="search-form">
          <el-form-item label="年度:">
            <el-date-picker
              v-model="searchFormPlan.year"
              type="year"
              placeholder="选择年份"
              value-format="yyyy"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="项目类型:">
            <el-select
              v-model="searchFormPlan.projectType"
              placeholder="请选择"
            >
              <el-option
                v-for="item in projectTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleSearch"
              >查询</el-button
            >
            <el-button @click="handleReset">重置</el-button>
            <el-button type="primary" icon="el-icon-plus" @click="handleAdd"
              >新增</el-button
            >
          </el-form-item>
        </el-form>

        <!-- 表格 -->
        <free-table
          :data="tableData"
          :column="tableColumn"
          showPagination
          background
          :total="paginationPlan.total"
          :current-page="paginationPlan.page"
          @page-change="loadData"
        >
          <template #attachments="{ row }">
            <div v-if="row.attachments.length" class="file-list">
              <el-link
                v-for="file in row.attachments"
                :key="file.id"
                @click="handlePreview(file)"
                >{{ file.mc }}</el-link
              >
            </div>
          </template>
          <template #actions="{ row }">
            <el-button type="text" @click="handleEdit(row)">修改</el-button>
            <el-button type="text" @click="handleDelete(row)">删除</el-button>
          </template>
        </free-table>

        <!-- 新增/编辑对话框 -->
        <el-dialog
          :title="dialogTitle"
          :visible.sync="dialogVisible"
          width="50%"
          top="0"
          @close="handleDialogClose"
          :center="true"
        >
          <el-form
            :model="formDataPlan"
            :rules="formRules"
            ref="formRef"
            label-width="120px"
          >
            <el-form-item label="计划年度" prop="year">
              <el-date-picker
                v-model="formDataPlan.year"
                type="year"
                placeholder="选择年份"
                value-format="yyyy"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="项目类型" prop="projectType">
              <el-select
                v-model="formDataPlan.projectType"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in projectTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="具体内容" prop="content">
              <el-input
                type="textarea"
                v-model="formDataPlan.content"
              ></el-input>
            </el-form-item>
            <el-form-item label="频率" prop="frequency">
              <el-select v-model="formDataPlan.frequency" placeholder="请选择">
                <el-option
                  v-for="item in frequencys"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="计划执行次数" prop="planTimes">
              <el-input v-model.number="formDataPlan.planTimes"></el-input>
            </el-form-item>
            <el-form-item label="计划经费(万元)" prop="planFunds">
              <el-input v-model="formDataPlan.planFunds"></el-input>
            </el-form-item>
            <el-form-item label="附件" prop="attachments">
              <TaskUpload
                module="plan"
                :showTip="true"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.rar,.zip,.png,.jpg"
                tip="支持扩展名：pdf、doc、docx、xls、xlsx、rar、zip、png、jpg且文件不超过100M"
                :readonly="false"
                :fileList.sync="formDataPlan.attachments"
              />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                v-model="formDataPlan.remark"
              ></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
    <!-- 维修养护 -->
    <div v-if="activeTab === 'maintenance'">
      <div class="detail-content flex-1">
        <div class="md-chart flex">
          <div class="md-chart-item flex-1 mr20">
            <div v-show="!pieEmpty" ref="chartPie" class="chart"></div>
            <div
              v-show="pieEmpty"
              class="chart-empty flex-column j-center a-center"
            >
              <img src="@/assets/basic/empty.png" />
              <span>暂无统计数据</span>
            </div>
          </div>
          <div ref="chartLine" class="md-chart-item flex-1"></div>
        </div>
        <div class="md-table flex-column" style="flex: 1; overflow: hidden">
          <form-item-comp
            ref="searchForm"
            :form-config="searchFormJson"
            :data="searchForm"
            :listTypeInfo="listTypeInfo"
            :refObj="{}"
          >
            <template slot="form-btn">
              <div class="search-btn">
                <el-button
                  class="el-icon-search"
                  type="primary"
                  @click="loadDataMaintenance"
                  >查询</el-button
                >
                <el-button @click="reset">重置</el-button>
                <el-button
                  type="primary"
                  class="el-icon-plus"
                  @click="edit('add')"
                  >新增</el-button
                >
                <el-button plain icon="el-icon-download" @click="exports"
                  >导出</el-button
                >
              </div>
            </template>
          </form-item-comp>

          <free-table
            height="100%"
            :data="projectData"
            :column="projectColumns"
            showPagination
            background
            paginationPosition="bottom"
            :total="pagination.total"
            :current-page.sync="pagination.page"
            @page-change="loadProject"
          >
            <template #sfzc="{ row }">
              {{ selectDict(row.sfzc, "sfzcList") }}
            </template>
            <template #wxyhlx="{ row }">
              {{ selectDict(row.wxyhlx, "wxyhlxList") }}
            </template>
            <template #fjFileList="{ row }">
              <div v-if="row.fjFileList.length" class="file-list">
                <el-link
                  v-for="file in row.fjFileList"
                  :key="file.id"
                  @click="handlePreview(file)"
                  >{{ file.mc }}</el-link
                >
              </div>
            </template>
            <template #wxyhhFileList="{ row }">
              <div v-if="row.wxyhhFileList.length">
                <el-link
                  class="file-item"
                  v-for="file in row.wxyhhFileList"
                  :key="file.id"
                  @click="handlePreview(file)"
                  ><img class="table-img" :src="previewUrl + file.file_path"
                /></el-link>
              </div>
            </template>
            <template #actions="{ row }">
              <el-button
                type="text"
                class="el-icon-edit"
                @click="edit('edit', row)"
                >编辑</el-button
              >
              <el-button
                type="text"
                class="el-icon-delete"
                @click="delProject(row.id)"
                >删除</el-button
              >
            </template>
          </free-table>
        </div>
      </div>
    </div>
    <MaintenanceDialog
      ref="maintenanceDialog"
      :visible="dialogOptions.show"
      :title="dialogOptions.title"
      :formData.sync="formData"
      :listTypeInfo="listTypeInfo"
      @on-save="refresh"
      @on-close="resetDialogForm"
    />
    <FilePreviewDialog ref="filePreviewDialog" />
  </div>
</template>
<script>
import { mapState } from "vuex";
import FormItemComp from "@/components/elCommon/searchForm/searchForm.vue";
import FreeTable from "@/components/elCommon/freeTable";
import FilePreviewDialog from "@/components/elCommon/FilePreviewDialog.vue";
import MaintenanceDialog from "./maintenanceDialog.vue";
import utils from "@/utils/utils";
import { previewUrl } from "@/api/attachment";
import { getStorage } from "@/utils/storage";
import {
  detailList,
  statisticsWxyh,
  deleteById,
  exportList,
  getMaintenancePlanList,
  deleteMaintenancePlan,
  updateMaintenancePlan,
  addMaintenancePlan,
} from "@/api/OperationManagement/maintenance";
import { getAllOptionList } from "@/api/attachment";
import { downloadFileByBlob } from "@/utils/utils";
import { getDictData } from "@/api/system/dict";
import TaskUpload from "@/components/elCommon/taskUpload.vue";

const options = {
  week: 7,
  month: 30,
  year: 12,
};
export default {
  name: "MaintenanceDetail",
  components: {
    TaskUpload,
    FormItemComp,
    FreeTable,
    FilePreviewDialog,
    MaintenanceDialog,
  },
  props: {
    record: {
      type: Object,
    },
  },
  data() {
    return {
      activeTab: "plan",
      previewUrl,
      detailRecord: {},
      wxyhlxLoaded: false,

      // 养护计划
      searchFormPlan: {
        year: "",
        projectType: "",
      },
      projectTypes: [],
      frequencys: [],
      tableData: [],
      tableColumn: [
        { label: "序号", type: "index" },
        { label: "计划年度", prop: "year" },
        { label: "项目类型", prop: "projectTypeName" },
        { label: "具体内容", prop: "content" },
        { label: "频率", prop: "frequencyName" },
        { label: "计划执行次数", prop: "planTimes" },
        { label: "计划经费（万元）", prop: "planFunds" },
        { label: "附件", prop: "attachments", slotScope: true },
        { label: "备注", prop: "remark" },
        { label: "操作", prop: "actions", slotScope: true, fixed: "right" },
      ],
      paginationPlan: {
        total: 0,
        page: 1,
      },
      dialogVisible: false,
      dialogTitle: "",
      formDataPlan: {
        year: "",
        projectType: "",
        content: "",
        frequency: "",
        planTimes: null,
        planFunds: null,
        remark: "",
        projectTypeName: "",
        frequencyName: "",
        attachments: [],
      },
      formRules: {
        year: [{ required: true, message: "请输入计划年度", trigger: "blur" }],
        projectType: [
          { required: true, message: "请选择项目类型", trigger: "change" },
        ],
        content: [
          { required: true, message: "请输入具体内容", trigger: "blur" },
        ],
        frequency: [{ required: true, message: "请输入频率", trigger: "blur" }],
        planTimes: [
          { required: true, message: "请输入计划执行次数", trigger: "blur" },
          {
            type: "number",
            message: "计划执行次数必须为数字",
            trigger: "blur",
          },
        ],
        planFunds: [
          { required: true, message: "请输入计划经费", trigger: "blur" },
          {
            validator: (_, value, callback) => {
              if (!/^\d+(\.\d+)?$/.test(value)) {
                callback(new Error("计划经费必须为数字"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      currentRow: null,

      // 维修养护
      searchForm: {
        range: "week",
      },
      searchFormJson: {
        id: "form",
        className: "el-form-component",
        fieldList: [
          {
            label: "维修项目名称：",
            value: "wxyhxm",
            type: "el-input",
            clearable: true,
          },
          {
            label: "时间：",
            value: "time",
            type: "el-date-picker",
            dataType: "daterange",
            clearable: true,
            rangeSeparator: "~",
            startPlaceholder: "开始时间",
            endPlaceholder: "结束时间",
            defaultTime: ["00:00:00", "23:59:59"],
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            width: "450px",
          },
          // slot插槽
          {
            label: "",
            value: "btn",
            type: "slot",
            colWidth: 18,
          },
        ],
      },
      projectColumns: [
        { label: "序号", type: "index", width: 60, fixed: "left" },
        { label: "维修项目名称", prop: "wxyhxm" },
        { label: "维修养护类型", prop: "wxyhlx", slotScope: true, width: 100 },
        { label: "地点", prop: "dd" },
        { label: "时间", prop: "sj" },
        { label: "负责人", prop: "fzr", width: 80 },
        { label: "是否正常", prop: "sfzc", slotScope: true, width: 80 },
        { label: "附件", prop: "fjFileList", slotScope: true },
        { label: "维修养护后图片", prop: "wxyhhFileList", slotScope: true },
        { label: "操作", prop: "actions", slotScope: true, width: 120 },
      ],
      projectData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
      },
      lineChart: null,
      pieChart: null,
      pieEmpty: false,
      dialogOptions: {
        show: false,
        title: "",
        type: "add",
      },
      formData: {},
      listTypeInfo: {
        wxyhlxList: [],
        sfzcList: [
          { label: "是", value: 1 },
          { label: "否", value: 0 },
        ],
      },
    };
  },
  mounted() {
    this.loadData();
  },
  watch: {
    activeTab(val) {
      if (val === "plan") {
        this.initDataPlan();
      } else if (val === "maintenance") {
        this.enterMaintenanceTab();
      }
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    ...mapState({
      projectInfo: (state) =>
        Object.keys(state.project.projectInfo).length === 0
          ? getStorage("std-projectInfo")
          : state.project.projectInfo,
    }),
    selectDict() {
      return (value, dictName) => {
        return (
          this.listTypeInfo[dictName].find((item) => item.value === value)
            ?.label || value
        );
      };
    },
    btnShow() {
      return this.userInfo.assignwrpcdList.length > 0 ? false : true;
    },

    title() {
      let text = "";
      if (this.userInfo.assignwrpcdList?.length) {
        text = this.projectInfo?.projectName;
      }
      return text;
    },
  },
  methods: {
    initDataPlan() {
      this.loadData();
    },

    async loadData(page = 1) {
      this.paginationPlan.page = page;
      // 这里调用接口获取数据
      const params = {
        ...this.searchFormPlan,
        pageNum: this.paginationPlan.page,
        pageSize: 10,
      };
      try {
        const res = await getMaintenancePlanList(params);
        if (res.status === 200) {
          this.tableData = res.data.list || [];
          this.paginationPlan.total = res.data.total || 0;
        } else {
          this.$message.error("获取数据失败");
        }
      } catch (error) {
        this.$message.error("获取数据失败");
      }
    },

    handleSearch() {
      this.loadData();
    },

    handleReset() {
      this.searchFormPlan = {
        year: "",
        projectType: "",
      };
      this.loadData();
    },

    handleAdd() {
      this.dialogTitle = "新增";
      this.dialogVisible = true;
      this.currentRow = null;
      this.$nextTick(() => {
        this.$refs.formRef.resetFields();
      });
    },
    handleEdit(row) {
      this.dialogTitle = "编辑";
      this.dialogVisible = true;
      this.currentRow = row;
      this.$nextTick(() => {
        this.$refs.formRef.resetFields();
        Object.assign(this.formDataPlan, {
          ...row,
          year: row.year ? String(row.year).substring(0, 4) : "",
          projectType: row.projectType ? String(row.projectType) : "",
          frequency: row.frequency ? String(row.frequency) : "",
        });
      });
    },
    handleDelete(row) {
      this.$confirm("确认删除该记录?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const res = await deleteMaintenancePlan({ id: row.id });
            if (res.status === 200) {
              this.$message.success("删除成功");
              this.loadData();
            } else {
              this.$message.error("删除失败");
            }
          } catch (error) {
            this.$message.error("删除失败");
          }
        })
        .catch(() => {});
    },
    handleDialogClose() {
      this.currentRow = null;
    },
    handleSubmit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          try {
            let res;
            console.log(this.formDataPlan, "this.formDataPlan");

            if (this.currentRow) {
              // 编辑
              res = await updateMaintenancePlan({
                ...this.formDataPlan,
                id: this.currentRow.id,
                attachmentId: this.formDataPlan.attachments
                  ?.map((i) => i.id)
                  ?.join(","),
              });
            } else {
              // 新增
              res = await addMaintenancePlan({
                ...this.formDataPlan,
                attachmentId: this.formDataPlan.attachments
                  ?.map((i) => i.id)
                  ?.join(","),
              });
            }
            if (res.status === 200) {
              this.$message.success("操作成功");
              this.dialogVisible = false;
              this.loadData();
            } else {
              this.$message.error("操作失败");
            }
          } catch (error) {
            this.$message.error("操作失败");
          }
        }
      });
    },

    getOption(code, key) {
      getDictData({ code }).then((res) => {
        if (res.status === 200 && Array.isArray(res.data)) {
          const list = res.data[0]
            ? Array.isArray(res.data[0].children)
              ? res.data[0].children
              : []
            : [];
          const data = list.map((i) => ({
            label: i.dictName,
            value: i.dictValue,
          }));
          this[key] = data;
        }
      });
    },

    // 返回
    back() {
      this.$emit("back");
    },
    loadDataMaintenance() {
      this.loadChartData();
      this.loadProject();
    },
    loadChartData() {
      const [beginDate, endDate] = this.searchForm.time || [];
      statisticsWxyh({
        wrpcd: this.record.wrpcd,
        beginDate,
        endDate,
        // type: this.searchForm.range === "year" ? "month" : "day",
        // dayValue: options[this.searchForm.range],
      }).then((res) => {
        if (res.status === 200) {
          this.pieEmpty = !Object.keys(res.data.pieChart)?.length;
          this.$nextTick(() => {
            this.drawLineChart(res.data.lineChart);
            if (!this.pieEmpty) {
              this.drawPieChart(res.data.pieChart);
            }
          });
        }
      });
    },
    drawLineChart(lineData) {
      // let { xData, data1, data2 } = Object.keys(lineData).reduce(
      //   (obj, key) => {
      //     console.log("obj, key",obj, key);
      //     obj.xData.push(key);
      //     lineData[key]?.forEach((item) => {
      //       obj[item.status == 1 ? "data1" : "data2"].push(item.count || 0);
      //     });
      //     return obj;
      //   },
      //   { xData: [], data1: [], data2: [] }
      // );
      this.$nextTick(() => {
        const chartDom = this.$refs.chartLine;
        if (!chartDom) {
          console.warn("折线图容器不存在");
          return;
        }

        // 销毁旧的实例
        if (this.lineChart) {
          this.lineChart.dispose();
          this.lineChart = null;
        }

        // 初始化新的图表
        this.lineChart = this.$echarts.init(chartDom);

        let xData = [],
          data1 = [],
          data2 = [];
        for (let key in lineData) {
          xData.push(key);
          data1.push([key, lineData[key].zc]);
          data2.push([key, lineData[key].yc]);
        }
        if (!this.lineChart) {
          this.lineChart = this.$echarts.init(this.$refs.chartLine);
        }
        const option = {
          color: ["#1DADFF", "#FF8831"],
          legend: {
            show: true,
            top: utils.fontSize(10),
          },
          grid: {
            top: utils.fontSize(50),
            bottom: utils.fontSize(30),
            left: utils.fontSize(40),
            right: utils.fontSize(20),
          },
          tooltip: {
            show: true,
            trigger: "axis",
          },
          xAxis: {
            type: "category",
            axisLine: {
              show: true,
              lineStyle: {
                color: "#AAB9CE",
              },
            },
            axisTick: {
              show: false,
              inside: false,
            },
            axisLabel: {
              show: true,
              color: "#222F40",
              fontSize: utils.fontSize(14),
            },
            splitLine: {
              show: false,
            },
            data: xData,
          },
          yAxis: {
            type: "value",
            yAxisIndex: 0,
            splitLine: {
              show: true,
              lineStyle: {
                color: "#AAB9CE",
              },
            },
            axisTick: {
              show: false,
            },
            axisLine: { show: false },
            axisLabel: {
              show: true,
              color: "#222F40",
              fontSize: utils.fontSize(14),
            },
            minInterval: 1,
            name: "数量",
            nameTextStyle: {
              color: "#222F40",
              fontSize: utils.fontSize(14),
            },
          },
          series: [
            {
              name: "正常",
              yAxisIndex: 0,
              type: "line",
              symbol: "emptyCircle",
              symbolSize: utils.fontSize(8),
              itemStyle: {
                normal: {
                  borderWidth: utils.fontSize(2),
                },
              },
              data: data1,
            },
            {
              name: "异常",
              yAxisIndex: 0,
              type: "line",
              symbol: "emptyCircle",
              symbolSize: utils.fontSize(8),
              itemStyle: {
                normal: {
                  borderWidth: 2,
                },
              },
              data: data2,
            },
          ],
        };
        this.lineChart.clear();
        this.lineChart.setOption(option);
        // 根据页面大小自动响应图表大小
        window.addEventListener("resize", () => {
          this.lineChart.resize();
        });
      });
    },
    drawPieChart(pieData) {
      this.$nextTick(() => {
        const chartDom = this.$refs.chartPie;
        if (!chartDom) {
          return;
        }

        // 如果已有实例，则先销毁
        if (this.pieChart) {
          this.pieChart.dispose();
          this.pieChart = null;
        }

        // 初始化图表
        this.pieChart = this.$echarts.init(chartDom);

        const data = [
          { name: "设施检修", value: pieData[1] },
          { name: "设备维护", value: pieData[2] },
          { name: "清淤疏浚", value: pieData[3] },
          { name: "防渗防漏", value: pieData[4] },
        ];

        const option = {
          tooltip: { trigger: "item" },
          legend: {
            orient: "vertical",
            top: "center",
            right: "20%",
            icon: "circle",
            textStyle: {
              color: "#222F40",
              fontSize: utils.fontSize(14),
            },
          },
          series: [
            {
              type: "pie",
              radius: ["39%", "77%"],
              avoidLabelOverlap: false,
              label: {
                show: true,
                formatter: "{d}%",
                color: "inherit",
                fontSize: utils.fontSize(14),
                fontWeight: "bold",
              },
              labelLine: { show: true },
              data,
            },
          ],
        };

        this.pieChart.setOption(option);
        window.addEventListener("resize", () => {
          this.pieChart?.resize();
        });
      });
    },

    loadProject() {
      const [beginDate, endDate] = this.searchForm.time || [];
      detailList({
        wrpcd: this.record.wrpcd,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit,
        beginDate,
        endDate,
        wxyhxm: this.searchForm.wxyhxm,
        // type: this.searchForm.range === "year" ? "month" : "day",
        // dayValue: options[this.searchForm.range],
      }).then((res) => {
        if (res.status === 200) {
          this.projectData = res.data.list || [];
          this.pagination.total = res.data.total;
        }
      });
    },
    // 重置
    reset() {
      this.$set(this.pagination, "page", 1);
      this.$refs.searchForm.resetForm();
      this.searchForm = {};
      this.loadDataMaintenance();
    },
    edit(type, row = {}) {
      this.dialogOptions.type = type;
      this.dialogOptions.title = type === "add" ? "新增" : "编辑";
      const formData = JSON.parse(
        JSON.stringify({ sfzc: 1, ...row, wrpcd: this.record.wrpcd })
      );

      // if (!formData.zgqFileList) {
      //   formData.zgqFileList = [];
      // }
      // if (!formData.zghFileList) {
      //   formData.zghFileList = [];
      // }
      this.formData = formData;
      this.dialogOptions.show = true;
    },
    handlePreview(row) {
      this.$refs.filePreviewDialog.preview(row);
    },
    resetDialogForm() {
      this.formData = {};
      this.$refs.maintenanceDialog.resetFields();
      this.dialogOptions.show = false;
    },
    refresh() {
      this.resetDialogForm();
      this.loadDataMaintenance();
      this.$emit("refresh");
    },
    // 删除
    delProject(id) {
      this.$confirm("此操作将删除选择维修项目,是否继续?", "提示", {
        type: "warning",
      }).then(() => {
        deleteById({
          id,
          wrpcd: this.record.wrpcd,
        }).then((res) => {
          if (res.status === 200) {
            this.$message.success("删除成功!");
            if (this.pagination.page > 1 && this.projectData.length === 1) {
              this.pagination.page -= 1;
            }
            this.loadChartData();
            this.loadProject();
            this.$emit("refresh");
          }
        });
      });
    },
    // 导出
    exports() {
      const [beginDate, endDate] = this.searchForm.time || [];
      const data = {
        wrpcd: this.record.wrpcd,
        beginDate,
        endDate,
        wxyhxm: this.searchForm.wxyhxm,
      };
      exportList(data, "", "arraybuffer").then((res) => {
        downloadFileByBlob("维修养护", res.data, "application/zip");
      });
    },
    childFn() {
      getAllOptionList({ type: "wxyhlx" }).then((res) => {
        if (res.status === 200) {
          this.listTypeInfo.wxyhlxList = (res.data || []).map((i) => ({
            label: i.name,
            value: i.code,
          }));
        }
      });
      this.loadDataMaintenance();
    },

    async loadDictIfNeeded() {
      if (this.wxyhlxLoaded) return;

      try {
        const res = await getAllOptionList({ type: "wxyhlx" });
        if (res.status === 200) {
          this.listTypeInfo.wxyhlxList = (res.data || []).map((i) => ({
            label: i.name,
            value: i.code,
          }));
          this.wxyhlxLoaded = true;
        }
      } catch (err) {
        console.error("加载字典失败", err);
      }
    },

    async enterMaintenanceTab() {
      await this.loadDictIfNeeded(); // 确保字典已加载
      this.loadDataMaintenance(); // 再加载数据和图表
    },
  },
  created() {
    this.getOption("OMP_PROJECT_TYPE", "projectTypes");
    this.getOption("OMP_FREQUENCY", "frequencys");
  },
  beforeDestroy() {
    // 销毁前的清理
    if (this.pieChart) {
      this.pieChart.dispose();
      this.pieChart = null;
    }
    if (this.lineChart) {
      this.lineChart.dispose();
      this.lineChart = null;
    }
  },
};
</script>
<style lang="scss" scoped>
.maintenance-detail {
  height: 100%;

  .detail-content {
    overflow: hidden;

    .md-chart {
      margin-bottom: 5px;
      height: 406px;

      .md-chart-item {
        height: 100%;
        box-sizing: border-box;
        border: 1px solid #abbacf;
        text-align: center;

        .chart,
        .chart-empty {
          width: 100%;
          height: 100%;
        }

        .chart-empty {
          text-align: center;

          img {
            width: 505px;
            height: 310px;
          }

          span {
            font-size: 22px;
            color: #7e858d;
          }
        }
      }
    }

    .md-table {
      height: calc(100% - 411px);

      .file-item {
        width: 64px;
        height: 48px;
        border-radius: 4px;
        overflow: hidden;

        & ~ .file-item {
          margin-left: 10px;
        }

        .table-img {
          width: 64px;
          height: 48px;
        }
      }

      ::v-deep(.el-form-item) {
        margin-bottom: 20px;
      }
    }
  }
}

.title {
  display: flex;
  align-items: center;
}

.file-list {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}
</style>
