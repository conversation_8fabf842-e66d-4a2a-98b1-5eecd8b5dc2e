// 手机后台设置配置
export const instructions = [
  {
    title: '华为',
    brand: 'huawei',
    children: [
      {
        title: '允许后台活动',
        dsc: '避免应用被系统清理',
        key: 'battery',
        children: [
          {
            title: '电池优化选择所有应用',
            img: require('@/assets/images/instruction/honor/battery1.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/honor/battery2.png')
          },
          {
            title: '选择不允许',
            img: require('@/assets/images/instruction/honor/battery3.png')
          },
          {
            title: '点击确定',
            img: require('@/assets/images/instruction/honor/battery4.png')
          }
        ]
      },
      {
        title: '自启动权限设置',
        dsc: '在应用异常退出后可自恢复',
        key: 'start',
        children: [
          {
            title: '应用',
            img: require('@/assets/images/instruction/honor/start1.png')
          },
          {
            title: '应用启动管理',
            img: require('@/assets/images/instruction/honor/start2.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/honor/start3.png')
          },
          {
            title: '允许自启动和后台活动',
            img: require('@/assets/images/instruction/honor/start4.png')
          },
          {
            title: '点击确定',
            img: require('@/assets/images/instruction/honor/start5.png')
          }
        ]
      },
      {
        title: '锁定后台任务',
        dsc: '避免应用被系统或人为误清理',
        key: 'lock',
        children: [
          {
            title: '从屏幕底部向上滑',
            img: require('@/assets/images/instruction/lock.png')
          },
          {
            title: '下滑白云智慧水务',
            img: require('@/assets/images/instruction/honor/lock2.png')
          },
          {
            title: '点击锁定白云智慧水务',
            img: require('@/assets/images/instruction/honor/lock3.png')
          }
        ]
      }
    ]
  },
  {
    title: '荣耀',
    brand: 'honor',
    children: [
      {
        title: '允许后台活动',
        dsc: '避免应用被系统清理',
        key: 'battery',
        children: [
          {
            title: '电池优化选择所有应用',
            img: require('@/assets/images/instruction/honor/battery1.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/honor/battery2.png')
          },
          {
            title: '选择不允许',
            img: require('@/assets/images/instruction/honor/battery3.png')
          },
          {
            title: '点击确定',
            img: require('@/assets/images/instruction/honor/battery4.png')
          }
        ]
      },
      {
        title: '自启动权限设置',
        dsc: '在应用异常退出后可自恢复',
        key: 'start',
        children: [
          {
            title: '应用',
            img: require('@/assets/images/instruction/honor/start1.png')
          },
          {
            title: '应用启动管理',
            img: require('@/assets/images/instruction/honor/start2.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/honor/start3.png')
          },
          {
            title: '允许自启动和后台活动',
            img: require('@/assets/images/instruction/honor/start4.png')
          },
          {
            title: '点击确定',
            img: require('@/assets/images/instruction/honor/start5.png')
          }
        ]
      },
      {
        title: '锁定后台任务',
        dsc: '避免应用被系统或人为误清理',
        key: 'lock',
        children: [
          {
            title: '从屏幕底部向上滑',
            img: require('@/assets/images/instruction/lock.png')
          },
          {
            title: '下滑青山嘴水库',
            img: require('@/assets/images/instruction/honor/lock2.png')
          },
          {
            title: '点击锁定青山嘴水库',
            img: require('@/assets/images/instruction/honor/lock3.png')
          }
        ]
      }
    ]
  },
  {
    title: '小米',
    brand: 'xiaomi',
    children: [
      {
        title: '允许后台活动',
        dsc: '避免应用被系统清理',
        key: 'battery',
        children: [
          {
            title: '应用设置',
            img: require('@/assets/images/instruction/xiaomi/battery1.png')
          },
          {
            title: '应用管理',
            img: require('@/assets/images/instruction/xiaomi/battery2.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/xiaomi/battery3.png')
          },
          {
            title: '省电策略',
            img: require('@/assets/images/instruction/xiaomi/battery4.png')
          },
          {
            title: '选择无限制',
            img: require('@/assets/images/instruction/xiaomi/battery5.png')
          }
        ]
      },
      {
        title: '自启动权限设置',
        dsc: '在应用异常退出后可自恢复',
        key: 'start',
        children: [
          {
            title: '应用设置',
            img: require('@/assets/images/instruction/xiaomi/start1.png')
          },
          {
            title: '应用管理',
            img: require('@/assets/images/instruction/xiaomi/start2.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/xiaomi/start3.png')
          },
          {
            title: '开启自启动开关',
            img: require('@/assets/images/instruction/xiaomi/start4.png')
          }
        ]
      },
      {
        title: '锁定后台任务',
        dsc: '避免应用被系统或人为误清理',
        key: 'lock',
        children: [
          {
            title: '从屏幕底部向上滑',
            img: require('@/assets/images/instruction/lock.png')
          },
          {
            title: '长按青山嘴水库',
            img: require('@/assets/images/instruction/xiaomi/lock2.png')
          },
          {
            title: '点击锁定青山嘴水库',
            img: require('@/assets/images/instruction/xiaomi/lock3.png')
          }
        ]
      }
    ]
  },
  {
    title: 'oppo',
    brand: 'oppo',
    children: [
      {
        title: '允许后台活动',
        dsc: '避免应用被系统清理',
        key: 'battery',
        children: [
          {
            title: '电池',
            img: require('@/assets/images/instruction/oppo/battery1.png')
          },
          {
            title: '应用耗电管理',
            img: require('@/assets/images/instruction/oppo/battery2.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/oppo/battery3.png')
          },
          {
            title: '允许完全后台行为',
            img: require('@/assets/images/instruction/oppo/battery4.png')
          },
          {
            title: '点击允许',
            img: require('@/assets/images/instruction/oppo/battery5.png')
          }
        ]
      },
      {
        title: '自启动权限设置',
        dsc: '在应用异常退出后可自恢复',
        key: 'start',
        children: [
          {
            title: '应用管理',
            img: require('@/assets/images/instruction/oppo/start1.png')
          },
          {
            title: '自启动管理',
            img: require('@/assets/images/instruction/oppo/start2.png')
          },
          {
            title: '开启白云智慧水务开关',
            img: require('@/assets/images/instruction/oppo/start3.png')
          }
        ]
      },
      {
        title: '锁定后台任务',
        dsc: '避免应用被系统或人为误清理',
        key: 'lock',
        children: [
          {
            title: '从屏幕底部向上滑',
            img: require('@/assets/images/instruction/lock.png')
          },
          {
            title: '点击白云智慧水务更多按钮',
            img: require('@/assets/images/instruction/oppo/lock2.png')
          },
          {
            title: '点击锁定白云智慧水务',
            img: require('@/assets/images/instruction/oppo/lock3.png')
          }
        ]
      }
    ]
  },
  {
    title: 'realme',
    brand: 'realme',
    children: [
      {
        title: '允许后台活动',
        dsc: '避免应用被系统清理',
        key: 'battery',
        children: [
          {
            title: '电池',
            img: require('@/assets/images/instruction/oppo/battery1.png')
          },
          {
            title: '应用耗电管理',
            img: require('@/assets/images/instruction/oppo/battery2.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/oppo/battery3.png')
          },
          {
            title: '允许完全后台行为',
            img: require('@/assets/images/instruction/oppo/battery4.png')
          },
          {
            title: '点击允许',
            img: require('@/assets/images/instruction/oppo/battery5.png')
          }
        ]
      },
      {
        title: '自启动权限设置',
        dsc: '在应用异常退出后可自恢复',
        key: 'start',
        children: [
          {
            title: '应用管理',
            img: require('@/assets/images/instruction/oppo/start1.png')
          },
          {
            title: '自启动管理',
            img: require('@/assets/images/instruction/oppo/start2.png')
          },
          {
            title: '开启白云智慧水务开关',
            img: require('@/assets/images/instruction/oppo/start3.png')
          }
        ]
      },
      {
        title: '锁定后台任务',
        dsc: '避免应用被系统或人为误清理',
        key: 'lock',
        children: [
          {
            title: '从屏幕底部向上滑',
            img: require('@/assets/images/instruction/lock.png')
          },
          {
            title: '点击白云智慧水务更多按钮',
            img: require('@/assets/images/instruction/realme/lock2.png')
          },
          {
            title: '点击锁定白云智慧水务',
            img: require('@/assets/images/instruction/realme/lock3.png')
          }
        ]
      }
    ]
  },
  {
    title: 'vivo',
    brand: 'vivo',
    children: [
      {
        title: '允许后台活动',
        dsc: '避免应用被系统清理',
        key: 'battery',
        children: [
          {
            title: '电池',
            img: require('@/assets/images/instruction/vivo/battery1.png')
          },
          {
            title: '后台高耗电',
            img: require('@/assets/images/instruction/vivo/battery2.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/vivo/battery3.png')
          },
          {
            title: '开启开关',
            img: require('@/assets/images/instruction/vivo/battery4.png')
          }
          // {
          //   title: "开启白云智慧水务开关",
          //   img: ""
          // }
        ]
      },
      {
        title: '自启动权限设置',
        dsc: '在应用异常退出后可自恢复',
        key: 'start',
        children: [
          {
            title: '应用与权限',
            img: require('@/assets/images/instruction/vivo/start1.png')
          },
          {
            title: '权限管理',
            img: require('@/assets/images/instruction/vivo/start2.png')
          },
          {
            title: '自启动',
            img: require('@/assets/images/instruction/vivo/start3.png')
          },
          {
            title: '开启白云智慧水务开关',
            img: require('@/assets/images/instruction/vivo/start4.png')
          }
        ]
      },
      {
        title: '锁定后台任务',
        dsc: '避免应用被系统或人为误清理',
        key: 'lock',
        children: [
          {
            title: '从屏幕底部向上滑',
            img: require('@/assets/images/instruction/lock.png')
          },
          {
            title: '下滑白云智慧水务',
            img: require('@/assets/images/instruction/vivo/lock2.png')
          },
          {
            title: '点击锁定白云智慧水务',
            img: require('@/assets/images/instruction/vivo/lock3.png')
          }
        ]
      }
    ]
  },
  {
    title: '一加',
    brand: 'oneplus',
    children: [
      {
        title: '允许后台活动',
        dsc: '避免应用被系统清理',
        key: 'battery',
        children: [
          {
            title: '电池',
            img: require('@/assets/images/instruction/oneplus/battery1.png')
          },
          {
            title: '应用耗电管理',
            img: require('@/assets/images/instruction/oneplus/battery2.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/oneplus/battery3.png')
          },
          {
            title: '允许完全后台行为',
            img: require('@/assets/images/instruction/oneplus/battery4.png')
          },
          {
            title: '点击允许',
            img: require('@/assets/images/instruction/oneplus/battery5.png')
          }
        ]
      },
      {
        title: '自启动权限设置',
        dsc: '在应用异常退出后可自恢复',
        key: 'start',
        children: [
          {
            title: '应用管理',
            img: require('@/assets/images/instruction/oneplus/start1.png')
          },
          {
            title: '自启动管理',
            img: require('@/assets/images/instruction/oneplus/start2.png')
          },
          {
            title: '开启白云智慧水务开关',
            img: require('@/assets/images/instruction/oneplus/start3.png')
          }
        ]
      },
      {
        title: '锁定后台任务',
        dsc: '避免应用被系统或人为误清理',
        key: 'lock',
        children: [
          {
            title: '从屏幕底部向上滑',
            img: require('@/assets/images/instruction/lock.png')
          },
          {
            title: '点击白云智慧水务更多按钮',
            img: require('@/assets/images/instruction/oneplus/lock2.png')
          },
          {
            title: '点击锁定白云智慧水务',
            img: require('@/assets/images/instruction/oneplus/lock3.png')
          }
        ]
      }
    ]
  },
  {
    title: '魅族',
    brand: 'meizu',
    children: [
      {
        title: '允许后台活动',
        dsc: '避免应用被系统清理',
        key: 'battery',
        children: [
          {
            title: '隐私和权限',
            img: require('@/assets/images/instruction/meizu/battery1.png')
          },
          {
            title: '后台管理',
            img: require('@/assets/images/instruction/meizu/battery2.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/meizu/battery3.png')
          },
          {
            title: '选择允许后台运行',
            img: require('@/assets/images/instruction/meizu/battery4.png')
          }
        ]
      },
      {
        title: '锁定后台任务',
        dsc: '避免应用被系统或人为误清理',
        key: 'lock',
        children: [
          {
            title: '从屏幕底部向上滑',
            img: require('@/assets/images/instruction/lock.png')
          },
          {
            title: '下滑白云智慧水务',
            img: require('@/assets/images/instruction/meizu/lock2.png')
          },
          {
            title: '点击锁定白云智慧水务',
            img: require('@/assets/images/instruction/meizu/lock3.png')
          }
        ]
      }
    ]
  },
  {
    title: '联想',
    brand: 'lenovo',
    children: [
      {
        title: '允许后台活动',
        dsc: '避免应用被系统清理',
        key: 'battery',
        children: [
          {
            title: '应用和通知',
            img: require('@/assets/images/instruction/lenovo/battery1.png')
          },
          {
            title: '特殊应用权限',
            img: require('@/assets/images/instruction/lenovo/battery2.png')
          },
          {
            title: '电池优化',
            img: require('@/assets/images/instruction/lenovo/battery3.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/lenovo/battery4.png')
          },
          {
            title: '选择不优化',
            img: require('@/assets/images/instruction/lenovo/battery5.png')
          },
          {
            title: '点击完成',
            img: require('@/assets/images/instruction/lenovo/battery6.png')
          }
        ]
      },
      {
        title: '自启动权限设置',
        dsc: '在应用异常退出后可自恢复',
        key: 'start',
        children: [
          {
            title: '应用和通知',
            img: require('@/assets/images/instruction/lenovo/start1.png')
          },
          {
            title: '特殊应用权限',
            img: require('@/assets/images/instruction/lenovo/start2.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/lenovo/start3.png')
          },
          {
            title: '权限',
            img: require('@/assets/images/instruction/lenovo/start4.png')
          },
          {
            title: '应用自启',
            img: require('@/assets/images/instruction/lenovo/start5.png')
          },
          {
            title: '选择允许',
            img: require('@/assets/images/instruction/lenovo/start6.png')
          }
        ]
      }
    ]
  },
  {
    title: '三星',
    brand: 'samsung',
    children: [
      {
        title: '允许后台活动',
        dsc: '避免应用被系统清理',
        key: 'battery',
        children: [
          {
            title: '应用程序',
            img: require('@/assets/images/instruction/samsung/battery1.png')
          },
          {
            title: '青山嘴水库',
            img: require('@/assets/images/instruction/samsung/battery2.png')
          },
          {
            title: '电池',
            img: require('@/assets/images/instruction/samsung/battery3.png')
          },
          {
            title: '允许后台活动',
            img: require('@/assets/images/instruction/samsung/battery4.png')
          }
        ]
      },
      {
        title: '自启动权限设置',
        dsc: '在应用异常退出后可自恢复',
        key: 'start',
        children: [
          {
            title: '常规管理',
            img: require('@/assets/images/instruction/samsung/start1.png')
          },
          {
            title: '电池',
            img: require('@/assets/images/instruction/samsung/start2.png')
          },
          {
            title: '后台使用限制',
            img: require('@/assets/images/instruction/samsung/start3.png')
          },
          {
            title: '自动运行应用程序',
            img: require('@/assets/images/instruction/samsung/start4.png')
          },
          {
            title: '开启白云智慧水务开关',
            img: require('@/assets/images/instruction/samsung/start5.png')
          }
        ]
      },
      {
        title: '锁定后台任务',
        dsc: '避免应用被系统或人为误清理',
        key: 'lock',
        children: [
          {
            title: '从屏幕底部向上滑',
            img: require('@/assets/images/instruction/lock.png')
          },
          {
            title: '长按白云智慧水务',
            img: require('@/assets/images/instruction/samsung/lock2.png')
          },
          {
            title: '点击锁定白云智慧水务',
            img: require('@/assets/images/instruction/samsung/lock3.png')
          }
        ]
      }
    ]
  }
];
