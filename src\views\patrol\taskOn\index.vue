<template>
  <!-- 新增巡检任务 -->
  <main class="main flex-column">
    <CustomNavBar />
    <section class="section flex-1">
      <baidu-map
        ref="baiduMap"
        class="map"
        show-polyline
        show-marker
        :center="{
          lng: center.lng,
          lat: center.lat
        }"
        :polyline="polyline"
        @onMapReady="onMapReady"
      ></baidu-map>
    </section>

    <div class="topright-btns">
      <div class="btn" @click="showTipBox = !showTipBox">
        <img class="btn-icon" src="@/assets/images/patrol/route.png" alt="" />
        <span class="btn-text">线路</span>
      </div>

      <div class="tip-box" v-show="showTipBox">
        <van-steps direction="vertical" :active="-1">
          <van-step v-for="(item, index) in routeLine" :key="index">
            <template #inactive-icon>
              <van-badge color="#1989fa" :content="index + 1" />
            </template>
            <template #active-icon>
              <van-badge color="#1989fa" :content="index + 1" />
            </template>
            <h3>{{ item.pointName.split('（')[0] }}</h3>
            <p v-if="item.pointName.split('（')[1]">{{ item.pointName.split('（')[1].slice(0, -1) }}</p>
          </van-step>
        </van-steps>
      </div>
    </div>

    <div class="bottom-panel">
      <div class="panel-detail">
        <div class="detail-info flex-vc">
          <div class="info-item flex-1 flex-column">
            <div class="info-value">{{ currentTime }}</div>
            <div>时间</div>
          </div>
          <div class="info-item flex-1 flex-column">
            <div class="info-value">{{ currentDistance }} km</div>
            <div>路程</div>
          </div>
        </div>
        <div class="detail-action flex-hvc">
          <!-- <div :style="{ opacity: timer ? 1 : 0.5 }" class="action-item flex-1 flex-hvc" @click="onClickYhsbBtn">
            <img class="item-icon" src="@/assets/images/patrol/patrolTask/edit.png" alt="" />
            <span>隐患上报</span>
          </div> -->

          <div :style="{ opacity: timer ? 1 : 0.5 }" class="action-item flex-hvc">
            <van-uploader
              class="option-upload"
              :preview-image="false"
              :multiple="true"
              :capture="capture"
              @click-upload="beforeUpload"
              :after-read="afterRead"
              :disabled="!timer"
            >
              <div class="option-btn flex-vc">
                <img class="item-icon" src="@/assets/images/descript/photo.png" alt="" />
                <span>拍照</span>
              </div>
            </van-uploader>
          </div>
        </div>
      </div>
      <p class="panel-note">注意：巡检过程中请勿关闭程序，否则会导致自动上报位置失效。</p>
      <div class="panel-btn">
        <van-button v-if="!timer" class="btn" type="info" :block="true" :round="true" @click="startPatrol">开始巡检</van-button>
        <van-button v-else class="btn" type="danger" :block="true" :round="true" @click="endPatrol">结束巡检</van-button>
      </div>
    </div>
  </main>
</template>

<script>
import BaiduMap from '@/components/BaiduMap';
import { mapState } from 'vuex';
import { getCurrentPosition } from '@/utils/location';
import { addStn } from '@/api/patrolManage/patrolProblem/index.js';
import { checkEnv } from '@/utils/navigation';
import { checkApplication } from '@/utils/plusPermission';
import { setStorage, getStorage } from '@/utils/storage';
import { addTask, getGcxcDetail, deleteGcxc } from '@/api/patrol';
import dayjs from 'dayjs';
import { uploadFile } from '@/api/common.js';
import { checkBackgroundPermission, checkGPS, checkNotification, checkPower } from '@/utils/plusPermission';
import { routeLine } from '../options';

export default {
  name: 'PatrolTaskAdd',
  components: {
    BaiduMap
  },
  props: {},
  data() {
    const isAndroid = /android/.test(navigator.userAgent.toLowerCase());
    this.routeLine = routeLine;
    return {
      showTipBox: false,
      isMapReady: false,
      taskId: null,
      fromPathName: '',

      capture: isAndroid ? 'camera' : null,
      hasCheckPermisson: false
    };
  },
  computed: {
    ...mapState('patrol', {
      timer: state => state.timer,
      watchId: state => state.watchId,
      polyline: state => state.routeArray,
      coordinates: state => state.coordinates,
      currentTime: state => state.time,
      currentDistance: state => {
        if (state.distance >= 10) {
          return (state.distance / 1000).toFixed(2);
        }
        return 0;
      },
      patrolTask: state => state.patrolTask,
      patrolTaskKey: state => state.patrolTaskKey,
      patrolInfoKey: state => state.patrolInfoKey,
      center: state => state.center
    }),
    ...mapState({
      projectInfo: state => state.user.projectInfo,
      userInfo: state => state.user.userInfo
    }),
    mapList() {
      return [
        {
          name: '百度地图' + (process.env.NODE_ENV === 'app' ? (checkApplication('com.baidu.BaiduMap') ? '' : '(未安装)') : ''),
          value: 'baidu'
        }
        // {
        //   name: '高德地图' + (process.env.NODE_ENV === 'app' ? (checkApplication('com.autonavi.minimap') ? '' : '(未安装)') : ''),
        //   value: 'gaode'
        // }
      ];
    }
  },

  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.fromPathName = from.name;
    });
  },
  created() {},
  mounted() {
    this.$store.dispatch('patrol/getPosition');
    // this.$nextTick(() => {
    //   if (this.fromPathName === 'TaskDetail') {
    //     this.$userApp.toast.show({
    //       text: `本次巡查任务已完成，\r\n即将跳转到首页`,
    //       type: 'text'
    //     });
    //     setTimeout(() => {
    //       this.toPage({ name: 'TaskList' });
    //     }, 800);
    //   } else {
    //     if (!this.watchId) {
    //       this.$store.dispatch('patrol/startPatrol', this.taskId);
    //       this.$store.commit('patrol/setPatrolTimer');
    //     }
    //   }
    // });
  },
  activated() {
    // 从巡查记录进来的，未巡查完成的任务
    const param = this.$route.query;

    if (param.taskId) {
      this.taskId = +param.taskId;
      this.$nextTick(() => {
        if (this.fromPathName === 'TaskDetail') {
          this.$userApp.toast.show({
            text: `本次巡查任务已完成，\r\n即将跳转到首页`,
            type: 'text'
          });
          setTimeout(() => {
            this.toPage({ name: 'TaskList' });
          }, 800);
        } else {
          if (!this.watchId) {
            this.$store.dispatch('patrol/startPatrol', this.taskId);
            this.$store.commit('patrol/setPatrolTimer');
          }
        }
      });

      return;
    }

    this.checkTask();
  },
  deactivated() {},
  methods: {
    // 隐患上报
    onClickYhsbBtn() {
      if (!this.timer) {
        return;
      }
      this.toPage({ name: 'DangerUpload', taskId: this.taskId, type: 'add' });
    },
    beforeUpload() {
      this.$store.commit('location/setOtherBackgroundState', true);
    },
    async afterRead(result) {
      let formData = new FormData();
      formData.append('module', 'xjxc');
      formData.append('file', result.file);
      const res = await uploadFile(formData);

      let xjtpId = res.data.id;
      const res2 = await getGcxcDetail({
        id: this.taskId
      });
      if (res2.data.xjtpId) {
        xjtpId = res2.data.xjtpId + `,${xjtpId}`;
      }

      addTask({
        id: this.taskId,
        wrpcd: this.userInfo.assignWrpcdList?.[0] || this.projectInfo.wrpcd,
        xjtpId
      });

      this.$userApp.toast.show({
        text: `上传成功`,
        type: 'text'
      });
    },
    // 结束巡查
    endPatrol() {
      this.$router.push({
        name: 'PatrolReport',
        query: { taskId: this.taskId }
      });
    },
    // 创建巡查任务
    creatTask() {
      addTask({
        wrpcd: this.userInfo.assignWrpcdList?.[0] || this.projectInfo.wrpcd,
        xjrq: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        zgStatus: 1
      }).then(res => {
        if (res.status === 200) {
          setStorage(this.patrolTaskKey, {
            id: res.data,
            xjStartTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
          });
          this.$store.dispatch('patrol/getPosition');
          this.taskId = res.data;

          if (!this.watchId) {
            this.$store.dispatch('patrol/startPatrol', this.taskId);
            this.$store.commit('patrol/setPatrolTimer');
          }
        }
      });
    },

    startPatrol() {
      checkGPS().then(res => {
        console.log(res);

        if (res) {
          this.creatTask();
        } else {
          this.$dialog
            .confirm({
              title: '提示',
              message: '当前没有开启定位，是否确认进行巡查任务？'
            })
            .then(() => {
              this.creatTask();
            })
            .catch(() => {
              this.$dialog.close();
            });
        }
      });
    },
    init() {},
    onMapReady() {
      this.isMapReady = true;
    },
    setMarker() {
      if (this.projectInfo.lgtd && this.projectInfo.lttd) {
        const ptArr = [
          {
            lng: this.projectInfo.lgtd,
            lat: this.projectInfo.lttd,
            Name: '',
            iconUrl: require('@/assets/images/index/marker.png'),
            scale: 0.5,
            isAnimate: false,
            labelZoom: 12,
            pointZoom: 8
          }
        ];
        this.$refs.mapRef.setArrPtLayerVisible({
          isVisible: true,
          layerName: 'patrolPoint',
          lngFieldName: 'lng',
          latFieldName: 'lat',
          labelFieldName: 'Name',
          ptArr
        });
      }
    },
    toPage(item) {
      const { name, ...rest } = item;
      if (name) {
        this.$router.push({
          name,
          params: { ...rest }
        });
      }
    },
    checkTask() {
      const patrolTask = getStorage(this.patrolTaskKey);
      if (patrolTask) {
        this.$dialog
          .confirm({
            title: '提示',
            message: '已有巡查任务，是否继续巡查？'
          })
          .then(() => {
            this.$store.dispatch('patrol/startPatrol', patrolTask.id);
          })
          .catch(() => {
            // 不继续巡查
            this.deleteTask(patrolTask);
            this.$store.dispatch('patrol/getPosition');
          });
        return;
      }
      if (!this.hasCheckPermisson) {
        this.hasCheckPermisson = true;
        this.checkPermission();
      }
      checkGPS().then(res => {
        console.log('checkGPS', res);
        if (res) {
          this.$store.dispatch('patrol/getPosition');
        }
      });
    },
    // checkTask() {
    //   const patrolTask = getStorage(this.patrolTaskKey);
    //   if (patrolTask) {
    //     this.$dialog
    //       .confirm({
    //         title: '提示',
    //         message: '已有巡检任务，是否确认删除任务重新创建？'
    //       })
    //       .then(() => {
    //         this.deleteTask(patrolTask);
    //         this.$store.dispatch('patrol/getPosition');
    //       })
    //       .catch(() => {
    //         this.$router.back();
    //       });
    //     return;
    //   }
    //   if (!this.hasCheckPermisson) {
    //     this.hasCheckPermisson = true;
    //     this.checkPermission();
    //   }
    //   checkGPS().then(res => {
    //     console.log('checkGPS', res);
    //     if (res) {
    //       this.$store.dispatch('patrol/getPosition');
    //     }
    //   });
    // },
    deleteTask(data) {
      deleteGcxc({ id: data.id }).then(res => {
        if (res.status === 200) {
          this.$dialog.close();
          this.$store.commit('patrol/resetPatrol');
        } else {
          this.$userApp.toast.show({
            text: res.message,
            type: 'text'
          });
        }
      });
    },
    async checkPermission() {
      if (!this.isCheckBackgroundPermission) {
        await checkBackgroundPermission();
        this.isCheckBackgroundPermission = true;
      }
      if (!this.isCheckNotification) {
        await checkNotification();
        this.isCheckNotification = true;
      }
      if (!this.isCheckPower) {
        await checkPower();
        this.isCheckPower = true;
      }
    },

    handleMapSelect(mapData) {
      const patrolInfo = getStorage(this.patrolTaskKey);

      checkEnv(mapData.value, {
        lng: patrolInfo.lgtd || this.projectInfo.lgtd,
        lat: patrolInfo.lttd || this.projectInfo.lttd,
        title: patrolInfo.xjName || '',
        address: ''
      });
      // const successFn = position => {
      //   const { longitude, latitude } = position.coords;
      //   const { city, district, street, streetNum } = position.address || {};
      //   const address = [city, district, street, streetNum].filter(i => i).join('');
      //   checkEnv(mapData.value, {
      //     lng: longitude,
      //     lat: latitude,

      //     address: address
      //   });
      // };
      // getCurrentPosition(successFn);
    },
    // 打卡
    async clockIn() {
      const successFn = position => {
        let data = {
          taskId: this.taskId,
          stnType: 1,
          lgtd: '',
          lttd: '',
          stnAddress: '',
          attachmentList: []
        };
        const { longitude, latitude } = position.coords;
        const { city, district, street, streetNum } = position.address || {};
        const address = [city, district, street, streetNum].filter(i => i).join('');
        data.lgtd = longitude;
        data.lttd = latitude;
        data.stnAddress = address;

        addStn(data).then(res => {
          if (res.status === 200) {
            this.$userApp.toast.show({
              text: '打卡成功！',
              type: 'text'
            });
          }
        });
      };

      getCurrentPosition(successFn);
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
}
.section {
  position: relative;
  padding-bottom: 132px;
  overflow: hidden;
  .map {
    width: 100%;
    height: 100%;
  }
}
.top-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  left: 20px;
  z-index: 9999;
  .btn {
    width: 180px;
    height: 70px;
  }
}
.topright-btns {
  position: fixed;
  top: 120px;
  right: 20px;
  .btn {
    display: flex;
    flex-direction: column;
    gap: 5px;
    align-items: center;
    padding: 10px 20px;
    background: #ffffff;
    box-shadow: 0 0 15px rgb(0 0 0 / 10%);
    .btn-icon {
      width: 40px;
      height: 40px;
    }
  }
  .tip-box {
    position: absolute;
    top: 0;
    right: 100px;
    width: max-content;
    box-shadow: 0 0 15px rgb(0 0 0 / 10%);
    max-height: 40vh;
    overflow-y: auto;
    .step-number {
      padding: 5px;
      color: #ffffff;
      background: #478cdc;
      border-radius: 50%;
    }
    .van-step {
      padding: 10px 10px 10px 0;
      :deep(.van-step__title) {
        font-size: 22px;
        line-height: 1.2;
        color: #000000;
        p {
          color: gray;
        }
      }
    }
  }
}
.bottom-panel {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  font-size: 28px;
  background: linear-gradient(to bottom, transparent 0%, $bg-page 60%);
  .panel-detail {
    box-sizing: border-box;
    padding: 40px 32px 36px;
    margin: 0 20px;
    margin-bottom: 20px;
    background: rgba($color-primary, 0.9);
    border-radius: 18px;
    .detail-info {
      margin-bottom: 40px;
      color: $color-text-white;
      .info-item {
        align-items: center;
        color: rgba($color-text-white, 0.8);
        .info-value {
          margin-bottom: 8px;
          font-size: 32px;
          font-weight: 500;
          line-height: 40px;
          color: $color-text-white;
        }
      }
    }
    .action-item {
      width: 50%;
      padding: 5px 0;
      font-weight: 500;
      color: $color-text-black;
      text-align: center;
      background: $bg-page;
      border-radius: 38px;
      .item-icon {
        width: 70px;
        height: 70px;
        margin-right: 8px;
      }
      .option-btn {
        gap: 10px;
      }
    }
  }
  .panel-note {
    padding: 0 46px;
    font-size: 20px;
    line-height: 28px;
    color: rgba($color-text-black, 0.6);
    text-align: center;
  }
  .panel-btn {
    box-sizing: border-box;
    padding: 16px 46px 24px;
    .btn {
      height: 98px;
      font-size: 34px;
      font-weight: 500;
    }
  }
}
</style>
