<template>
  <div v-if="toastObj.type" class="lotus-toast">
    <div class="lotus-toast-inner">
      <p v-text="toastObj.text"></p>
    </div>
  </div>
</template>
<script>
export default {
  name: 'lotus-toast',
  props: ['lotusToastData'],
  data() {
    return {
      toastObj: {
        text: '',
        type: ''
      }
    };
  },
  components: {},
  methods: {},
  mounted() {},
  created() {}
};
</script>
<style lang="scss" scoped>
$lotusToast: lotus-toast;
.#{$lotusToast} {
  position: fixed;
  top: 0;
  z-index: 99999;
  width: 100%;
  height: 100%;
  background: rgb(255 255 255 / 0%);
  &-inner {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 80px;
    padding: 28px 42px;
    font-size: 28px;
    line-height: 36px;
    color: #ffffff;
    text-align: center;
    background: #4c4c4c;
    border-radius: 16px;
    transform: translate(-50%, -50%);
  }
}
</style>
