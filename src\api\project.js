import apiUrl from '@/utils/apiUrl';
import appAjax from '@/utils/apiRequestType';
const defaultUrl = apiUrl.defaultUrl;

/**
 * 获取工程基础信息
 * @param data
 * "projectId": 工程id
 */
export function getProjectInfo(data) {
  return appAjax.post(defaultUrl + '/reservoirProjectData/getBaseInfoList', data);
}

/**
 * 查询用户工程列表
 */
export function queryProjectList(data) {
  return appAjax.post(defaultUrl + '/scoreProject/querySortByProjectType', data);
}
