import Compressor from 'compressorjs';
export default {
  data() {
    return {};
  },
  methods: {
    async uploadImg(result) {
      this.$userApp.loading.show();
      if (Array.isArray(result)) {
        for (let r of result) {
          await this.compressorImg(r.file);
        }
      } else if (this.uploadType == 'video') {
        await this.uploadFile(result.file);
      } else {
        await this.compressorImg(result.file);
      }
    },
    // 压缩图片
    async compressorImg(file) {
      let _this = this;
      // if (file.size < 1000000) {
      //   await _this.uploadFile(file);
      //   return;
      // }
      return new Promise((res, rej) => {
        new Compressor(file, {
          quality: 0.4,
          success: async result => {
            await _this.uploadFile(result);
            res();
          },
          error: async err => {
            await _this.uploadFile(file);
            rej();
          }
        });
      });
    },
    async uploadFile(file) {
      let formData = new FormData();

      // 配置自己定义的额外的参数
      const extraParams = this.extraParams;
      Object.keys(extraParams).map(key => {
        formData.append(key, extraParams[key]);
      });
      if (this.modelType === 'zxqsb') {
        formData.append('module', this.uploadType); //img || video
        formData.append('file', file, file.name);
        let fileRes = await this.$appAjax.postUpLoadFile(this.$appApiUrl.defaultUrl + 'api/fxAttachment/upload', formData);
        this.fileList[this.fileList.length - 1].id = fileRes.id;
        this.$emit('uploadCB', fileRes);
        return;
      }

      if (this.modelType === 'ckdk') {
        formData.append('module', 'wzgl'); //img || video
        formData.append('file', file, file.name);
        let fileRes = await this.$appAjax.postUpLoadFile(
          this.$appApiUrl.planingUrl + 'msa-sfya/common/attachment/upload',
          formData
        );
        this.fileList[this.fileList.length - 1].id = fileRes.data.id;
        this.$emit('uploadCB', fileRes);
        return;
      }

      formData.append('pId', this.pId);
      formData.append('modelType', this.modelType);
      formData.append('file', file, file.name);

      await this.$appAjax.postUpLoadFile(this.$appApiUrl.defaultUrl + 'api/attachment/upload', formData);
    }
  }
};
