import { setStorage, getStorage, clearStorage, removeStorage } from '@/utils/storage';
import { sm2 } from 'sm-crypto';
import { login, userInfo, refresh } from '@/api/user';
import { queryProjectList } from '@/api/project';
import router from '@/router';
import store from '@/store';

function loopTree(tree) {
  if (tree) {
    if (Array.isArray(tree)) {
      tree.forEach(item => {
        item.label = item.areaName;
        item.value = item.areaCode;
        item.children = item.childList;
        if (item.children && Array.isArray(item.children)) {
          loopTree(item.children);
        }
      });
    }
  }
}

const state = {
  token: '',
  userInfo: {},
  isSingleProject: '', // 是否单工程用户
  projectInfo: {},
  projectList: [],
  areaCode: '',
  projectType: ''
};

const mutations = {
  setToken(state, data) {
    state.token = data ? data.accessToken : '';
    if (data) {
      // 后端返回来的过期时间单位是分钟，需要转换成毫秒
      const expire = +new Date() + data.tokenTime * 60 * 1000;
      setStorage('std_token', data.accessToken, expire);
      setStorage('std-refreshToken', data.refreshToken);
    } else {
      removeStorage('std_token');
      removeStorage('std-refreshToken');
    }
  },
  setUserInfo(state, userInfo) {
    if (userInfo && userInfo.attr) {
      loopTree(userInfo.attr.areaTree);
    }
    const { attr, ...other } = userInfo;
    setStorage('userInfo', other);
    setStorage('userInfoAttr', attr);
    state.userInfo = userInfo;
  },
  setIsSingleProject(state, isSingleProject) {
    state.isSingleProject = isSingleProject;
    setStorage('isSingleProject', isSingleProject);
  },
  setProjectInfo(state, projectInfo) {
    state.projectInfo = projectInfo;
    setStorage('projectInfo', projectInfo);
  },
  setProjectList(state, projectList) {
    state.projectList = projectList;
    setStorage('projectList', projectList);
  }
};

const actions = {
  // 用户登陆
  login({ commit }, user) {
    // 公钥
    const publicKey =
      '04e052982fd3001d32a6fbcbd131b5974da7a6e4d51f480e6b365a5182e9a629c545b9549135646b68e27c71907e3ae76cf724ab5f187ea52b55f5e0a625df071e';
    const encryptFormInfo = {
      username: '04' + sm2.doEncrypt(user.username, publicKey),
      password: '04' + sm2.doEncrypt(user.password, publicKey),
      captcha: '04' + sm2.doEncrypt(user.captcha, publicKey),
      codeId: user.codeId
    };

    // if (window.plus) {
    //   encryptFormInfo.version = rootState.location.version || window.plus.runtime.version;
    // }
    return login(encryptFormInfo).then(res => {
      if (res.status === 200) {
        const { data } = res;
        clearStorage();
        // 后端返回来的过期时间单位是分钟，需要转换成毫秒
        commit('setToken', data);
      }
      return res;
    });
  },
  // 获取用户信息
  getUserInfo({ commit, dispatch }) {
    return userInfo().then(async res => {
      if (res.status === 200) {
        commit('setUserInfo', res.data);
        await dispatch('getProjectList');
        await store.dispatch('common/getAllOption');
        return res;
      }
      return res;
    });
  },

  // 获取用户所在区域的所有工程列表
  async getProjectList({ commit, state }) {
    let projectList = [];
    const res = await queryProjectList({
      areaCode: Array.isArray(state.areaCode) ? state.areaCode[state.areaCode.length - 1] : '',
      projectType: state.projectType
    });

    if (res.status === 200) {
      projectList = res.data || [];
      // 当用户分配项目数量为1，且查询项目列表数据也为1时，系统名称改为对应项目的项目名称
      if (state.userInfo?.assignWrpcdList?.length === 1 && projectList.length === 1) {
        commit('setIsSingleProject', 'true');
        commit('setProjectInfo', {
          ...projectList[0],
          areaCode: projectList?.[0]?.addvcd,
          projectType: projectList?.[0]?.wrptycd
        });
      } else {
        commit('setIsSingleProject', 'false');
      }
    }
  },

  // 退出登录
  logout({ commit }) {
    commit('setToken', '');
    commit('setUserInfo', '');
    // 退出登录后，清除缓存(除用户名和密码)
    const username = getStorage('username');
    const password = getStorage('password');
    clearStorage();
    setStorage('username', username);
    setStorage('password', password);
    router.push({
      path: '/login'
    });
    // 调用退出登录接口
    return Promise.resolve({
      status: 200
    });
  },

  // 刷新token
  refreshToken({ commit, dispatch }) {
    return refresh({
      token: getStorage('refreshToken')
    })
      .then(res => {
        if (res.status === 200) {
          commit('setToken', res.data);
        } else {
          dispatch('logout');
          router.push({ path: '/login' });
        }
        return Promise.resolve(res);
      })
      .catch(e => {
        // 退出登录
        dispatch('logout');
        router.push({ path: '/login' });
      });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
