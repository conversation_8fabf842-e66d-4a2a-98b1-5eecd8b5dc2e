<template>
  <main class="main flex-column">
    <CustomNavBar />
    <van-tabs
      class="section flex-column"
      background="#266fe8"
      color="#fff"
      title-active-color="#fff"
      title-inactive-color="#ffffff61"
      v-model="activeTab"
      swipeable
      animated
    >
      <van-tab name="basic" title="基本信息">
        <BasicInfo class="flex-1" :data="basicinfo" :user-count="userList.length" />
      </van-tab>
      <van-tab name="user" title="人员信息">
        <UserInfo class="flex-1 user-tab" :data="userList" />
      </van-tab>
    </van-tabs>
  </main>
</template>

<script>
import BasicInfo from './basicInfo.vue';
import UserInfo from './userInfo.vue';
import { getDeptInfo, getUserList } from '@/api/patrolManage/patrolUnit/index.js';
export default {
  name: 'UnitInfo',
  components: {
    BasicInfo,
    UserInfo
  },
  props: {},
  data() {
    return {
      activeTab: 'basic',
      deptCode: null,
      basicinfo: {},
      userList: []
    };
  },
  computed: {},
  watch: {},
  created() {
    const param = this.$route.query;
    if (param.deptCode) {
      this.deptCode = param.deptCode;
      this.loadData();
    }
  },
  mounted() {},
  methods: {
    // 加载列表
    loadData() {
      this.getBasicInfo();
      this.getUserInfo();
    },
    getBasicInfo() {
      getDeptInfo({ deptCode: this.deptCode }).then(res => {
        if (res.status === 200 && res.data) {
          this.basicinfo = res.data;
        }
      });
    },
    getUserInfo() {
      getUserList({ deptCode: this.deptCode }).then(res => {
        if (res.status === 200 && res.data) {
          this.userList = res.data;
        } else {
          this.userList = [];
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  height: 100%;
  background: $bg-page-gray;
}
.section {
  height: calc(100% - 92px);
  background: linear-gradient(180deg, $color-primary 0%, $color-primary 70%, transparent 100%) no-repeat 0 0;
  background-size: 100% 546px;
  &::v-deep {
    .van-tabs__wrap::after {
      border: none;
    }
    .van-tabs__content {
      flex: 1;
      .van-tab__pane {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
    }
  }
  .user-tab {
    overflow: auto;
    overflow: overlay;
    background: $bg-page;
    -webkit-overflow-scrolling: touch;
  }
}
</style>
