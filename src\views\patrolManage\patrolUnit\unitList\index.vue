<template>
  <div class="section flex-column">
    <CustomSearch v-model="formData.deptName" :show-action="false" @search="onSearch" @cancel="showFilterPopup = true" />

    <van-pull-refresh ref="content" class="content flex-1" v-model="refresh" @refresh="onSearch">
      <van-list class="content-list" v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
        <van-cell
          v-for="unit in unitList"
          :key="unit.id"
          :title="unit.orgName"
          is-link
          :to="{ name: 'UnitInfo', query: { deptCode: unit.id } }"
        />
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script>
import CustomSearch from '@/components/CustomSearch';
import { getDeptList } from '@/api/patrolManage/patrolUnit/index.js';
export default {
  name: 'UnitList',
  components: {
    CustomSearch
  },
  props: {},
  data() {
    return {
      // keyword: '',
      // page: 1,
      // rows: 10,
      formData: {
        deptName: '',
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      unitList: [],
      refresh: false,
      loading: false, // 列表是否处于加载状态
      finished: true // 列表是否加载完成
    };
  },
  computed: {},
  watch: {},
  created() {
    this.loadData();
  },
  mounted() {},
  methods: {
    // 搜索事件
    onSearch() {
      this.formData.pageNum = 1;
      this.unitList = [];
      this.total = 0;
      this.loadData();
    },
    // 加载列表
    onLoad() {
      if (this.unitList.length >= this.total) {
        this.finished = true;
      } else {
        this.formData.pageNum += 1;
        this.loadData();
      }
    },
    loadData() {
      this.loading = true;
      getDeptList(this.formData).then(res => {
        if (res.status === 200 && res.data) {
          this.unitList.push(...res.data.list);
          this.total = res.data.total;
        }

        if (this.unitList.length >= this.total) {
          this.finished = true;
        } else {
          this.finished = false;
        }
        this.loading = false;
        this.refresh = false;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.section {
  height: 100%;
}
.content {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
</style>
