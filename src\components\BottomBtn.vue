<template>
  <div class="btn-container flex-vc j-sb van-hairline--top">
    <van-button v-if="showCancelBtn" class="btn cancel-btn" :round="true" plain @click="onCancel">{{ cancelText }}</van-button>
    <van-button v-bind="$attrs" class="btn confirm-btn" :type="type" :round="true" @click="onConfirm">
      {{ confirmText }}
    </van-button>
  </div>
</template>

<script>
export default {
  name: 'BottomBtn',
  components: {},
  props: {
    showCancelBtn: {
      type: Boolean,
      default: true
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    confirmText: {
      type: String,
      default: '提交'
    },
    type: {
      type: String,
      default: 'info',
      validator: value => {
        return ['default', 'primary', 'info', 'warning', 'danger'].includes(value);
      }
    }
  },
  data() {
    return {};
  },
  methods: {
    onCancel() {
      this.$emit('cancel');
    },
    onConfirm() {
      this.$emit('confirm');
    }
  },
  created() {},
  mounted() {}
};
</script>

<style lang="scss" scoped>
.btn-container {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  box-sizing: border-box;
  height: 145px;
  padding: 0 44px;
  background: $bg-page;
  .btn {
    flex: 1;
    height: 98px;
    font-size: 34px;
    font-weight: 500;
    & ~ .btn {
      margin-left: 20px;
    }
  }
  .cancel-btn {
    background: $bg-page-gray;
    border-color: $bg-page-gray;
  }
}
</style>
