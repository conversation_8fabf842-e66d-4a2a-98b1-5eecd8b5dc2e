<template>
  <!-- 新增巡检任务 -->
  <main class="main flex-column">
    <CustomNavBar @click-left="toPage({ name: 'TaskList' })" />
    <section class="section flex-1">
      <baidu-map
        ref="baiduMap"
        class="map"
        show-polyline
        show-marker
        :center="center"
        :polyline="polyline"
        @onMapReady="onMapReady"
      ></baidu-map>
      <!-- <CustomMap ref="mapRef" /> -->
      <div class="top-panel flex j-sb">
        <van-button class="btn" type="info" :block="true" @click="toNav">导航</van-button>
        <van-button class="btn" type="info" :block="true" @click="clockIn">打卡</van-button>
      </div>
    </section>

    <div class="bottom-panel">
      <div class="panel-detail">
        <div class="detail-info flex-vc">
          <div class="info-item flex-1 flex-column">
            <div class="info-value">{{ currentTime }}</div>
            <div>时间</div>
          </div>
          <div class="info-item flex-1 flex-column">
            <div class="info-value">{{ currentDistance }} km</div>
            <div>路程</div>
          </div>
        </div>
        <div class="detail-action flex-vc">
          <div class="action-item flex-1 flex-hvc" @click="toPage({ name: 'TaskRecord', taskId })">
            <img class="item-icon" src="@/assets/images/patrol/patrolTask/list.png" alt="" />
            <span>已提交情况</span>
          </div>
          <div class="action-item flex-1 flex-hvc" @click="toPage({ name: 'TaskReport', taskId })">
            <img class="item-icon" src="@/assets/images/patrol/patrolTask/edit.png" alt="" />
            <span>问题上报</span>
          </div>
        </div>
      </div>
      <p class="panel-note">注意：巡检过程中请勿关闭程序，否则会导致自动上报位置失效。</p>
      <div class="panel-btn">
        <van-button class="btn" type="danger" :block="true" :round="true" @click="submit">结束巡检</van-button>
      </div>
    </div>

    <van-action-sheet
      v-model:show="isShowOtherMap"
      title="使用地图打开"
      :actions="mapList"
      cancel-text="取消"
      description="如果点击无响应，可能是您还没有安装该APP"
      close-on-click-action
      @select="handleMapSelect"
    />
  </main>
</template>

<script>
import BaiduMap from '@/components/BaiduMap';
// import CustomMap from '@/components/CustomMap/index.vue';
import { getStnByTaskId, addRoute, editXjStatus } from '@/api/patrolManage/patrolTask/index.js';
import { getPhoneInfo } from '@/utils/plusPermission';
import { mapState } from 'vuex';
import { getCurrentPosition } from '@/utils/location';
import { addStn } from '@/api/patrolManage/patrolProblem/index.js';
import { checkEnv } from '@/utils/navigation';
import { checkApplication } from '@/utils/plusPermission';
import { getStorage } from '@/utils/storage';

export default {
  name: 'TaskOn',
  components: {
    BaiduMap
    // CustomMap
  },
  props: {},
  data() {
    return {
      isMapReady: false,
      taskId: null,
      fromPathName: '',
      isShowOtherMap: false
    };
  },
  computed: {
    ...mapState('patrol', {
      watchId: state => state.watchId,
      center: state => state.center,
      polyline: state => state.routeArray,
      coordinates: state => state.coordinates,
      currentTime: state => state.time,
      currentDistance: state => {
        if (state.distance >= 10) {
          return (state.distance / 1000).toFixed(2);
        }
        return 0;
      },
      patrolTask: state => state.patrolTask,
      patrolTaskKey: state => state.patrolTaskKey,
      patrolInfoKey: state => state.patrolInfoKey
    }),
    mapList() {
      return [
        {
          name: '百度地图' + (process.env.NODE_ENV === 'app' ? (checkApplication('com.baidu.BaiduMap') ? '' : '(未安装)') : ''),
          value: 'baidu'
        }
        // {
        //   name: '高德地图' + (process.env.NODE_ENV === 'app' ? (checkApplication('com.autonavi.minimap') ? '' : '(未安装)') : ''),
        //   value: 'gaode'
        // }
      ];
    }
  },
  watch: {
    currentDistance(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        if (!this.$refs.mapRef) {
          return;
        }
        this.setMarker();
        this.$refs.mapRef.toggleGeoJson({
          idf: 'patrolTrack',
          optType: 'showLayer',
          features: [
            {
              type: 'Feature',
              geometry: {
                type: 'LineString',
                coordinates: this.coordinates
              },
              properties: {}
            }
          ],
          style: {
            outlineColor: '#3E80ED',
            fillColor: '#3E80ED',
            outlineWidth: 5
          }
        });
        this.$refs.mapRef.toggleGeoJson({
          idf: 'patrolTrack',
          optType: 'flyToLayer'
        });
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.fromPathName = from.name;
    });
  },
  created() {
    const param = this.$route.query;
    this.taskId = +param.taskId;
    document.addEventListener(
      'backbutton',
      e => {
        e.preventDefault();
        this.toPage({ name: 'TaskList' });
      },
      false
    );
  },
  mounted() {
    // this.$nextTick(() => {
    //   if (this.fromPathName === 'TaskDetail') {
    //     this.$userApp.toast.show({
    //       text: `本次巡查任务已完成，\r\n即将跳转到首页`,
    //       type: 'text'
    //     });
    //     setTimeout(() => {
    //       this.toPage({ name: 'TaskList' });
    //     }, 800);
    //   } else {
    //     if (!this.watchId) {
    //       this.$store.dispatch('patrol/startPatrol', this.taskId);
    //       this.$store.commit('patrol/setPatrolTimer');
    //     }
    //   }
    // });
  },
  activated() {
    const param = this.$route.query;
    this.taskId = +param.taskId;
    this.$nextTick(() => {
      if (this.fromPathName === 'TaskDetail') {
        this.$userApp.toast.show({
          text: `本次巡查任务已完成，\r\n即将跳转到首页`,
          type: 'text'
        });
        setTimeout(() => {
          this.toPage({ name: 'TaskList' });
        }, 800);
      } else {
        if (!this.watchId) {
          this.$store.dispatch('patrol/startPatrol', this.taskId);
          this.$store.commit('patrol/setPatrolTimer');
        }
        if (this.$refs.mapRef) {
          this.setMarker();
        }
      }
    });
  },
  deactivated() {},
  methods: {
    init() {},
    onMapReady() {
      this.isMapReady = true;
    },
    setMarker() {
      if (this.center.lng) {
        const ptArr = [
          {
            lng: this.center.lng,
            lat: this.center.lat,
            Name: '',
            iconUrl: require('@/assets/images/index/marker.png'),
            scale: 0.5,
            isAnimate: false,
            labelZoom: 12,
            pointZoom: 8
          }
        ];
        this.$refs.mapRef.setArrPtLayerVisible({
          isVisible: true,
          layerName: 'patrolPoint',
          lngFieldName: 'lng',
          latFieldName: 'lat',
          labelFieldName: 'Name',
          ptArr
        });
      }
    },
    toPage(item) {
      const { name, ...rest } = item;
      if (name) {
        this.$router.push({
          name,
          query: { ...rest }
        });
      }
    },
    checkTask(callback) {
      getStnByTaskId({ taskId: this.taskId }).then(res => {
        if (res.status === 200 && res.data === 1) {
          callback(true);
        } else {
          callback(false);
          this.$userApp.toast.show({
            text: '请上报巡检情况',
            type: 'text'
          });
        }
      });
    },
    submit() {
      this.checkTask(valide => {
        if (valide) {
          addRoute({
            taskId: this.taskId,
            distance: this.currentDistance,
            routeId: this.patrolTask.xjRoute || this.patrolTask.xjName,
            route: this.polyline.length ? JSON.stringify(this.polyline) : null,
            coordinates: this.coordinates.length ? JSON.stringify(this.coordinates) : null,
            time: this.currentTime,
            weather: '',
            information: getPhoneInfo()
          }).then(res => {
            if (res.status === 200) {
              editXjStatus({
                taskId: this.taskId,
                xjStatus: 2
              }).then(r => {
                if (r.status === 200) {
                  this.$userApp.toast.show({
                    text: '提交成功',
                    type: 'text'
                  });
                  this.$store.commit('patrol/resetPatrol');
                  setTimeout(() => {
                    this.$router.push({
                      name: 'TaskDetail',
                      query: { taskId: this.taskId }
                    });
                  }, 800);
                }
              });
            }
          });
        }
      });
    },

    // 导航
    toNav() {
      this.isShowOtherMap = true;
    },
    handleMapSelect(mapData) {
      const patrolInfo = getStorage(this.patrolTaskKey);

      checkEnv(mapData.value, {
        lng: patrolInfo.lgtd || this.center.lng,
        lat: patrolInfo.lttd || this.center.lat,
        title: patrolInfo.xjName || '',
        address: ''
      });
      // const successFn = position => {
      //   const { longitude, latitude } = position.coords;
      //   const { city, district, street, streetNum } = position.address || {};
      //   const address = [city, district, street, streetNum].filter(i => i).join('');
      //   checkEnv(mapData.value, {
      //     lng: longitude,
      //     lat: latitude,

      //     address: address
      //   });
      // };
      // getCurrentPosition(successFn);
    },
    // 打卡
    async clockIn() {
      const successFn = position => {
        let data = {
          taskId: this.taskId,
          stnType: 1,
          lgtd: '',
          lttd: '',
          stnAddress: '',
          attachmentList: []
        };
        const { longitude, latitude } = position.coords;
        const { city, district, street, streetNum } = position.address || {};
        const address = [city, district, street, streetNum].filter(i => i).join('');
        data.lgtd = longitude;
        data.lttd = latitude;
        data.stnAddress = address;

        addStn(data).then(res => {
          if (res.status === 200) {
            this.$userApp.toast.show({
              text: '打卡成功！',
              type: 'text'
            });
          }
        });
      };

      getCurrentPosition(successFn);
    }
  }
};
</script>
<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100%;
}
.section {
  position: relative;
  padding-bottom: 132px;
  overflow: hidden;
  .map {
    width: 100%;
    height: 100%;
  }
}
.top-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  left: 20px;
  z-index: 9999;
  .btn {
    width: 180px;
    height: 70px;
  }
}
.bottom-panel {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  font-size: 28px;
  background: linear-gradient(to bottom, transparent 0%, $bg-page 60%);
  .panel-detail {
    box-sizing: border-box;
    padding: 40px 32px 36px;
    margin: 0 20px;
    margin-bottom: 20px;
    background: rgba($color-primary, 0.9);
    border-radius: 18px;
    .detail-info {
      margin-bottom: 40px;
      color: $color-text-white;
      .info-item {
        align-items: center;
        color: rgba($color-text-white, 0.8);
        .info-value {
          margin-bottom: 8px;
          font-size: 32px;
          font-weight: 500;
          line-height: 40px;
          color: $color-text-white;
        }
      }
    }
    .action-item {
      padding: 15px 0;
      font-weight: 500;
      color: $color-text-black;
      background: $bg-page;
      border-radius: 18px;
      &:first-child {
        margin-right: 24px;
      }
      .item-icon {
        width: 70px;
        height: 70px;
        margin-right: 8px;
      }
    }
  }
  .panel-note {
    padding: 0 46px;
    font-size: 20px;
    line-height: 28px;
    color: rgba($color-text-black, 0.6);
    text-align: center;
  }
  .panel-btn {
    box-sizing: border-box;
    padding: 16px 46px 24px;
    .btn {
      height: 98px;
      font-size: 34px;
      font-weight: 500;
    }
  }
}
</style>
