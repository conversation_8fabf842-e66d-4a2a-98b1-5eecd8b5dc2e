<template>
  <van-empty class="custom-image" :image="image" :description="description" v-bind="$attrs" />
</template>

<script>
export default {
  props: {
    description: {
      type: String,
      default: '暂无数据'
    },
    image: {
      default: () => require('@/assets/images/empty.png')
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .van-empty__image img {
  height: auto;
}
</style>
